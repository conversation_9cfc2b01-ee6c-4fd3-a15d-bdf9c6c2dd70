<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎使用Spring Boot</title>
    <style>
        /* 基础重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* 渐变背景 */
        body {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            min-height: 100vh;
            color: #fff;
        }

        /* 容器布局 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* 头部样式 */
        .header {
            text-align: center;
            padding: 3rem 0;
            animation: fadeIn 1s ease-in;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        /* 特性卡片 */
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.9);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            color: #333;
        }

        .card:hover {
            transform: translateY(-10px);
        }

        .card h3 {
            color: #4CAF50;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        /* 按钮样式 */
        .cta-button {
            display: inline-block;
            padding: 1rem 2rem;
            background: #fff;
            color: #4CAF50;
            text-decoration: none;
            border-radius: 30px;
            margin-top: 2rem;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .cta-button:hover {
            background: #45a049;
            color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <header class="header">
        <h1>🚀 Spring Boot 应用已启动！</h1>
        <p>版本 3.2.0 | 当前环境：开发模式</p>
        <a href="/docs" class="cta-button">查看API文档</a>
    </header>

    <div class="features">
        <div class="card">
            <h3>✔ 内置功能</h3>
            <ul>
                <li>嵌入式Tomcat服务器</li>
                <li>自动配置</li>
                <li>健康检查端点</li>
                <li>Actuator监控</li>
            </ul>
        </div>

        <div class="card">
            <h3>📚 技术栈</h3>
            <ul>
                <li>Spring Framework 6</li>
                <li>Java 17</li>
                <li>Maven构建工具</li>
                <li>Thymeleaf模板引擎</li>
            </ul>
        </div>

        <div class="card">
            <h3>📈 系统状态</h3>
            <ul>
                <li>内存使用：256MB/1024MB</li>
                <li>启动时间：2.34秒</li>
                <li>活跃会话：12</li>
                <li>响应时间：58ms</li>
            </ul>
        </div>
    </div>
</div>
</body>
</html>