<template>
  <el-dialog
    :title="'与 ' + toName + ' 聊天'"
    v-model="visible"
    width="600px"
    :before-close="handleClose"
  >
    <div class="chat-container">
      <!-- 聊天记录区域 -->
      <div class="chat-messages" ref="messageBox">
        <div
          v-for="(msg, index) in chatMessages"
          :key="index"
          :class="['message', msg.lname === currentUser ? 'message-right' : 'message-left']"
        >
          <!-- 添加头像 -->
          <div class="avatar" v-if="msg.lname !== currentUser">
            <img v-if="avatarUrls[msg.lname]" :src="avatarUrls[msg.lname]" alt="avatar" />
          </div>

          <div class="message-content">
            <div class="message-info">
              <span class="sender">{{ msg.lname }}</span>
              <span class="time">{{ msg.sendtime }}</span>
            </div>
            <div class="message-text">{{ msg.content }}</div>
          </div>

          <!-- 右侧消息的头像 -->
          <div class="avatar" v-if="msg.lname === currentUser">
            <img v-if="currentUserAvatar" :src="currentUserAvatar" alt="avatar" />
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <el-input
          v-model="messageContent"
          type="textarea"
          :rows="3"
          placeholder="请输入消息内容"
          @keyup.enter.native="sendMessage"
        ></el-input>
        <el-button type="primary" @click="sendMessage" :loading="sending">发送</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import request, { base } from '../../utils/http';

export default {
  name: 'ChatDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    toUser: {
      type: String,
      required: true,
    },
    toName: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      chatMessages: [],
      messageContent: '',
      currentUser: '',
      currentUserAvatar: '',
      toUserAvatar: '',
      sending: false,
      timer: null,
      avatarUrls: {}, // 存储所有用户的头像URL
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initChat();
        this.startPolling();
      } else {
        this.stopPolling();
      }
    },
  },
  methods: {
    // 初始化聊天
    async initChat() {
      this.currentUser = sessionStorage.getItem('lname');
      const sf = sessionStorage.getItem('sf');

      console.log('初始化聊天, 当前用户:', this.currentUser, '身份:', sf, '聊天对象:', this.toUser);

      if (!this.currentUser || !sf) {
        console.error('初始化聊天失败: 用户未登录或会话已过期');
        this.$message.error('请先登录');
        return;
      }

      if (!this.toUser) {
        console.error('初始化聊天失败: 未指定聊天对象');
        this.$message.error('未指定聊天对象');
        return;
      }

      // 获取当前用户头像
      try {
        if (sf === '家长') {
          console.log('获取家长头像:', this.currentUser);
          this.currentUserAvatar = await this.getUserAvatar(this.currentUser);
        } else {
          console.log('获取家教头像:', this.currentUser);
          this.currentUserAvatar = await this.getTutorAvatar(this.currentUser);
        }
        console.log('获取到的头像URL:', this.currentUserAvatar);
      } catch (error) {
        console.error('获取头像异常:', error);
      }

      this.getChatHistory();
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 加载所有用户头像
    async loadAvatars() {
      const sf = sessionStorage.getItem('sf');
      const promises = this.chatMessages.map(async (msg) => {
        if (msg.lname === this.currentUser) {
          this.avatarUrls[msg.lname] = this.currentUserAvatar;
        } else {
          if (sf === '家长') {
            this.avatarUrls[msg.lname] = await this.getTutorAvatar(msg.lname);
          } else {
            this.avatarUrls[msg.lname] = await this.getUserAvatar(msg.lname);
          }
        }
      });
      await Promise.all(promises);
    },

    // 获取家长头像
    async getUserAvatar(account) {
      try {
        const res = await request.get(base + '/users/get?id=' + account);
        if (res.code === 200 && res.resdata) {
          return res.resdata.avatar
            ? 'http://localhost:8088/TutoringServicePlatform/' + res.resdata.avatar
            : '';
        }
      } catch (error) {
        console.error('获取头像失败:', error);
        return '';
      }
      return '';
    },

    // 获取家教头像
    async getTutorAvatar(account) {
      try {
        const res = await request.get(base + '/tutors/get?id=' + account);
        if (res.code === 200 && res.resdata) {
          return res.resdata.photo
            ? 'http://localhost:8088/TutoringServicePlatform/' + res.resdata.photo
            : '';
        }
      } catch (error) {
        console.error('获取头像失败:', error);
        return '';
      }
      return '';
    },

    // 获取聊天记录
    getChatHistory() {
      if (!this.currentUser || !this.toUser) {
        console.error('获取聊天记录失败: 用户信息不完整',
          '当前用户:', this.currentUser, '聊天对象:', this.toUser);
        return;
      }

      console.log('开始获取聊天记录, 当前用户:', this.currentUser, '聊天对象:', this.toUser);

      let para = {
        condition:
          ' and ((lname="' +
          this.currentUser +
          '" and lname2="' +
          this.toUser +
          '") or (lname="' +
          this.toUser +
          '" and lname2="' +
          this.currentUser +
          '"))',
      };

      const url = base + '/api/chatinfo/list?currentPage=1&pageSize=1000&condition=' + encodeURIComponent(para.condition);
      console.log('获取聊天记录URL:', url);

      request.get(url).then(async (res) => {
        console.log('获取聊天记录响应:', res);

        if (res.code === 0 || res.code === 200) {
          this.chatMessages = res.resdata || [];
          console.log('获取到聊天记录数量:', this.chatMessages.length);

          this.chatMessages.sort((a, b) => {
            return new Date(a.sendtime) - new Date(b.sendtime);
          });

          await this.loadAvatars(); // 加载所有用户头像
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        } else {
          this.$message.error(res.msg || '获取聊天记录失败');
          console.error('获取聊天记录失败:', res);
        }
      }).catch(error => {
        console.error('获取聊天记录异常:', error);
        this.$message.error('获取聊天记录失败，请稍后重试');
      });
    },

    // 发送消息
    sendMessage() {
      if (!this.messageContent.trim()) {
        this.$message.warning('请输入消息内容');
        return;
      }

      // 检查当前用户和目标用户是否存在
      if (!this.currentUser) {
        this.$message.error('当前用户未登录或会话已过期');
        console.error('发送消息失败: 当前用户未登录或会话已过期');
        return;
      }

      if (!this.toUser) {
        this.$message.error('未指定聊天对象');
        console.error('发送消息失败: 未指定聊天对象');
        return;
      }

      this.sending = true;
      console.log('准备发送消息, 发送人:', this.currentUser, '接收人:', this.toUser);

      let para = {
        lname: this.currentUser,
        lname2: this.toUser,
        content: this.messageContent,
        sendtime: this.formatDate(new Date()),
      };

      console.log('发送消息参数:', para);
      console.log('发送消息URL:', base + '/api/chatinfo/add');

      request.post(base + '/api/chatinfo/add', para).then((res) => {
        this.sending = false;
        console.log('发送消息响应:', res);

        if (res.code === 0 || res.code === 200) {
          this.messageContent = '';
          this.$message.success('发送成功');
          this.getChatHistory();
        } else {
          this.$message.error(res.msg || '发送失败');
          console.error('发送消息失败:', res);
        }
      }).catch(error => {
        this.sending = false;
        console.error('发送消息异常:', error);
        this.$message.error('发送失败，请稍后重试');
      });
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 滚动到底部
    scrollToBottom() {
      const messageBox = this.$refs.messageBox;
      if (messageBox) {
        messageBox.scrollTop = messageBox.scrollHeight;
      }
    },

    // 开始轮询
    startPolling() {
      this.timer = setInterval(() => {
        this.getChatHistory();
      }, 3000);
    },

    // 停止轮询
    stopPolling() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    // 关闭对话框
    handleClose() {
      this.stopPolling();
      this.$emit('update:visible', false);
    },
  },
  beforeUnmount() {
    this.stopPolling();
  },
};
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.message {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.avatar {
  width: 40px;
  height: 40px;
  margin: 0 10px;
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message-left {
  justify-content: flex-start;
}

.message-right {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
  background: #fff;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.message-right .message-content {
  background: #409eff;
  color: #fff;
  order: -1; /* 让消息内容在头像左边 */
}

.message-info {
  font-size: 12px;
  margin-bottom: 5px;
  color: #909399;
}

.message-right .message-info {
  color: #fff;
  opacity: 0.8;
}

.sender {
  font-weight: bold;
  margin-right: 10px;
}

.message-text {
  word-break: break-all;
  line-height: 1.4;
}

.chat-input {
  padding: 20px 0 0;
}

.chat-input .el-button {
  margin-top: 10px;
  float: right;
}
</style>
