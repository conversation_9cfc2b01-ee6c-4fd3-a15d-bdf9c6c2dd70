<template>
  <header>
    <div class="header-top">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-6 d-none d-md-block">
            <div class="top-cta">
              <p><i class="ti-headphone-alt"></i> 咨询电话：010-66666666</p>
            </div>
          </div>
          <div class="col-md-6">
            <div class="header-top-right text-right">
              <ul>
                <li v-if="islogin">
                  <a href="#/Ureg"><i class="ti-user"></i>家长注册</a>
                </li>
                <li v-if="islogin"><a href="#/Ulogin">家长登录</a></li>
                <li v-if="islogin">
                  <a href="#/Treg">家教注册</a>
                </li>
                <li v-if="islogin"><a href="#/Tlogin">家教登录</a></li>
                <li v-if="islogin"><a href="">您暂未登录 请先登录！</a></li>
                <li v-else style="color: #fff">
                  欢迎您：<a href="javascript:void(0);" style="color: #fff">{{ lname }}</a>
                  &nbsp;|&nbsp;
                  <a href="javascript:void(0);" @click="exit" style="color: #fff">退出登录</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="header-sticky" class="menu-area pt-15 pb-15">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-2 col-md-3">
            <div class="logo-area">
              <a href="#/index"><img src="@/assets/img/logo/logo.png" alt="img" /></a>
            </div>
          </div>
          <div class="col-lg-8 col-md-6">
            <div class="main-menu text-right">
              <nav id="mobile-menu">
                <ul>
                  <li><a href="#/index">网站首页</a></li>
                  <li><a href="#/hometutorinfoList">家教资讯</a></li>
                  <li><a href="#/tutorsList">家教列表</a></li>
                  <li><a href="#/feedback">留言反馈</a></li>
                  <li><a href="#/Ai">AI答疑</a></li>
                  <li><a href="#/uweclome">个人中心</a></li>
                </ul>
              </nav>
            </div>
          </div>
          <div class="col-lg-2 col-md-3 d-none d-md-block">
            <!-- Modal Search -->
            <div
              class="modal fade"
              id="search-modal"
              tabindex="-1"
              好好好好好啊我听见有人喊我
              role="dialog"
              aria-hidden="true"
            >
              <div class="modal-dialog" role="document">
                <div class="modal-content">
                  <form33>
                    <input type="text" placeholder="搜索..." />
                    <button><i class="fa fa-search"></i></button>
                  </form33>
                </div>
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="mobile-menu"></div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>
<script>
import request, { base } from '../../utils/http';
import '../assets/css/qbootstrap.min.css';
import '../assets/css/qanimate.min.css';
import '../assets/css/qfontawesome-all.min.css';
import '../assets/css/qthemify-icons.css';
import '../assets/css/qmeanmenu.css';
import '../assets/css/qdefault.css';
import '../assets/css/qstyle.css';
export default {
  name: 'TopMenu',
  data() {
    return {
      islogin: true,
      lname: '',
      ishow: false,
      key: '',
    };
  },
  mounted() {
    this.lname = sessionStorage.getItem('lname');
    if (this.lname) {
      this.islogin = false;
    }
  },
  methods: {
    exit: function () {
      var _this = this;
      this.$confirm('确认退出吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          sessionStorage.removeItem('lname');
          _this.$router.push('/index');
        })
        .catch(() => {});
    },
  },
};
</script>

<style>
.header-top{
  .container{
    .align-items-center{
      margin-left: -200px;
      margin-right: -200px;
    }
  }
}
#header-sticky{
  .container{
    .align-items-center{
      margin-left: -80px;
      margin-right: 100px;
      font-weight: 700;
      li{
        margin-left: 45px;
      }
    }
  }
}
</style>
