<template>
  <header id="topnav">
    <nav class="navbar-custom">
      <ul class="list-unstyled topbar-right-menu float-right mb-0">
        <li class="dropdown notification-list">
          <a
            class="nav-link dropdown-toggle nav-user"
            data-toggle="dropdown"
            href="#"
            role="button"
            aria-haspopup="false"
            aria-expanded="false"
            @click="toggleShowExist"
          >
            <img src="../assets/images/users/avatar-1.jpg" alt="user" class="rounded-circle" />
            <span class="ml-1">
              【<b style="color: red">{{ role }}</b
              >】{{ userLname }}<i class="mdi mdi-chevron-down"></i>
            </span>
          </a>
          <div
            class="dropdown-menu dropdown-menu-right dropdown-menu-animated profile-dropdown"
            v-show="showexist"
          >
            <!-- item-->
            <div class="dropdown-item noti-title">
              <h6 class="text-overflow m-0">Welcome !</h6>
            </div>

            <a href="#/" class="dropdown-item notify-item" target="_blank">
              <i class="dripicons-home"></i> <span>网站首页</span>
            </a>

            <!-- item-->

            <router-link to="/Password" class="dropdown-item notify-item">
              <i class="dripicons-lock"></i> <span>修改密码</span>
            </router-link>

            <!-- item-->
            <a href="javascript:void(0);" class="dropdown-item notify-item" @click="exit">
              <i class="dripicons-power"></i> <span>退出登录</span>
            </a>
          </div>
        </li>
      </ul>

      <ul class="list-unstyled menu-left mb-0">
        <li class="float-left">
          <a href="index.html" class="logo">
            <span class="logo-lg" style="font-size: 20px; color: #fff">
              <img src="../assets/images/logo_sm.png" alt="" height="30" />
              家教服务平台
            </span>
            <span class="logo-sm">
              <img src="../assets/images/logo_sm.png" alt="" height="28" />
            </span>
          </a>
        </li>
      </ul>
    </nav>
    <!-- end navbar-custom -->
  </header>
</template>
<script>
import $ from 'jquery';
export default {
  data() {
    return {
      activeIndex: '1',
      activeIndex2: '1',
      showexist: false,
      userLname: '',
      role: '',
    };
  },
  mounted() {
    this.userLname = sessionStorage.getItem('userLname');
    this.role = sessionStorage.getItem('role');

    //判断是否登录
    if (this.userLname == null) {
      this.$router.push('/login');
    }
  },
  methods: {
    handleSelect(key, keyPath) {
      console.log(key, keyPath);
    },
    toggleShowExist() {
      this.showexist = !this.showexist;

      if (this.showexist) {
        $('.dropdown-menu').removeClass('show');
      } else {
        $('.dropdown-menu').addClass('show');
      }
    },

    exit: function () {
      var _this = this;
      this.$confirm('确认退出吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          sessionStorage.removeItem('userLname');
          sessionStorage.removeItem('role');
          _this.$router.push('/login');
        })
        .catch(() => {});
    },
    toggleFullScreen() {
      const elem = document.documentElement;
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if (elem.mozRequestFullScreen) {
        elem.mozRequestFullScreen();
      } else if (elem.webkitRequestFullscreen) {
        elem.webkitRequestFullscreen();
      } else if (elem.msRequestFullscreen) {
        elem.msRequestFullscreen();
      }
    },
  },
};
</script>
