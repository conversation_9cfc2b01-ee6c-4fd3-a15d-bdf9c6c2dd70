<template>
  <div class="col-lg-4 sm-mt sidebar-blog right-side">
    <div class="widget">
      <h4 class="widget-title">我的菜单</h4>
      <div class="widget-link">
        <ul class="sidebar-link" v-if="sf == '家长'">
          <li><a href="#/Uweclome">欢迎页面</a></li>

          <li><a href="#/orderinfo_manage">我的订单</a></li>
          <li><a href="#/feedback_manage">我的留言反馈</a></li>
          <li><a href="#/Uinfo">修改个人信息</a></li>
          <li><a href="#/Upassword">修改密码</a></li>
          <li><a @click="quit()" style="cursor: pointer">退出登录</a></li>
        </ul>

        <ul class="sidebar-link" v-if="sf == '家教'">
          <li><a href="#/Uweclome">欢迎页面</a></li>
          <li><a href="#/orderinfo_manage2">管理订单</a></li>
          <li><a href="#/chat_received">收到的聊天</a></li>
          <li><a href="#/feedback_manage">我的留言反馈</a></li>
          <li><a href="#/Tinfo">修改个人信息</a></li>
          <li><a href="#/Tpassword">修改密码</a></li>
          <li><a @click="quit()" style="cursor: pointer">退出登录</a></li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Menu',
  data() {
    return {
      sf: '', //用户类型
    };
  },
  mounted() {
    //判断是否登录
    var lname = sessionStorage.getItem('lname');
    this.sf = sessionStorage.getItem('sf');
    if (lname == null) {
      //弹出提示
      this.$message({
        message: '请先登录',
        type: 'warning',
        offset: 320,
      });
      //跳转到登录页面
      this.$router.push('/tlogin');
    }
  },
  methods: {
    quit() {
      var _this = this;
      this.$confirm('确认退出吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          sessionStorage.removeItem('lname');
          _this.$router.push('/tlogin');
        })
        .catch(() => {});
    },
  },
};
</script>

<style></style>
