<template>
  <div class="left-side-menu">
    <div class="slimscroll-menu">
      <!--- Sidemenu -->
      <div id="sidebar-menu">
        <ul class="metismenu" id="side-menu">
          <li class="menu-title">功能菜单</li>

          <li>
            <router-link to="/main"
              ><i class="mdi mdi-view-dashboard"></i><span>首页</span></router-link
            >
          </li>

          <li :class="{ active: activeMenu === 'nav3' }">
            <a href="javascript: void(0);" @click="toggleMenu('nav3')" aria-expanded="true">
              <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">家教类型管理</span>
              <span class="menu-arrow"></span>
            </a>
            <ul
              class="nav-second-level collapse"
              :class="{ in: activeMenu === 'nav3' }"
              aria-expanded="false"
            >
              <li><router-link to="/categorysAdd">添加家教类型</router-link></li>
              <li><router-link to="/categorysManage">管理家教类型</router-link></li>
            </ul>
          </li>

          <li :class="{ active: activeMenu === 'nav8' }">
            <a href="javascript: void(0);" @click="toggleMenu('nav8')" aria-expanded="true">
              <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">科目管理</span>
              <span class="menu-arrow"></span>
            </a>
            <ul
              class="nav-second-level collapse"
              :class="{ in: activeMenu === 'nav8' }"
              aria-expanded="false"
            >
              <li><router-link to="/subjectsAdd">添加科目</router-link></li>
              <li><router-link to="/subjectsManage">管理科目</router-link></li>
            </ul>
          </li>

          <li :class="{ active: activeMenu === 'nav2' }">
            <a href="javascript: void(0);" @click="toggleMenu('nav2')" aria-expanded="true">
              <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">家长管理</span>
              <span class="menu-arrow"></span>
            </a>
            <ul
              class="nav-second-level collapse"
              :class="{ in: activeMenu === 'nav2' }"
              aria-expanded="false"
            >
              <li><router-link to="/usersManage2">审核家长</router-link></li>
              <li><router-link to="/usersManage">管理家长</router-link></li>
              <li><router-link to="/usersManage3">审核不通过家长</router-link></li>
            </ul>
          </li>

          <li :class="{ active: activeMenu === 'nav1' }">
            <a href="javascript: void(0);" @click="toggleMenu('nav1')" aria-expanded="true">
              <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">家教管理</span>
              <span class="menu-arrow"></span>
            </a>
            <ul
              class="nav-second-level collapse"
              :class="{ in: activeMenu === 'nav1' }"
              aria-expanded="false"
            >
              <li><router-link to="/tutorsManage2">审核家教</router-link></li>
              <li><router-link to="/tutorsManage">管理家教</router-link></li>
              <li><router-link to="/tutorsManage3">审核不通过家教</router-link></li>
            </ul>
          </li>

          <li :class="{ active: activeMenu === 'nav6' }">
            <a href="javascript: void(0);" @click="toggleMenu('nav6')" aria-expanded="true">
              <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">家教资讯管理</span>
              <span class="menu-arrow"></span>
            </a>
            <ul
              class="nav-second-level collapse"
              :class="{ in: activeMenu === 'nav6' }"
              aria-expanded="false"
            >
              <li><router-link to="/hometutorinfoAdd">添加家教资讯</router-link></li>
              <li><router-link to="/hometutorinfoManage">管理家教资讯</router-link></li>
            </ul>
          </li>

          <li :class="{ active: activeMenu === 'nav9' }">
            <a href="javascript: void(0);" @click="toggleMenu('nav9')" aria-expanded="true">
              <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">订单管理</span>
              <span class="menu-arrow"></span>
            </a>
            <ul
              class="nav-second-level collapse"
              :class="{ in: activeMenu === 'nav9' }"
              aria-expanded="false"
            >
              <li><router-link to="/orderinfoManage">管理订单</router-link></li>
            </ul>
          </li>

          <li :class="{ active: activeMenu === 'nav7' }">
            <a href="javascript: void(0);" @click="toggleMenu('nav7')" aria-expanded="true">
              <i class="mdi mdi-chart-donut-variant"></i><span class="btitle">留言反馈管理</span>
              <span class="menu-arrow"></span>
            </a>
            <ul
              class="nav-second-level collapse"
              :class="{ in: activeMenu === 'nav7' }"
              aria-expanded="false"
            >
              <li><router-link to="/feedbackManage">管理留言反馈</router-link></li>
            </ul>
          </li>

          <li :class="{ active: activeMenu === 'nav22' }">
            <a href="javascript: void(0);" @click="toggleMenu('nav22')">
              <i class="mdi mdi-chart-donut-variant"></i><span> 系统管理 </span>
              <span class="menu-arrow"></span>
            </a>
            <ul
              class="nav-second-level collapse"
              :class="{ in: activeMenu === 'nav22' }"
              aria-expanded="false"
            >
              <li v-if="role == '超级管理员'">
                <router-link to="/adminAdd">添加管理员</router-link>
              </li>
              <li v-if="role == '超级管理员'">
                <router-link to="/adminManage">管理管理员</router-link>
              </li>
              <li><router-link to="password">修改密码</router-link></li>
            </ul>
          </li>
        </ul>
      </div>
      <!-- Sidebar -->

      <div class="clearfix"></div>
    </div>
    <!-- Sidebar -left -->
  </div>
</template>

<script>
import $ from 'jquery';

export default {
  name: 'LeftMenu',
  data() {
    return {
      userLname: '',
      role: '',
      activeMenu: null, // 用于跟踪当前激活的菜单
    };
  },
  mounted() {
    this.userLname = sessionStorage.getItem('userLname');
    this.role = sessionStorage.getItem('role');
  },
  methods: {
    toggleMenu(menu) {
      this.activeMenu = this.activeMenu === menu ? null : menu;
    },
    exit: function () {
      var _this = this;
      this.$confirm('确认退出吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          sessionStorage.removeItem('userLname');
          sessionStorage.removeItem('role');
          _this.$router.push('/login');
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.example-showcase .el-dropdown-link {
  cursor: pointer;
  color: green;
  display: flex;
  align-items: center;
}

.pcoded-submenu a {
  text-decoration: none;
  font-size: 14px;
}

/*加点击效果*/
.pcoded-submenu a:hover {
  color: #fff;
  color: #ff6600;
}
</style>
