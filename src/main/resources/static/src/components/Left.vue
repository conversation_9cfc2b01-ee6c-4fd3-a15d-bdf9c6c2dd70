<template>
  <div class="col-lg-4 sm-mt sidebar-blog right-side">
    <div class="widget">
      <h4 class="widget-title">最新家教</h4>
      <div class="sidebar-rc-post">
        <div class="tutor-grid">
          <div class="tutor-item" v-for="(item, index) in list2" :key="index">
            <a :href="'#tutorsView?id=' + item.taccount" class="tutor-link">
              <div class="tutor-image">
                <img :src="'http://localhost:8088/TutoringServicePlatform/' + item.photo" />
              </div>
              <div class="tutor-info">
                <div class="tutor-name">{{ item.tuname }}</div>
                <div class="tutor-price">￥{{ item.price }}/时</div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import request, { base } from '../../utils/http';
export default {
  name: 'Left',
  data() {
    return {
      list1: '',
      list2: '',
    };
  },
  mounted() {
    this.getlist2();
  },
  methods: {
    // 获取最新家教
    getlist2() {
      let para = {
        tflag: '审核通过',
      };
      this.listLoading = true;
      let url = base + '/tutors/list?currentPage=1&pageSize=5';
      request.post(url, para).then((res) => {
        this.list2 = res.resdata;
        this.listLoading = false;
      });
    },
  },
};
</script>

<style scoped>
.tutor-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 10px;
}

.tutor-item {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.tutor-item:hover {
  transform: translateY(-3px);
}

.tutor-link {
  text-decoration: none;
  color: inherit;
}

.tutor-image {
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.tutor-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.tutor-item:hover .tutor-image img {
  transform: scale(1.05);
}

.tutor-info {
  padding: 10px;
  text-align: center;
}

.tutor-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tutor-price {
  font-size: 14px;
  color: #f56c6c;
  font-weight: bold;
}

/* 响应式布局 */
@media screen and (max-width: 576px) {
  .tutor-grid {
    grid-template-columns: 1fr;
  }
}
</style>
