<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Dripicons - V2 (Line-Icon Font) by <PERSON>it <PERSON></title>
    <meta name="description" content="A completely vector line-icon font by Amit <PERSON>.">
    <meta name="keywords" content="Amit J<PERSON>hu,Dripicons,Icons,Iconset,Free Icons,Line Icons,Linicons,V2,Version 2,Icon,Toronto,Canada,Dribbble,Behance,Download,Resource,Designerfuel,Design,UI,User Interface,UX,User Experience">
    <meta itemprop="name" content="Dripicons V2 (Free Iconset)">
    <meta itemprop="description" content="A completely vector line-icon font by Amit J<PERSON>.">
    <meta itemprop="image" content="http://demo.amitjakhu.com/dripicons/img/dripicons.png">

    <meta property="og:title" content="Dripicons V2 (Free Iconset)" />
    <meta property="og:type" content="product" />
    <meta property="og:url" content="http://demo.amitjakhu.com/dripicons/" />
    <meta property="og:image" content="http://demo.amitjakhu.com/dripicons/img/dripicons.png" />
    <meta property="og:description" content="A completely free vector line-icon font by Amit Jakhu." />
    <meta property="og:site_name" content="Dripicons V2 (Free Iconset)" />

    <link href="http://fonts.googleapis.com/css?family=Dosis:400,500,700" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="webfont.css">
  </head>
  <body>
      <script>(function(d, s, id) {
          var js, fjs = d.getElementsByTagName(s)[0];
          if (d.getElementById(id)) return;
          js = d.createElement(s); js.id = id;
          js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=272937136072184";
          fjs.parentNode.insertBefore(js, fjs);

        }(document, 'script', 'facebook-jssdk'));</script>
    <div class="container">
      <h4>Version 2</h4>
      <h1>Dripicons</h1>
      <p class="small">A completely free vector line-icon font by <a href="http://amitjakhu.com">Amit Jakhu</a>. Version 2.0</p>
          <ul class="share">
                <li><div class="fb-like" data-width="The pixel width of the plugin" data-height="The pixel height of the plugin" data-colorscheme="light" data-layout="button_count" data-action="like" data-show-faces="false" data-send="false"></div></li>
                <li><a href="https://twitter.com/share" class="twitter-share-button" data-via="amitjakhu">Tweet</a><script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');</script></li>
            </ul>
      <h2>Character mapping</h2>
      <ul class="glyphs character-mapping">
        <li>
          <div data-icon="a" class="icon"></div>
          <input type="text" readonly="readonly" value="a">
        </li>
        <li>
          <div data-icon="b" class="icon"></div>
          <input type="text" readonly="readonly" value="b">
        </li>
        <li>
          <div data-icon="c" class="icon"></div>
          <input type="text" readonly="readonly" value="c">
        </li>
        <li>
          <div data-icon="d" class="icon"></div>
          <input type="text" readonly="readonly" value="d">
        </li>
        <li>
          <div data-icon="e" class="icon"></div>
          <input type="text" readonly="readonly" value="e">
        </li>
        <li>
          <div data-icon="f" class="icon"></div>
          <input type="text" readonly="readonly" value="f">
        </li>
        <li>
          <div data-icon="g" class="icon"></div>
          <input type="text" readonly="readonly" value="g">
        </li>
        <li>
          <div data-icon="h" class="icon"></div>
          <input type="text" readonly="readonly" value="h">
        </li>
        <li>
          <div data-icon="i" class="icon"></div>
          <input type="text" readonly="readonly" value="i">
        </li>
        <li>
          <div data-icon="j" class="icon"></div>
          <input type="text" readonly="readonly" value="j">
        </li>
        <li>
          <div data-icon="k" class="icon"></div>
          <input type="text" readonly="readonly" value="k">
        </li>
        <li>
          <div data-icon="l" class="icon"></div>
          <input type="text" readonly="readonly" value="l">
        </li>
        <li>
          <div data-icon="m" class="icon"></div>
          <input type="text" readonly="readonly" value="m">
        </li>
        <li>
          <div data-icon="n" class="icon"></div>
          <input type="text" readonly="readonly" value="n">
        </li>
        <li>
          <div data-icon="o" class="icon"></div>
          <input type="text" readonly="readonly" value="o">
        </li>
        <li>
          <div data-icon="p" class="icon"></div>
          <input type="text" readonly="readonly" value="p">
        </li>
        <li>
          <div data-icon="q" class="icon"></div>
          <input type="text" readonly="readonly" value="q">
        </li>
        <li>
          <div data-icon="r" class="icon"></div>
          <input type="text" readonly="readonly" value="r">
        </li>
        <li>
          <div data-icon="s" class="icon"></div>
          <input type="text" readonly="readonly" value="s">
        </li>
        <li>
          <div data-icon="t" class="icon"></div>
          <input type="text" readonly="readonly" value="t">
        </li>
        <li>
          <div data-icon="u" class="icon"></div>
          <input type="text" readonly="readonly" value="u">
        </li>
        <li>
          <div data-icon="v" class="icon"></div>
          <input type="text" readonly="readonly" value="v">
        </li>
        <li>
          <div data-icon="w" class="icon"></div>
          <input type="text" readonly="readonly" value="w">
        </li>
        <li>
          <div data-icon="x" class="icon"></div>
          <input type="text" readonly="readonly" value="x">
        </li>
        <li>
          <div data-icon="y" class="icon"></div>
          <input type="text" readonly="readonly" value="y">
        </li>
        <li>
          <div data-icon="z" class="icon"></div>
          <input type="text" readonly="readonly" value="z">
        </li>
        <li>
          <div data-icon="A" class="icon"></div>
          <input type="text" readonly="readonly" value="A">
        </li>
        <li>
          <div data-icon="B" class="icon"></div>
          <input type="text" readonly="readonly" value="B">
        </li>
        <li>
          <div data-icon="C" class="icon"></div>
          <input type="text" readonly="readonly" value="C">
        </li>
        <li>
          <div data-icon="D" class="icon"></div>
          <input type="text" readonly="readonly" value="D">
        </li>
        <li>
          <div data-icon="E" class="icon"></div>
          <input type="text" readonly="readonly" value="E">
        </li>
        <li>
          <div data-icon="F" class="icon"></div>
          <input type="text" readonly="readonly" value="F">
        </li>
        <li>
          <div data-icon="G" class="icon"></div>
          <input type="text" readonly="readonly" value="G">
        </li>
        <li>
          <div data-icon="H" class="icon"></div>
          <input type="text" readonly="readonly" value="H">
        </li>
        <li>
          <div data-icon="I" class="icon"></div>
          <input type="text" readonly="readonly" value="I">
        </li>
        <li>
          <div data-icon="J" class="icon"></div>
          <input type="text" readonly="readonly" value="J">
        </li>
        <li>
          <div data-icon="K" class="icon"></div>
          <input type="text" readonly="readonly" value="K">
        </li>
        <li>
          <div data-icon="L" class="icon"></div>
          <input type="text" readonly="readonly" value="L">
        </li>
        <li>
          <div data-icon="M" class="icon"></div>
          <input type="text" readonly="readonly" value="M">
        </li>
        <li>
          <div data-icon="N" class="icon"></div>
          <input type="text" readonly="readonly" value="N">
        </li>
        <li>
          <div data-icon="O" class="icon"></div>
          <input type="text" readonly="readonly" value="O">
        </li>
        <li>
          <div data-icon="P" class="icon"></div>
          <input type="text" readonly="readonly" value="P">
        </li>
        <li>
          <div data-icon="Q" class="icon"></div>
          <input type="text" readonly="readonly" value="Q">
        </li>
        <li>
          <div data-icon="R" class="icon"></div>
          <input type="text" readonly="readonly" value="R">
        </li>
        <li>
          <div data-icon="S" class="icon"></div>
          <input type="text" readonly="readonly" value="S">
        </li>
        <li>
          <div data-icon="T" class="icon"></div>
          <input type="text" readonly="readonly" value="T">
        </li>
        <li>
          <div data-icon="U" class="icon"></div>
          <input type="text" readonly="readonly" value="U">
        </li>
        <li>
          <div data-icon="V" class="icon"></div>
          <input type="text" readonly="readonly" value="V">
        </li>
        <li>
          <div data-icon="W" class="icon"></div>
          <input type="text" readonly="readonly" value="W">
        </li>
        <li>
          <div data-icon="X" class="icon"></div>
          <input type="text" readonly="readonly" value="X">
        </li>
        <li>
          <div data-icon="Y" class="icon"></div>
          <input type="text" readonly="readonly" value="Y">
        </li>
        <li>
          <div data-icon="Z" class="icon"></div>
          <input type="text" readonly="readonly" value="Z">
        </li>
        <li>
          <div data-icon="0" class="icon"></div>
          <input type="text" readonly="readonly" value="0">
        </li>
        <li>
          <div data-icon="1" class="icon"></div>
          <input type="text" readonly="readonly" value="1">
        </li>
        <li>
          <div data-icon="2" class="icon"></div>
          <input type="text" readonly="readonly" value="2">
        </li>
        <li>
          <div data-icon="3" class="icon"></div>
          <input type="text" readonly="readonly" value="3">
        </li>
        <li>
          <div data-icon="4" class="icon"></div>
          <input type="text" readonly="readonly" value="4">
        </li>
        <li>
          <div data-icon="5" class="icon"></div>
          <input type="text" readonly="readonly" value="5">
        </li>
        <li>
          <div data-icon="6" class="icon"></div>
          <input type="text" readonly="readonly" value="6">
        </li>
        <li>
          <div data-icon="7" class="icon"></div>
          <input type="text" readonly="readonly" value="7">
        </li>
        <li>
          <div data-icon="8" class="icon"></div>
          <input type="text" readonly="readonly" value="8">
        </li>
        <li>
          <div data-icon="9" class="icon"></div>
          <input type="text" readonly="readonly" value="9">
        </li>
        <li>
          <div data-icon="!" class="icon"></div>
          <input type="text" readonly="readonly" value="!">
        </li>
        <li>
          <div data-icon="&#34;" class="icon"></div>
          <input type="text" readonly="readonly" value="&quot;">
        </li>
        <li>
          <div data-icon="#" class="icon"></div>
          <input type="text" readonly="readonly" value="#">
        </li>
        <li>
          <div data-icon="$" class="icon"></div>
          <input type="text" readonly="readonly" value="$">
        </li>
        <li>
          <div data-icon="%" class="icon"></div>
          <input type="text" readonly="readonly" value="%">
        </li>
        <li>
          <div data-icon="&" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;">
        </li>
        <li>
          <div data-icon="'" class="icon"></div>
          <input type="text" readonly="readonly" value="&#39;">
        </li>
        <li>
          <div data-icon="(" class="icon"></div>
          <input type="text" readonly="readonly" value="(">
        </li>
        <li>
          <div data-icon=")" class="icon"></div>
          <input type="text" readonly="readonly" value=")">
        </li>
        <li>
          <div data-icon="*" class="icon"></div>
          <input type="text" readonly="readonly" value="*">
        </li>
        <li>
          <div data-icon="+" class="icon"></div>
          <input type="text" readonly="readonly" value="+">
        </li>
        <li>
          <div data-icon="," class="icon"></div>
          <input type="text" readonly="readonly" value=",">
        </li>
        <li>
          <div data-icon="-" class="icon"></div>
          <input type="text" readonly="readonly" value="-">
        </li>
        <li>
          <div data-icon="." class="icon"></div>
          <input type="text" readonly="readonly" value=".">
        </li>
        <li>
          <div data-icon="/" class="icon"></div>
          <input type="text" readonly="readonly" value="/">
        </li>
        <li>
          <div data-icon=":" class="icon"></div>
          <input type="text" readonly="readonly" value=":">
        </li>
        <li>
          <div data-icon=";" class="icon"></div>
          <input type="text" readonly="readonly" value=";">
        </li>
        <li>
          <div data-icon="<" class="icon"></div>
          <input type="text" readonly="readonly" value="&lt;">
        </li>
        <li>
          <div data-icon="=" class="icon"></div>
          <input type="text" readonly="readonly" value="=">
        </li>
        <li>
          <div data-icon=">" class="icon"></div>
          <input type="text" readonly="readonly" value="&gt;">
        </li>
        <li>
          <div data-icon="?" class="icon"></div>
          <input type="text" readonly="readonly" value="?">
        </li>
        <li>
          <div data-icon="@" class="icon"></div>
          <input type="text" readonly="readonly" value="@">
        </li>
        <li>
          <div data-icon="[" class="icon"></div>
          <input type="text" readonly="readonly" value="[">
        </li>
        <li>
          <div data-icon="]" class="icon"></div>
          <input type="text" readonly="readonly" value="]">
        </li>
        <li>
          <div data-icon="^" class="icon"></div>
          <input type="text" readonly="readonly" value="^">
        </li>
        <li>
          <div data-icon="_" class="icon"></div>
          <input type="text" readonly="readonly" value="_">
        </li>
        <li>
          <div data-icon="`" class="icon"></div>
          <input type="text" readonly="readonly" value="`">
        </li>
        <li>
          <div data-icon="{" class="icon"></div>
          <input type="text" readonly="readonly" value="{">
        </li>
        <li>
          <div data-icon="|" class="icon"></div>
          <input type="text" readonly="readonly" value="|">
        </li>
        <li>
          <div data-icon="}" class="icon"></div>
          <input type="text" readonly="readonly" value="}">
        </li>
        <li>
          <div data-icon="~" class="icon"></div>
          <input type="text" readonly="readonly" value="~">
        </li>
        <li>
          <div data-icon="\" class="icon"></div>
          <input type="text" readonly="readonly" value="\">
        </li>
        <li>
          <div data-icon="&#xe000;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe000;">
        </li>
        <li>
          <div data-icon="&#xe001;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe001;">
        </li>
        <li>
          <div data-icon="&#xe002;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe002;">
        </li>
        <li>
          <div data-icon="&#xe003;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe003;">
        </li>
        <li>
          <div data-icon="&#xe004;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe004;">
        </li>
        <li>
          <div data-icon="&#xe005;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe005;">
        </li>
        <li>
          <div data-icon="&#xe006;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe006;">
        </li>
        <li>
          <div data-icon="&#xe007;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe007;">
        </li>
        <li>
          <div data-icon="&#xe008;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe008;">
        </li>
        <li>
          <div data-icon="&#xe009;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe009;">
        </li>
        <li>
          <div data-icon="&#xe00a;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe00a;">
        </li>
        <li>
          <div data-icon="&#xe00b;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe00b;">
        </li>
        <li>
          <div data-icon="&#xe00c;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe00c;">
        </li>
        <li>
          <div data-icon="&#xe00d;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe00d;">
        </li>
        <li>
          <div data-icon="&#xe00e;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe00e;">
        </li>
        <li>
          <div data-icon="&#xe00f;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe00f;">
        </li>
        <li>
          <div data-icon="&#xe010;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe010;">
        </li>
        <li>
          <div data-icon="&#xe011;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe011;">
        </li>
        <li>
          <div data-icon="&#xe012;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe012;">
        </li>
        <li>
          <div data-icon="&#xe013;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe013;">
        </li>
        <li>
          <div data-icon="&#xe014;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe014;">
        </li>
        <li>
          <div data-icon="&#xe015;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe015;">
        </li>
        <li>
          <div data-icon="&#xe016;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe016;">
        </li>
        <li>
          <div data-icon="&#xe017;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe017;">
        </li>
        <li>
          <div data-icon="&#xe018;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe018;">
        </li>
        <li>
          <div data-icon="&#xe019;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe019;">
        </li>
        <li>
          <div data-icon="&#xe01a;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe01a;">
        </li>
        <li>
          <div data-icon="&#xe01b;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe01b;">
        </li>
        <li>
          <div data-icon="&#xe01c;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe01c;">
        </li>
        <li>
          <div data-icon="&#xe01d;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe01d;">
        </li>
        <li>
          <div data-icon="&#xe01e;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe01e;">
        </li>
        <li>
          <div data-icon="&#xe01f;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe01f;">
        </li>
        <li>
          <div data-icon="&#xe020;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe020;">
        </li>
        <li>
          <div data-icon="&#xe021;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe021;">
        </li>
        <li>
          <div data-icon="&#xe022;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe022;">
        </li>
        <li>
          <div data-icon="&#xe023;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe023;">
        </li>
        <li>
          <div data-icon="&#xe024;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe024;">
        </li>
        <li>
          <div data-icon="&#xe025;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe025;">
        </li>
        <li>
          <div data-icon="&#xe026;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe026;">
        </li>
        <li>
          <div data-icon="&#xe027;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe027;">
        </li>
        <li>
          <div data-icon="&#xe028;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe028;">
        </li>
        <li>
          <div data-icon="&#xe029;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe029;">
        </li>
        <li>
          <div data-icon="&#xe02a;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe02a;">
        </li>
        <li>
          <div data-icon="&#xe02b;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe02b;">
        </li>
        <li>
          <div data-icon="&#xe02c;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe02c;">
        </li>
        <li>
          <div data-icon="&#xe02d;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe02d;">
        </li>
        <li>
          <div data-icon="&#xe02e;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe02e;">
        </li>
        <li>
          <div data-icon="&#xe02f;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe02f;">
        </li>
        <li>
          <div data-icon="&#xe030;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe030;">
        </li>
        <li>
          <div data-icon="&#xe031;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe031;">
        </li>
        <li>
          <div data-icon="&#xe032;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe032;">
        </li>
        <li>
          <div data-icon="&#xe033;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe033;">
        </li>
        <li>
          <div data-icon="&#xe034;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe034;">
        </li>
        <li>
          <div data-icon="&#xe035;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe035;">
        </li>
        <li>
          <div data-icon="&#xe036;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe036;">
        </li>
        <li>
          <div data-icon="&#xe037;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe037;">
        </li>
        <li>
          <div data-icon="&#xe038;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe038;">
        </li>
        <li>
          <div data-icon="&#xe039;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe039;">
        </li>
        <li>
          <div data-icon="&#xe03a;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe03a;">
        </li>
        <li>
          <div data-icon="&#xe03b;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe03b;">
        </li>
        <li>
          <div data-icon="&#xe03c;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe03c;">
        </li>
        <li>
          <div data-icon="&#xe03d;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe03d;">
        </li>
        <li>
          <div data-icon="&#xe03e;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe03e;">
        </li>
        <li>
          <div data-icon="&#xe03f;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe03f;">
        </li>
        <li>
          <div data-icon="&#xe040;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe040;">
        </li>
        <li>
          <div data-icon="&#xe041;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe041;">
        </li>
        <li>
          <div data-icon="&#xe042;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe042;">
        </li>
        <li>
          <div data-icon="&#xe043;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe043;">
        </li>
        <li>
          <div data-icon="&#xe044;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe044;">
        </li>
        <li>
          <div data-icon="&#xe045;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe045;">
        </li>
        <li>
          <div data-icon="&#xe046;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe046;">
        </li>
        <li>
          <div data-icon="&#xe047;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe047;">
        </li>
        <li>
          <div data-icon="&#xe048;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe048;">
        </li>
        <li>
          <div data-icon="&#xe049;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe049;">
        </li>
        <li>
          <div data-icon="&#xe04a;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe04a;">
        </li>
        <li>
          <div data-icon="&#xe04b;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe04b;">
        </li>
        <li>
          <div data-icon="&#xe04c;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe04c;">
        </li>
        <li>
          <div data-icon="&#xe04d;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe04d;">
        </li>
        <li>
          <div data-icon="&#xe04e;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe04e;">
        </li>
        <li>
          <div data-icon="&#xe04f;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe04f;">
        </li>
        <li>
          <div data-icon="&#xe050;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe050;">
        </li>
        <li>
          <div data-icon="&#xe051;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe051;">
        </li>
        <li>
          <div data-icon="&#xe052;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe052;">
        </li>
        <li>
          <div data-icon="&#xe053;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe053;">
        </li>
        <li>
          <div data-icon="&#xe054;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe054;">
        </li>
        <li>
          <div data-icon="&#xe055;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe055;">
        </li>
        <li>
          <div data-icon="&#xe056;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe056;">
        </li>
        <li>
          <div data-icon="&#xe057;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe057;">
        </li>
        <li>
          <div data-icon="&#xe058;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe058;">
        </li>
        <li>
          <div data-icon="&#xe059;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe059;">
        </li>
        <li>
          <div data-icon="&#xe05a;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe05a;">
        </li>
        <li>
          <div data-icon="&#xe05b;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe05b;">
        </li>
        <li>
          <div data-icon="&#xe05c;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe05c;">
        </li>
        <li>
          <div data-icon="&#xe05d;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe05d;">
        </li>
        <li>
          <div data-icon="&#xe05e;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe05e;">
        </li>
        <li>
          <div data-icon="&#xe05f;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe05f;">
        </li>
        <li>
          <div data-icon="&#xe060;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe060;">
        </li>
        <li>
          <div data-icon="&#xe061;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe061;">
        </li>
        <li>
          <div data-icon="&#xe062;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe062;">
        </li>
        <li>
          <div data-icon="&#xe063;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe063;">
        </li>
        <li>
          <div data-icon="&#xe064;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe064;">
        </li>
        <li>
          <div data-icon="&#xe065;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe065;">
        </li>
        <li>
          <div data-icon="&#xe066;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe066;">
        </li>
        <li>
          <div data-icon="&#xe067;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe067;">
        </li>
        <li>
          <div data-icon="&#xe068;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe068;">
        </li>
        <li>
          <div data-icon="&#xe069;" class="icon"></div>
          <input type="text" readonly="readonly" value="&amp;#xe069;">
        </li>
      </ul>
      <h2>CSS mapping</h2>
      <ul class="glyphs css-mapping">
        <li>
          <div class="icon dripicons-alarm"></div>
          <input type="text" readonly="readonly" value="alarm">
        </li>
        <li>
          <div class="icon dripicons-align-center"></div>
          <input type="text" readonly="readonly" value="align-center">
        </li>
        <li>
          <div class="icon dripicons-align-justify"></div>
          <input type="text" readonly="readonly" value="align-justify">
        </li>
        <li>
          <div class="icon dripicons-align-left"></div>
          <input type="text" readonly="readonly" value="align-left">
        </li>
        <li>
          <div class="icon dripicons-align-right"></div>
          <input type="text" readonly="readonly" value="align-right">
        </li>
        <li>
          <div class="icon dripicons-anchor"></div>
          <input type="text" readonly="readonly" value="anchor">
        </li>
        <li>
          <div class="icon dripicons-archive"></div>
          <input type="text" readonly="readonly" value="archive">
        </li>
        <li>
          <div class="icon dripicons-arrow-down"></div>
          <input type="text" readonly="readonly" value="arrow-down">
        </li>
        <li>
          <div class="icon dripicons-arrow-left"></div>
          <input type="text" readonly="readonly" value="arrow-left">
        </li>
        <li>
          <div class="icon dripicons-arrow-right"></div>
          <input type="text" readonly="readonly" value="arrow-right">
        </li>
        <li>
          <div class="icon dripicons-arrow-thin-down"></div>
          <input type="text" readonly="readonly" value="arrow-thin-down">
        </li>
        <li>
          <div class="icon dripicons-arrow-thin-left"></div>
          <input type="text" readonly="readonly" value="arrow-thin-left">
        </li>
        <li>
          <div class="icon dripicons-arrow-thin-right"></div>
          <input type="text" readonly="readonly" value="arrow-thin-right">
        </li>
        <li>
          <div class="icon dripicons-arrow-thin-up"></div>
          <input type="text" readonly="readonly" value="arrow-thin-up">
        </li>
        <li>
          <div class="icon dripicons-arrow-up"></div>
          <input type="text" readonly="readonly" value="arrow-up">
        </li>
        <li>
          <div class="icon dripicons-article"></div>
          <input type="text" readonly="readonly" value="article">
        </li>
        <li>
          <div class="icon dripicons-backspace"></div>
          <input type="text" readonly="readonly" value="backspace">
        </li>
        <li>
          <div class="icon dripicons-basket"></div>
          <input type="text" readonly="readonly" value="basket">
        </li>
        <li>
          <div class="icon dripicons-basketball"></div>
          <input type="text" readonly="readonly" value="basketball">
        </li>
        <li>
          <div class="icon dripicons-battery-empty"></div>
          <input type="text" readonly="readonly" value="battery-empty">
        </li>
        <li>
          <div class="icon dripicons-battery-full"></div>
          <input type="text" readonly="readonly" value="battery-full">
        </li>
        <li>
          <div class="icon dripicons-battery-low"></div>
          <input type="text" readonly="readonly" value="battery-low">
        </li>
        <li>
          <div class="icon dripicons-battery-medium"></div>
          <input type="text" readonly="readonly" value="battery-medium">
        </li>
        <li>
          <div class="icon dripicons-bell"></div>
          <input type="text" readonly="readonly" value="bell">
        </li>
        <li>
          <div class="icon dripicons-blog"></div>
          <input type="text" readonly="readonly" value="blog">
        </li>
        <li>
          <div class="icon dripicons-bluetooth"></div>
          <input type="text" readonly="readonly" value="bluetooth">
        </li>
        <li>
          <div class="icon dripicons-bold"></div>
          <input type="text" readonly="readonly" value="bold">
        </li>
        <li>
          <div class="icon dripicons-bookmark"></div>
          <input type="text" readonly="readonly" value="bookmark">
        </li>
        <li>
          <div class="icon dripicons-bookmarks"></div>
          <input type="text" readonly="readonly" value="bookmarks">
        </li>
        <li>
          <div class="icon dripicons-box"></div>
          <input type="text" readonly="readonly" value="box">
        </li>
        <li>
          <div class="icon dripicons-briefcase"></div>
          <input type="text" readonly="readonly" value="briefcase">
        </li>
        <li>
          <div class="icon dripicons-brightness-low"></div>
          <input type="text" readonly="readonly" value="brightness-low">
        </li>
        <li>
          <div class="icon dripicons-brightness-max"></div>
          <input type="text" readonly="readonly" value="brightness-max">
        </li>
        <li>
          <div class="icon dripicons-brightness-medium"></div>
          <input type="text" readonly="readonly" value="brightness-medium">
        </li>
        <li>
          <div class="icon dripicons-broadcast"></div>
          <input type="text" readonly="readonly" value="broadcast">
        </li>
        <li>
          <div class="icon dripicons-browser"></div>
          <input type="text" readonly="readonly" value="browser">
        </li>
        <li>
          <div class="icon dripicons-browser-upload"></div>
          <input type="text" readonly="readonly" value="browser-upload">
        </li>
        <li>
          <div class="icon dripicons-brush"></div>
          <input type="text" readonly="readonly" value="brush">
        </li>
        <li>
          <div class="icon dripicons-calendar"></div>
          <input type="text" readonly="readonly" value="calendar">
        </li>
        <li>
          <div class="icon dripicons-camcorder"></div>
          <input type="text" readonly="readonly" value="camcorder">
        </li>
        <li>
          <div class="icon dripicons-camera"></div>
          <input type="text" readonly="readonly" value="camera">
        </li>
        <li>
          <div class="icon dripicons-card"></div>
          <input type="text" readonly="readonly" value="card">
        </li>
        <li>
          <div class="icon dripicons-cart"></div>
          <input type="text" readonly="readonly" value="cart">
        </li>
        <li>
          <div class="icon dripicons-checklist"></div>
          <input type="text" readonly="readonly" value="checklist">
        </li>
        <li>
          <div class="icon dripicons-checkmark"></div>
          <input type="text" readonly="readonly" value="checkmark">
        </li>
        <li>
          <div class="icon dripicons-chevron-down"></div>
          <input type="text" readonly="readonly" value="chevron-down">
        </li>
        <li>
          <div class="icon dripicons-chevron-left"></div>
          <input type="text" readonly="readonly" value="chevron-left">
        </li>
        <li>
          <div class="icon dripicons-chevron-right"></div>
          <input type="text" readonly="readonly" value="chevron-right">
        </li>
        <li>
          <div class="icon dripicons-chevron-up"></div>
          <input type="text" readonly="readonly" value="chevron-up">
        </li>
        <li>
          <div class="icon dripicons-clipboard"></div>
          <input type="text" readonly="readonly" value="clipboard">
        </li>
        <li>
          <div class="icon dripicons-clock"></div>
          <input type="text" readonly="readonly" value="clock">
        </li>
        <li>
          <div class="icon dripicons-clockwise"></div>
          <input type="text" readonly="readonly" value="clockwise">
        </li>
        <li>
          <div class="icon dripicons-cloud"></div>
          <input type="text" readonly="readonly" value="cloud">
        </li>
        <li>
          <div class="icon dripicons-cloud-download"></div>
          <input type="text" readonly="readonly" value="cloud-download">
        </li>
        <li>
          <div class="icon dripicons-cloud-upload"></div>
          <input type="text" readonly="readonly" value="cloud-upload">
        </li>
        <li>
          <div class="icon dripicons-code"></div>
          <input type="text" readonly="readonly" value="code">
        </li>
        <li>
          <div class="icon dripicons-contract"></div>
          <input type="text" readonly="readonly" value="contract">
        </li>
        <li>
          <div class="icon dripicons-contract-2"></div>
          <input type="text" readonly="readonly" value="contract-2">
        </li>
        <li>
          <div class="icon dripicons-conversation"></div>
          <input type="text" readonly="readonly" value="conversation">
        </li>
        <li>
          <div class="icon dripicons-copy"></div>
          <input type="text" readonly="readonly" value="copy">
        </li>
        <li>
          <div class="icon dripicons-crop"></div>
          <input type="text" readonly="readonly" value="crop">
        </li>
        <li>
          <div class="icon dripicons-cross"></div>
          <input type="text" readonly="readonly" value="cross">
        </li>
        <li>
          <div class="icon dripicons-crosshair"></div>
          <input type="text" readonly="readonly" value="crosshair">
        </li>
        <li>
          <div class="icon dripicons-cutlery"></div>
          <input type="text" readonly="readonly" value="cutlery">
        </li>
        <li>
          <div class="icon dripicons-device-desktop"></div>
          <input type="text" readonly="readonly" value="device-desktop">
        </li>
        <li>
          <div class="icon dripicons-device-mobile"></div>
          <input type="text" readonly="readonly" value="device-mobile">
        </li>
        <li>
          <div class="icon dripicons-device-tablet"></div>
          <input type="text" readonly="readonly" value="device-tablet">
        </li>
        <li>
          <div class="icon dripicons-direction"></div>
          <input type="text" readonly="readonly" value="direction">
        </li>
        <li>
          <div class="icon dripicons-disc"></div>
          <input type="text" readonly="readonly" value="disc">
        </li>
        <li>
          <div class="icon dripicons-document"></div>
          <input type="text" readonly="readonly" value="document">
        </li>
        <li>
          <div class="icon dripicons-document-delete"></div>
          <input type="text" readonly="readonly" value="document-delete">
        </li>
        <li>
          <div class="icon dripicons-document-edit"></div>
          <input type="text" readonly="readonly" value="document-edit">
        </li>
        <li>
          <div class="icon dripicons-document-new"></div>
          <input type="text" readonly="readonly" value="document-new">
        </li>
        <li>
          <div class="icon dripicons-document-remove"></div>
          <input type="text" readonly="readonly" value="document-remove">
        </li>
        <li>
          <div class="icon dripicons-dot"></div>
          <input type="text" readonly="readonly" value="dot">
        </li>
        <li>
          <div class="icon dripicons-dots-2"></div>
          <input type="text" readonly="readonly" value="dots-2">
        </li>
        <li>
          <div class="icon dripicons-dots-3"></div>
          <input type="text" readonly="readonly" value="dots-3">
        </li>
        <li>
          <div class="icon dripicons-download"></div>
          <input type="text" readonly="readonly" value="download">
        </li>
        <li>
          <div class="icon dripicons-duplicate"></div>
          <input type="text" readonly="readonly" value="duplicate">
        </li>
        <li>
          <div class="icon dripicons-enter"></div>
          <input type="text" readonly="readonly" value="enter">
        </li>
        <li>
          <div class="icon dripicons-exit"></div>
          <input type="text" readonly="readonly" value="exit">
        </li>
        <li>
          <div class="icon dripicons-expand"></div>
          <input type="text" readonly="readonly" value="expand">
        </li>
        <li>
          <div class="icon dripicons-expand-2"></div>
          <input type="text" readonly="readonly" value="expand-2">
        </li>
        <li>
          <div class="icon dripicons-experiment"></div>
          <input type="text" readonly="readonly" value="experiment">
        </li>
        <li>
          <div class="icon dripicons-export"></div>
          <input type="text" readonly="readonly" value="export">
        </li>
        <li>
          <div class="icon dripicons-feed"></div>
          <input type="text" readonly="readonly" value="feed">
        </li>
        <li>
          <div class="icon dripicons-flag"></div>
          <input type="text" readonly="readonly" value="flag">
        </li>
        <li>
          <div class="icon dripicons-flashlight"></div>
          <input type="text" readonly="readonly" value="flashlight">
        </li>
        <li>
          <div class="icon dripicons-folder"></div>
          <input type="text" readonly="readonly" value="folder">
        </li>
        <li>
          <div class="icon dripicons-folder-open"></div>
          <input type="text" readonly="readonly" value="folder-open">
        </li>
        <li>
          <div class="icon dripicons-forward"></div>
          <input type="text" readonly="readonly" value="forward">
        </li>
        <li>
          <div class="icon dripicons-gaming"></div>
          <input type="text" readonly="readonly" value="gaming">
        </li>
        <li>
          <div class="icon dripicons-gear"></div>
          <input type="text" readonly="readonly" value="gear">
        </li>
        <li>
          <div class="icon dripicons-graduation"></div>
          <input type="text" readonly="readonly" value="graduation">
        </li>
        <li>
          <div class="icon dripicons-graph-bar"></div>
          <input type="text" readonly="readonly" value="graph-bar">
        </li>
        <li>
          <div class="icon dripicons-graph-line"></div>
          <input type="text" readonly="readonly" value="graph-line">
        </li>
        <li>
          <div class="icon dripicons-graph-pie"></div>
          <input type="text" readonly="readonly" value="graph-pie">
        </li>
        <li>
          <div class="icon dripicons-headset"></div>
          <input type="text" readonly="readonly" value="headset">
        </li>
        <li>
          <div class="icon dripicons-heart"></div>
          <input type="text" readonly="readonly" value="heart">
        </li>
        <li>
          <div class="icon dripicons-help"></div>
          <input type="text" readonly="readonly" value="help">
        </li>
        <li>
          <div class="icon dripicons-home"></div>
          <input type="text" readonly="readonly" value="home">
        </li>
        <li>
          <div class="icon dripicons-hourglass"></div>
          <input type="text" readonly="readonly" value="hourglass">
        </li>
        <li>
          <div class="icon dripicons-inbox"></div>
          <input type="text" readonly="readonly" value="inbox">
        </li>
        <li>
          <div class="icon dripicons-information"></div>
          <input type="text" readonly="readonly" value="information">
        </li>
        <li>
          <div class="icon dripicons-italic"></div>
          <input type="text" readonly="readonly" value="italic">
        </li>
        <li>
          <div class="icon dripicons-jewel"></div>
          <input type="text" readonly="readonly" value="jewel">
        </li>
        <li>
          <div class="icon dripicons-lifting"></div>
          <input type="text" readonly="readonly" value="lifting">
        </li>
        <li>
          <div class="icon dripicons-lightbulb"></div>
          <input type="text" readonly="readonly" value="lightbulb">
        </li>
        <li>
          <div class="icon dripicons-link"></div>
          <input type="text" readonly="readonly" value="link">
        </li>
        <li>
          <div class="icon dripicons-link-broken"></div>
          <input type="text" readonly="readonly" value="link-broken">
        </li>
        <li>
          <div class="icon dripicons-list"></div>
          <input type="text" readonly="readonly" value="list">
        </li>
        <li>
          <div class="icon dripicons-loading"></div>
          <input type="text" readonly="readonly" value="loading">
        </li>
        <li>
          <div class="icon dripicons-location"></div>
          <input type="text" readonly="readonly" value="location">
        </li>
        <li>
          <div class="icon dripicons-lock"></div>
          <input type="text" readonly="readonly" value="lock">
        </li>
        <li>
          <div class="icon dripicons-lock-open"></div>
          <input type="text" readonly="readonly" value="lock-open">
        </li>
        <li>
          <div class="icon dripicons-mail"></div>
          <input type="text" readonly="readonly" value="mail">
        </li>
        <li>
          <div class="icon dripicons-map"></div>
          <input type="text" readonly="readonly" value="map">
        </li>
        <li>
          <div class="icon dripicons-media-loop"></div>
          <input type="text" readonly="readonly" value="media-loop">
        </li>
        <li>
          <div class="icon dripicons-media-next"></div>
          <input type="text" readonly="readonly" value="media-next">
        </li>
        <li>
          <div class="icon dripicons-media-pause"></div>
          <input type="text" readonly="readonly" value="media-pause">
        </li>
        <li>
          <div class="icon dripicons-media-play"></div>
          <input type="text" readonly="readonly" value="media-play">
        </li>
        <li>
          <div class="icon dripicons-media-previous"></div>
          <input type="text" readonly="readonly" value="media-previous">
        </li>
        <li>
          <div class="icon dripicons-media-record"></div>
          <input type="text" readonly="readonly" value="media-record">
        </li>
        <li>
          <div class="icon dripicons-media-shuffle"></div>
          <input type="text" readonly="readonly" value="media-shuffle">
        </li>
        <li>
          <div class="icon dripicons-media-stop"></div>
          <input type="text" readonly="readonly" value="media-stop">
        </li>
        <li>
          <div class="icon dripicons-medical"></div>
          <input type="text" readonly="readonly" value="medical">
        </li>
        <li>
          <div class="icon dripicons-menu"></div>
          <input type="text" readonly="readonly" value="menu">
        </li>
        <li>
          <div class="icon dripicons-message"></div>
          <input type="text" readonly="readonly" value="message">
        </li>
        <li>
          <div class="icon dripicons-meter"></div>
          <input type="text" readonly="readonly" value="meter">
        </li>
        <li>
          <div class="icon dripicons-microphone"></div>
          <input type="text" readonly="readonly" value="microphone">
        </li>
        <li>
          <div class="icon dripicons-minus"></div>
          <input type="text" readonly="readonly" value="minus">
        </li>
        <li>
          <div class="icon dripicons-monitor"></div>
          <input type="text" readonly="readonly" value="monitor">
        </li>
        <li>
          <div class="icon dripicons-move"></div>
          <input type="text" readonly="readonly" value="move">
        </li>
        <li>
          <div class="icon dripicons-music"></div>
          <input type="text" readonly="readonly" value="music">
        </li>
        <li>
          <div class="icon dripicons-network-1"></div>
          <input type="text" readonly="readonly" value="network-1">
        </li>
        <li>
          <div class="icon dripicons-network-2"></div>
          <input type="text" readonly="readonly" value="network-2">
        </li>
        <li>
          <div class="icon dripicons-network-3"></div>
          <input type="text" readonly="readonly" value="network-3">
        </li>
        <li>
          <div class="icon dripicons-network-4"></div>
          <input type="text" readonly="readonly" value="network-4">
        </li>
        <li>
          <div class="icon dripicons-network-5"></div>
          <input type="text" readonly="readonly" value="network-5">
        </li>
        <li>
          <div class="icon dripicons-pamphlet"></div>
          <input type="text" readonly="readonly" value="pamphlet">
        </li>
        <li>
          <div class="icon dripicons-paperclip"></div>
          <input type="text" readonly="readonly" value="paperclip">
        </li>
        <li>
          <div class="icon dripicons-pencil"></div>
          <input type="text" readonly="readonly" value="pencil">
        </li>
        <li>
          <div class="icon dripicons-phone"></div>
          <input type="text" readonly="readonly" value="phone">
        </li>
        <li>
          <div class="icon dripicons-photo"></div>
          <input type="text" readonly="readonly" value="photo">
        </li>
        <li>
          <div class="icon dripicons-photo-group"></div>
          <input type="text" readonly="readonly" value="photo-group">
        </li>
        <li>
          <div class="icon dripicons-pill"></div>
          <input type="text" readonly="readonly" value="pill">
        </li>
        <li>
          <div class="icon dripicons-pin"></div>
          <input type="text" readonly="readonly" value="pin">
        </li>
        <li>
          <div class="icon dripicons-plus"></div>
          <input type="text" readonly="readonly" value="plus">
        </li>
        <li>
          <div class="icon dripicons-power"></div>
          <input type="text" readonly="readonly" value="power">
        </li>
        <li>
          <div class="icon dripicons-preview"></div>
          <input type="text" readonly="readonly" value="preview">
        </li>
        <li>
          <div class="icon dripicons-print"></div>
          <input type="text" readonly="readonly" value="print">
        </li>
        <li>
          <div class="icon dripicons-pulse"></div>
          <input type="text" readonly="readonly" value="pulse">
        </li>
        <li>
          <div class="icon dripicons-question"></div>
          <input type="text" readonly="readonly" value="question">
        </li>
        <li>
          <div class="icon dripicons-reply"></div>
          <input type="text" readonly="readonly" value="reply">
        </li>
        <li>
          <div class="icon dripicons-reply-all"></div>
          <input type="text" readonly="readonly" value="reply-all">
        </li>
        <li>
          <div class="icon dripicons-return"></div>
          <input type="text" readonly="readonly" value="return">
        </li>
        <li>
          <div class="icon dripicons-retweet"></div>
          <input type="text" readonly="readonly" value="retweet">
        </li>
        <li>
          <div class="icon dripicons-rocket"></div>
          <input type="text" readonly="readonly" value="rocket">
        </li>
        <li>
          <div class="icon dripicons-scale"></div>
          <input type="text" readonly="readonly" value="scale">
        </li>
        <li>
          <div class="icon dripicons-search"></div>
          <input type="text" readonly="readonly" value="search">
        </li>
        <li>
          <div class="icon dripicons-shopping-bag"></div>
          <input type="text" readonly="readonly" value="shopping-bag">
        </li>
        <li>
          <div class="icon dripicons-skip"></div>
          <input type="text" readonly="readonly" value="skip">
        </li>
        <li>
          <div class="icon dripicons-stack"></div>
          <input type="text" readonly="readonly" value="stack">
        </li>
        <li>
          <div class="icon dripicons-star"></div>
          <input type="text" readonly="readonly" value="star">
        </li>
        <li>
          <div class="icon dripicons-stopwatch"></div>
          <input type="text" readonly="readonly" value="stopwatch">
        </li>
        <li>
          <div class="icon dripicons-store"></div>
          <input type="text" readonly="readonly" value="store">
        </li>
        <li>
          <div class="icon dripicons-suitcase"></div>
          <input type="text" readonly="readonly" value="suitcase">
        </li>
        <li>
          <div class="icon dripicons-swap"></div>
          <input type="text" readonly="readonly" value="swap">
        </li>
        <li>
          <div class="icon dripicons-tag"></div>
          <input type="text" readonly="readonly" value="tag">
        </li>
        <li>
          <div class="icon dripicons-tag-delete"></div>
          <input type="text" readonly="readonly" value="tag-delete">
        </li>
        <li>
          <div class="icon dripicons-tags"></div>
          <input type="text" readonly="readonly" value="tags">
        </li>
        <li>
          <div class="icon dripicons-thumbs-down"></div>
          <input type="text" readonly="readonly" value="thumbs-down">
        </li>
        <li>
          <div class="icon dripicons-thumbs-up"></div>
          <input type="text" readonly="readonly" value="thumbs-up">
        </li>
        <li>
          <div class="icon dripicons-ticket"></div>
          <input type="text" readonly="readonly" value="ticket">
        </li>
        <li>
          <div class="icon dripicons-time-reverse"></div>
          <input type="text" readonly="readonly" value="time-reverse">
        </li>
        <li>
          <div class="icon dripicons-to-do"></div>
          <input type="text" readonly="readonly" value="to-do">
        </li>
        <li>
          <div class="icon dripicons-toggles"></div>
          <input type="text" readonly="readonly" value="toggles">
        </li>
        <li>
          <div class="icon dripicons-trash"></div>
          <input type="text" readonly="readonly" value="trash">
        </li>
        <li>
          <div class="icon dripicons-trophy"></div>
          <input type="text" readonly="readonly" value="trophy">
        </li>
        <li>
          <div class="icon dripicons-upload"></div>
          <input type="text" readonly="readonly" value="upload">
        </li>
        <li>
          <div class="icon dripicons-user"></div>
          <input type="text" readonly="readonly" value="user">
        </li>
        <li>
          <div class="icon dripicons-user-group"></div>
          <input type="text" readonly="readonly" value="user-group">
        </li>
        <li>
          <div class="icon dripicons-user-id"></div>
          <input type="text" readonly="readonly" value="user-id">
        </li>
        <li>
          <div class="icon dripicons-vibrate"></div>
          <input type="text" readonly="readonly" value="vibrate">
        </li>
        <li>
          <div class="icon dripicons-view-apps"></div>
          <input type="text" readonly="readonly" value="view-apps">
        </li>
        <li>
          <div class="icon dripicons-view-list"></div>
          <input type="text" readonly="readonly" value="view-list">
        </li>
        <li>
          <div class="icon dripicons-view-list-large"></div>
          <input type="text" readonly="readonly" value="view-list-large">
        </li>
        <li>
          <div class="icon dripicons-view-thumb"></div>
          <input type="text" readonly="readonly" value="view-thumb">
        </li>
        <li>
          <div class="icon dripicons-volume-full"></div>
          <input type="text" readonly="readonly" value="volume-full">
        </li>
        <li>
          <div class="icon dripicons-volume-low"></div>
          <input type="text" readonly="readonly" value="volume-low">
        </li>
        <li>
          <div class="icon dripicons-volume-medium"></div>
          <input type="text" readonly="readonly" value="volume-medium">
        </li>
        <li>
          <div class="icon dripicons-volume-off"></div>
          <input type="text" readonly="readonly" value="volume-off">
        </li>
        <li>
          <div class="icon dripicons-wallet"></div>
          <input type="text" readonly="readonly" value="wallet">
        </li>
        <li>
          <div class="icon dripicons-warning"></div>
          <input type="text" readonly="readonly" value="warning">
        </li>
        <li>
          <div class="icon dripicons-web"></div>
          <input type="text" readonly="readonly" value="web">
        </li>
        <li>
          <div class="icon dripicons-weight"></div>
          <input type="text" readonly="readonly" value="weight">
        </li>
        <li>
          <div class="icon dripicons-wifi"></div>
          <input type="text" readonly="readonly" value="wifi">
        </li>
        <li>
          <div class="icon dripicons-wrong"></div>
          <input type="text" readonly="readonly" value="wrong">
        </li>
        <li>
          <div class="icon dripicons-zoom-in"></div>
          <input type="text" readonly="readonly" value="zoom-in">
        </li>
        <li>
          <div class="icon dripicons-zoom-out"></div>
          <input type="text" readonly="readonly" value="zoom-out">
        </li>
      </ul>
      <a href="http://designerfuel.tumblr.com/post/141385060889/dripicons-v2-free-iconset-available-formats" class="button" target="_blank"><span class="icon icon-download"></span>&nbsp;&nbsp;Download</a>
      <p class="small footer">This font was created with <a href="http://fontastic.me/">Fontastic</a>. Dripicons 2.0 are licensed under <a rel="license" href="http://creativecommons.org/licenses/by/4.0/" target="_blank">Creative Commons Attribution 4.0 International License</a> and the font under <a href="http://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL" target="_blank">SIL Open Font License</a>.</p>
    </div>
    <script>(function() {
  var glyphs, i, len, ref;

  ref = document.getElementsByClassName('glyphs');
  for (i = 0, len = ref.length; i < len; i++) {
    glyphs = ref[i];
    glyphs.addEventListener('click', function(event) {
      if (event.target.tagName === 'INPUT') {
        return event.target.select();
      }
    });
  }

}).call(this);

    </script>
  </body>
</html>
