@import url(https://fonts.googleapis.com/css?family=Noto+Sans:400,700);
  * {transition:all 0.15s;}
  ::selection{background:#374347;color:#fff;}
  ::-moz-selection{background:#374347;color:#fff;}
  html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td{margin:0;padding:0;border:0;outline:0;font-weight:inherit;font-style:inherit;font-family:inherit;font-size:100%;vertical-align:baseline}
  body{line-height:1;color:#B6B6B6;background:#fff;}
  ol,ul{list-style:none}
  table{border-collapse:separate;border-spacing:0;vertical-align:middle}
  caption,th,td{text-align:left;font-weight:normal;vertical-align:middle}
  a img{border:none}
  *{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
  body{font-family:'Noto Sans','Helvetica','Arial',sans-serif;font-weight:400;}
  .container{margin:15px auto;width:90%;max-width:1140px;}
  h1{margin-bottom:30px;font-weight:700;font-size:120px;line-height:134px;color:#398FF7;text-align:center;text-transform:normal;letter-spacing:1px;}
  h4{font-size:12px;line-height:20px;letter-spacing:8px;font-weight:400;text-transform:uppercase;color:#161616;margin-bottom:20px;margin-top:130px;text-align:center;}
  h2{color:#969696;font-size:24px;padding:0 0 21px 5px;margin:50px 0 0 0;text-transform:normal;font-weight:400;text-align:center;letter-spacing:1px;}
  .small{font-size:14px;color:#969696;text-align:center;line-height: 20px;}
  .small a{color:#969696;}
  .small a:hover{color:#398FF7}
  .footer{font-size:12px;display:block;margin: 20px auto 80px auto;line-height: 20px;}
  .glyphs.character-mapping{margin:0;padding:0;border:none;}
  .glyphs.character-mapping li{margin:0;display:inline-block;width:90px;border-radius:4px;}
  .glyphs.character-mapping .icon{margin:10px 0 10px 15px;padding:15px;position:relative;width:55px;height:55px;color:#398FF7 !important;overflow:hidden;-webkit-border-radius:3px;border-radius:3px;font-size:32px;}
  .glyphs.character-mapping .icon svg{fill:#398FF7}
  .glyphs.character-mapping li:hover .icon{color:#fff !important;}
  .glyphs.character-mapping li:hover .icon svg{fill:#fff}
  .glyphs.character-mapping li:hover input{opacity:100;}
  .glyphs.character-mapping li:hover{background:#374347;}
  .glyphs.character-mapping input{opacity:0;background:#398FF7;color:#fff;margin:0;padding:10px 0;line-height:12px;font-size:12px;display:block;width:100%;border:none;text-align:center;outline:none;border-bottom-right-radius:3px;border-bottom-left-radius:3px;font-family:'Montserrat','Helvetica','Arial',sans-serif;font-weight:400;}
  .glyphs.character-mapping input:focus{border:none;}
  .glyphs.character-mapping input:hover{border:none;}
  .glyphs.css-mapping{margin:0 0 60px 0;padding:30px 0 20px 30px;color:rgba(0,0,0,0.5);border:none;-webkit-border-radius:3px;border-radius:3px;}
  .glyphs.css-mapping li{margin:0 30px 20px 0;padding:0;display:inline-block;overflow:hidden}
  .glyphs.css-mapping .icon{margin:0;margin-right:10px;padding:13px;height:50px;width:50px;color:#398FF7 !important;overflow:hidden;float:left;font-size:24px}
  .glyphs.css-mapping input{background:none;color:#398FF7;margin:0;margin-top:5px;padding:8px;line-height:14px;font-size:14px;font-family:'Montserrat','Helvetica','Arial',sans-serif;font-weight:700;display:block;width:120px;height:40px;border:none;-webkit-border-radius:5px;border-radius:5px;outline:none;float:right;}
  .glyphs.css-mapping input:focus{border:none;}
  .glyphs.css-mapping input:hover{}
  .button{display:block;width:250px;margin:0 auto 40px auto;padding:30px 50px;background:#374347;color:#fff;font-weight:700;text-transform:normal;letter-spacing:1px;text-decoration:none;text-align:center;border-radius:4px;}
  .button:hover{background:#398FF7;color:#fff;}
  .share, .share li {padding: 0;list-style: none;max-width: 500px;display: block;text-align: center;}
  .share {margin: 30px auto 15px auto;}
  .share li {padding: 0 15px;margin: 0 auto;text-align: center;display: inline;overflow: hidden;position: relative;right: -15px;}
  .share li:first-child {top: -3px;}
  @media screen and (max-width: 640px) {
    h1{font-size:48px;line-height:56px;}
  }