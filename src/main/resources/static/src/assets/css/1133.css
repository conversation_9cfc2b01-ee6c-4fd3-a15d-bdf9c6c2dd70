/* body { overflow-x: hidden;} */
.dowebok { margin: 0 auto;}
.swiper-container { margin: 0 auto; overflow: visible;}
.swiper-container:hover .swiper-button-next, .swiper-container:hover .swiper-button-prev { display: block;}
.swiper-slide { position: relative; opacity: 0 !important;}
.swiper-slide img { width: 100%;}
.swiper-slide-active { opacity: 1 !important;}
.swiper-slide .ren { position: absolute; left: 0; top: 0; width: 1436px; height: 840px;}
.swiper-button-next, .swiper-button-prev { display: none; width: 40px; height: 68px; background-size: contain;}
.swiper-button-next { right: 100px; background-image: url(../img/next.png);}
.swiper-button-prev { left: 100px; background-image: url(../img/prev.png);}
.swiper-container-horizontal .swiper-pagination-bullets { bottom: 40px;}
.swiper-pagination-bullet { width: 70px; height: 4px; margin: 0 10px !important; padding: 6px 0; border-radius: 0; background-color: transparent; opacity: 1;}
.swiper-pagination-bullet:after { content: ""; display: block; height: 4px; background-color: #fff; opacity: 0.5;}
.swiper-pagination-bullet:hover:after, .swiper-pagination-bullet-active:after { background-color: #000;}