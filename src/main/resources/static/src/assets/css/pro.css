
.divlist {
    width: 100%;
    display: inline-block;
}

.divlist2 {
    width: 68%;
    display: inline-block;
    font-size: 12px
}

.divlist2 ul {
    padding-left: 5px;
}

.divlist2 ul li {

    float: left;
    width: 100%;
    list-style-type: none;
    margin: 5px;
}


.divlist ul {
    padding-left: 5px;
}

.divlist ul li {

    float: left;

    list-style-type: none;
    margin: 5px;
}


.divlist ul li img {
    width: 95%;
    transition: all 1s;
}

.divlist ul li img:hover {
    transform: scale(1.1);

}

.divlist li span {
    width: 100%;
    text-align: center;
    display: block;
    font-size: 13px;
    line-height: 23px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.divlist li a {
    text-decoration: none;

}

.divlist .wspan {
    font-size: 14px;
    font-weight: bold;
}


.border {
    border: 1px solid black;

}

.widthk1 {
    width: 98%;
}

.widthk2 {
    width: 48%;
}

.widthk3 {
    width: 32%;
}

.widthk4 {
    width: 22.5%;
}

.widthk5 {
    width: 18.7%;
}

.widthk6 {
    width: 15.22%;
}

.widthk7 {
    width: 12.6%;
}

.widthk8 {
    width: 11.1%;
}

.text-red {
    color: red;
}

.text-12 {
    font-size: 12px;
}

.text-14 {
    font-size: 14px;
}

.text-15 {
    font-size: 15px;
}
.text-16 {
    font-size: 16px;
}
.text-17 {
    font-size: 17px;
}
.text-18 {
    font-size: 18px;
}

.text-center {
    text-align: center;
}

.text-blod {
    font-weight: bold;
}

.floatleft {
    float: left;
}

.floatright {
    float: right;
}

.cu-tag {

    vertical-align: middle;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 5px;
    font-family: Helvetica Neue, Helvetica, sans-serif;
    white-space: nowrap;
    height:30px;
}

.bg-red {
    background-color: #E54D42FF;
    color: white;
}

.bg-orange {
    background-color: #F37B1DFF;
    color: white;
}

.bg-yellow {
    background-color: #FBBD08FF;
    color: white;
}

.bg-olive {
    background-color: #8DC63FFF;
    color: white;
}

.bg-green {
    background-color: #39B54AFF;
    color: white;
}

.bg-cyan {
    background-color: #1CBBB4FF;
    color: white;
}

.bg-blue {
    background-color: #0081FFFF;
    color: white;
}

.bg-purple {
    background-color: #6739B6FF;
    color: white;
}

.bg-mauve {
    background-color: #9C26B0FF;
    color: white;
}

.bg-pink {
    background-color: #E03997FF;
    color: white;
}

.bg-brown {
    background-color: #A5673FFF;
    color: white;
}

.bg-grey {
    background-color: #8799A3FF;
    color: white;
}

.bg-gray {
    background-color: #f0f0f0;
    color: var(--black);
}


.line-cyan,
.lines-cyan {
    color: #1cbbb4;
    border: 1px solid #1cbbb4;
}


.line-red,
.lines-red {
    color: #E54D42FF;
    border: 1px solid #E54D42FF;
}


.line-orange,
.lines-orange {
    color: #F37B1DFF;
    border: 1px solid #F37B1DFF;
}


.line-yellow,
.lines-yellow {
    color: #FBBD08FF;
    border: 1px solid #FBBD08FF;
}


.line-olive,
.lines-olive {
    color: #8DC63FFF;
    border: 1px solid #8DC63FFF;
}


.line-green,
.lines-green {
    color: #39B54AFF;
    border: 1px solid #39B54AFF;
}


.line-cyan,
.lines-cyan {
    color: #1CBBB4FF;
    border: 1px solid #1CBBB4FF;
}


.line-blue,
.lines-blue {
    color: #0081FFFF;
    border: 1px solid #0081FFFF;
}


.line-purple,
.lines-purple {
    color: #6739B6FF;
    border: 1px solid #6739B6FF;
}


.line-mauve,
.lines-mauve {
    color: #9C26B0FF;
    border: 1px solid #9C26B0FF;
}


.line-pink,
.lines-pink {
    color: #E03997FF;
    border: 1px solid #E03997FF;
}


.line-brown,
.lines-brown {
    color: #A5673FFF;
    border: 1px solid #A5673FFF;
}


.line-grey,
.lines-grey {
    color: #8799A3FF;
    border: 1px solid #8799A3FF;
}


.line-white,
.lines-white {
    color: white;
}


.text-red {
    color: #E54D42FF;
}

.text-orange {
    color: #F37B1DFF;
}

.text-yellow {
    color: #FBBD08FF;
}

.text-olive {
    color: #8DC63FFF;
}

.text-green {
    color: #39B54AFF;
}

.text-cyan {
    color: #1CBBB4FF;
}

.text-blue {
    color: #0081FFFF;
}

.text-purple {
    color: #6739B6FF;
}

.text-mauve {
    color: #9C26B0FF;
}

.text-pink {
    color: #E03997FF;
}

.text-brown {
    color: #A5673FFF;
}

.text-grey {
    color: #8799A3FF;
}

.bg-red.light {
    color: #E54D42FF;
    background-color: #FADBD9FF;
}

.bg-orange.light {
    color: #F37B1DFF;
    background-color:  #fde6d2;
}

.bg-yellow.light {
    color: #FBBD08FF;
    background-color:  #fef2ce;
}

.bg-olive.light {
    color: #8DC63FFF;
    background-color: #e8f4d9;
}

.bg-green.light {
    color: #39B54AFF;
    background-color: #d7f0db;
}

.bg-cyan.light {
    color: #1CBBB4FF;
    background-color: #D2F1F0FF;
}

.bg-blue.light {
    color: #0081FFFF;
    background-color: #CCE6FFFF;
}

.bg-purple.light {
    color: #6739B6FF;
    background-color: #E1D7F0FF;
}

.bg-mauve.light {
    color: #9C26B0FF;
    background-color: #EBD4EFFF;
}

.bg-pink.light {
    color: #E03997FF;
    background-color: #F9D7EAFF;
}

.bg-brown.light {
    color: #A5673FFF;
    background-color: #EDE1D9FF;
}

.bg-grey.light {
    color: #8799A3FF;
    background-color: #E7EBEDFF;
}

.bg-gradual-red {
    background-image: linear-gradient(45deg, #f43f3b, #ec008c);
    color: white;
}

.bg-gradual-orange {
    background-image: linear-gradient(45deg, #ff9700, #ed1c24);
    color: white;
}

.bg-gradual-green {
    background-image: linear-gradient(45deg, #39b54a, #8dc63f);
    color: white;
}

.bg-gradual-purple {
    background-image: linear-gradient(45deg, #9000ff, #5e00ff);
    color: white;
}

.bg-gradual-pink {
    background-image: linear-gradient(45deg, #ec008c, #6739b6);
    color: white;
}

.bg-gradual-blue {
    background-image: linear-gradient(45deg, #0081ff, #1cbbb4);
    color: white;
}

.pinglun {
    width: 99%;
    min-height: 60px;
    display: block;
    margin: 0 auto;
    font-size: 13px;
    border-bottom: 1px dashed #ccc;
    padding: 10px;
}

.pinglunzuo {
    width: 60px;
    height: 60px;
    float: left;
}

.pinglunzuo img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.pinglunyou {
    width: 85%;
    height: 80px;
    float: left;
    margin-left: 10px;
}

.round {
    border-radius:35%;
}




.news-container-unique {
    width: 90%;
    max-width: 600px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.news-header-unique {
    background-color: #f5f5f5;
    padding: 10px;
    border-bottom: 2px solid #e60012;
    display: flex;
    align-items: center;
}
.news-header-unique::before {
    content: '';
    width: 5px;
    height: 20px;
    background-color: #e60012;
    margin-right: 10px;
}
.news-header-unique h2 {
    margin: 0;
    color: #e60012;
}
.news-list-unique {
    list-style: none;
    padding: 0;
    margin: 0;
}
.news-item-wrapper-unique {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #ddd;
    transition: background-color 0.3s;
}
.news-item-wrapper-unique:hover {
    background-color: #f5f5f5;
}
.news-item-number-unique {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    margin-right: 10px;
    font-weight: bold;
    font-size: 16px;
    transition: transform 0.3s;
}
.news-item-wrapper-unique:hover .news-item-number-unique {
    transform: scale(1.2);
}
.news-item-wrapper-unique:nth-child(1) .news-item-number-unique,
.news-item-wrapper-unique:nth-child(2) .news-item-number-unique,
.news-item-wrapper-unique:nth-child(3) .news-item-number-unique {
    background-color: #e60012;
    color: #fff;
}
.news-item-wrapper-unique:nth-child(n+4) .news-item-number-unique {
    background-color: #ddd;
    color: #333;
}
.news-item-text-unique {
    flex: 1;
}
.news-item-text-unique a {
    text-decoration: none;
    color: #333;
    transition: color 0.3s;
}
.news-item-text-unique a:hover {
    color: #e60012;
}


.video-report-container-unique {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}
.video-report-header-unique {
    color: #e60012;
    font-size: 24px;
    margin: 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #e60012;
}


.video-report-grid-unique {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-top: 20px;
}
.video-report-item-unique {
    display: flex;
    flex-direction: column;
}
.video-report-item-unique img {
    width: 100%;
    height: auto;
    object-fit: cover;
}
.video-report-text-unique {
    margin: 10px 0 0;
    font-size: 14px;
    line-height: 1.4;
}
.video-report-item-unique:hover .video-report-text-unique {
    color: #e60012;
}  