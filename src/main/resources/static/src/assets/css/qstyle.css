/*
  Theme Name: LittleAngel - Store eCommerce HTML5 Template.
  Description: Store eCommerce HTML5 Template.
  Version: 1.0
    
*/

/* CSS Index
-----------------------------------
1. Theme default css
2. header
3. search modal
4. breadcrumb
5. slider
6. collection
7. shop
8. offer
9. feature-product
10. collection
11. instagram
12. footer
13. newsletter

*/



/* 1. Theme default css */
@import url('https://fonts.googleapis.com/css?family=Baloo|Open+Sans:300,300i,400,400i,600,700,800');

body {
	font-family: 'Open Sans', sans-serif;
	font-weight: normal;
	font-style: normal;
	color: #666666;
}

.img {
	max-width: 100%;
	transition: all 0.3s ease-out 0s;
}

.f-left {
	float: left
}

.f-right {
	float: right
}

.fix {
	overflow: hidden
}

a,
.button {
	-webkit-transition: all 0.3s ease-out 0s;
	-moz-transition: all 0.3s ease-out 0s;
	-ms-transition: all 0.3s ease-out 0s;
	-o-transition: all 0.3s ease-out 0s;
	transition: all 0.3s ease-out 0s;
}

a:focus,
.button:focus {
	text-decoration: none;
	outline: none;
}

a:focus,
a:hover,
.portfolio-cat a:hover,
.footer -menu li a:hover {
	text-decoration: none;
	box-shadow: none !important;
}

a,
button {
	color: #1696e7;
	outline: medium none;
}

button:focus,
input:focus,
input:focus,
textarea,
textarea:focus {
	outline: 0;
	box-shadow: none;
}

.uppercase {
	text-transform: uppercase;
}

.capitalize {
	text-transform: capitalize;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	
	font-weight: normal;
	color: #133046;
	margin-top: 0px;
	font-style: normal;
	font-weight: 400;
	text-transform: normal;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
	color: inherit;
}

h1 {
	font-size: 40px;
	font-weight: 500;
}

h2 {
	font-size: 35px;
}

h3 {
	font-size: 28px;
	font-weight: 450;
}

h4 {
	font-size: 22px;
}

h5 {
	font-size: 18px;
}

h6 {
	font-size: 16px;
}

ul {
	margin: 0px;
	padding: 0px;
}

li {
	list-style: none
}

p {
	font-size: 16px;
	font-weight: normal;
	line-height: 24px;
	color: #666666;
	margin-bottom: 15px;
}

hr {
	border-bottom: 1px solid #eceff8;
	border-top: 0 none;
	margin: 30px 0;
	padding: 0;
}

label {
	color: #7e7e7e;
	cursor: pointer;
	font-size: 14px;
	font-weight: 400;
}

*::-moz-selection {
	background: #d6b161;
	color: #fff;
	text-shadow: none;
}

::-moz-selection {
	background: #444;
	color: #fff;
	text-shadow: none;
}

::selection {
	background: #444;
	color: #fff;
	text-shadow: none;
}

*::-moz-placeholder {
	color: #555555;
	font-size: 14px;
	opacity: 1;
}

*::placeholder {
	color: #555555;
	font-size: 14px;
	opacity: 1;
}

.theme-overlay {
	position: relative
}

.theme-overlay::before {
	background: #1696e7 none repeat scroll 0 0;
	content: "";
	height: 100%;
	left: 0;
	opacity: 0.6;
	position: absolute;
	top: 0;
	width: 100%;
}

.separator {
	border-top: 1px solid #f2f2f2
}

/* button style */
.btn {
	-moz-user-select: none;
	background: #e63a7a;
	border: 1px solid #e63a7a;
	border-radius: 0;
	color: #fff;
	cursor: pointer;
	display: inline-block;
	font-size: 13px;
	letter-spacing: .2px;
	line-height: 1;
	margin-bottom: 0;
	padding:10px 15px;
	text-align: center;
	text-transform: capitalize;
	touch-action: manipulation;
	transition: all 0.3s ease 0s;
	vertical-align: middle;
	white-space: nowrap;
}

.btn:hover {
	color: #e63a7a;
	background: transparent;
}

.breadcrumb>.active {
	color: #888;
}

/* scrollUp */
#scrollUp {
	background: #E63A7A;
	height: 45px;
	width: 45px;
	right: 50px;
	bottom: 77px;
	color: #fff;
	text-align: center;
	border-radius: 50%;
	font-size: 20px;
	line-height: 45px;
	z-index: 1 !important;
}

#scrollUp:hover {
	background: #444;
}

/* 2. header */
.header-top {
	background: #2c2e2c;
	height: 40px;
	line-height: 40px;
}

.top-cta p {
	margin-bottom: 0;
	font-size: 16px;
	color: #f7f7f7;
	font-weight: 700;
}

.top-cta i {
	margin-right: 5px;
}

.header-top-right ul>li {
	display: inline-block;
}

.header-top-right ul>li a {
	color: #f7f7f7;
	margin-right: 10px;
	font-size: 16px;
}

.header-top-right ul>li a>i {
	margin-right: 5px;
}

.nice-select {
	cursor: pointer;
	font-family: inherit;
	font-size: 14px;
	line-height: 40px;
	text-align: left !important;
	white-space: nowrap;
	color: #f7f7f7;
	font-weight: 600;
}

.main-menu ul li {
	display: inline-block;
	margin-left: 35px;
	position: relative;
}

.main-menu ul li a {
	font-size: 15px;
	font-weight: 700;
	color: #133046;
	display: block;
	position: relative;
}

.main-menu ul li .sub-menu {
	position: absolute;
	top: 110%;
	left: 0;
	background: #fff;
	z-index: 1;
	width: 240px;
	opacity: 0;
	visibility: hidden;
	transition: .3s;
	border-top: 3px solid #e63a7a;
}

.main-menu ul li .sub-menu li {
	display: block;
	margin: 0;
	border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.main-menu ul li .sub-menu li a {
	padding: 10px 15px;
	text-align: left;
	font-weight: 600;
	position: relative;
	font-size: 13px;
}

.main-menu ul li .sub-menu li a::before {
	position: absolute;
	left: 6px;
	color: #e63a7a;
	content: "-";
	transition: .3s;
	opacity: 0;
}

.main-menu ul li:hover .sub-menu {
	opacity: 1;
	visibility: visible;
	top: 100%;
}

.menu-icon ul li {
	display: inline-block;
	margin-left: 30px;
	position: relative;
}

.menu-icon ul li:last-child {
	padding: 18px 0;
}

.main-menu ul li.active a::before {
	width: 100%;
}

.main-menu ul li a::before {
	content: "";
	position: absolute;
	left: 0;
	bottom: -5px;
	background: #e63a7a;
	width: 0px;
	height: 2px;
	transition: all 300ms linear 0s;
}

.main-menu ul li:hover a::before {
	width: 100%;
}

.menu-icon ul li a {
	font-size: 24px;
	color: #133046;
	display: block;
}

.shop-cart {
	position: relative;
}

.shop-cart span {
	display: block;
	position: absolute;
	left: -10px;
	top: 0;
	height: 20px;
	width: 20px;
	font-size: 12px;
	background: #e63a7a;
	color: #fff;
	text-align: center;
	line-height: 20px;
	border-radius: 50%;
}

.sticky-menu {
	left: 0;
	margin: auto;
	position: fixed;
	top: 0;
	width: 100%;
	box-shadow: 0 0 60px 0 rgba(0, 0, 0, .07);
	z-index: 9999;
	-webkit-animation: .5s ease-in-out 0s normal none 1 running fadeInDown;
	animation: .5s ease-in-out 0s normal none 1 running fadeInDown;
	-webkit-box-shadow: 0 10px 15px rgba(25, 25, 25, 0.1);
	background: #fff;
	padding: 10px 0;
}

.menu-icon ul.minicart {
	background: #fff;
	opacity: 0;
	padding: 25px;
	position: absolute;
	right: 0;
	top: 100%;
	transition: all 0.3s ease 0s;
	width: 350px;
	z-index: 9;
	box-shadow: 0px 12px 24px 0px rgba(120, 120, 120, 0.3);
	visibility: hidden;
}

.menu-icon ul li:hover ul.minicart {
	opacity: 1;
	visibility: visible;
	z-index: 99;
}

.menu-icon ul li .minicart>li {
	display: block;
	margin-bottom: 22px;
	margin-left: 0;
	overflow: hidden;
	padding: 0;
}

.menu-icon ul li .minicart .cart-img {
	float: left;
}

ul.minicart .cart-img img {
	width: 85px;
}

.menu-icon ul li .minicart .cart-content {
	float: left;
	padding-left: 15px;
	text-align: left;
	margin-top: 20px;
}

.cart-content h3 {
	color: #ddd;
}

ul.minicart .cart-content a {
	color: #10111e;
	font-family: 'Open Sans', sans-serif;
	font-size: 16px;
	background: none;
	font-weight: 600;
}

ul.minicart .cart-content a:hover {
	color: #e63a7a;
}

ul.minicart .cart-price span {
	color: #a5a7bc;
	font-size: 13px;
	font-weight: 500;
}

ul.minicart .cart-price .new {
	font-size: 14px;
	color: #747691;
}

.menu-icon ul li .minicart .del-icon {
	float: right;
	margin-top: 30px;
}

.minicart .del-icon>a {
	color: #e63a7a;
	font-size: 18px;
}

.total-price {
	border-top: 1px solid #cacadb;
	overflow: hidden;
	padding-top: 25px;
	margin-top: 10px;
}

.total-price span {
	color: #747691;
	font-weight: 500;
}

.total-price span {
	color: #747691;
	font-weight: 500;
}

.menu-icon ul li .minicart>li:last-child {
	margin-bottom: 0;
}

.menu-icon ul li .minicart .checkout-link a {
	background: #45bf55;
	color: #fff;
	display: block;
	font-weight: 500;
	padding: 16px 30px;
	text-align: center;
	font-size: 13px;
	margin-bottom: 8px;
	text-transform: uppercase;
	letter-spacing: 2px;
}

.menu-icon ul li .minicart .checkout-link a.red-color {
	background: #e63a7a;
}

/* 3. search modal */
#search-modal {
	background-color: rgba(23, 26, 33, .95);
}

#search-modal .modal-dialog {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	border: none;
	outline: 0;
}

#search-modal .modal-dialog .modal-content {
	background: 0 0;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
	border: none;
}

#search-modal .modal-dialog .modal-content form {
	width: 555px;
	position: relative;
}

#search-modal .modal-dialog .modal-content form input {
	width: 100%;
	font-size: 36px;
	border: none;
	border-bottom: 3px solid rgba(255, 255, 255, .1);
	background: 0 0;
	color: #fff;
	padding-bottom: 12px;
	padding-right: 40px;
}

#search-modal .modal-dialog .modal-content form input::-moz-placeholder {
	font-size: 35px;
}

#search-modal .modal-dialog .modal-content form input::placeholder {
	font-size: 35px;
}

#search-modal .modal-dialog .modal-content form button {
	position: absolute;
	right: 0;
	margin-bottom: 3px;
	font-size: 30px;
	color: #e63a7a;
	background: 0 0;
	border: none;
	cursor: pointer;
	top: 11px;
}

/* 4. breadcrumb */
.breadcrumb-area {
	background: #f1f1f1;
	min-height: 210px;
	display: flex;
	align-items: center;
}

.breadcrumb-wrap h2 {
	font-size: 48px;
	margin-bottom: 3px;
}

.breadcrumb {
	display: -webkit-box;
	display: -ms-flexbox;
	display: block;
	-ms-flex-wrap: wrap;
	flex-wrap: unset;
	padding: 0;
	margin-bottom: 0;
	list-style: none;
	background-color: unset;
	border-radius: 0;
}

.breadcrumb-item {
	display: inline-block;
}

.breadcrumb-item a {
	color: #666;
	font-size: 14px;
}

.breadcrumb-item+.breadcrumb-item::before {
	display: inline-block;
	padding-right: 5px;
	padding-left: 5px;
	content: ">";
	font-size: 14px;
	color: #666666;
}

.breadcrumb>.active {
	color: #666666;
	font-size: 14px;
}

.breadcrumb-shape {
	position: absolute !important;
}

.b-shape01 {
	top: 30px !important;
	left: 90px !important;
}

.b-shape02 {
	top: 160px !important;
	left: 30% !important;
}

.b-shape03 {
	top: 70px !important;
	left: 45% !important;
}

.b-shape04 {
	top: 20px !important;
	left: 82% !important;
}

.b-shape05 {
	top: 170px !important;
	left: 95% !important;
}

/* 5. slider */
.slider-bg {
	min-height: 730px;
	background-size: cover;
	background-position: center;
	position: relative;
}

.slider-content h2 {
	color: #fff;
	font-size: 72px;
	line-height: 1.5;
	margin-bottom: 15px;
	letter-spacing: 2px;
}

.slider-shape {
	position: absolute;
	z-index: 1;
}

.slider-shape img {
	animation: heartbeat .8s infinite alternate;
}

.s-shape-1 {
	top: 70px;
	left: 130px;
}

.s-shape-2 {
	top: 41%;
	left: 2%;
}

.s-shape-3 {
	right: 260px;
	top: 95px;
}

.s-shape-4 {
	right: 90px;
	top: 300px;
}

.s-shape-5 {
	right: 270px;
	bottom: 120px;
}

.s-shape-6 {
	right: 31%;
	bottom: 120px;
}

.s-shape-7 {
	right: 20%;
	top: 45%;
}

/* heartbeat frame*/
@-webkit-keyframes heartbeat {
	to {
		-webkit-transform: scale(1.2);
		transform: scale(1.2);
	}
}

/* 6. collection */
.collect-thumb img {
	width: 100%;
}

.single-collect {
	position: relative;
	overflow: hidden;
}

.collect-content {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 90px;
	clip-path: polygon(-4px 0px, 101.35% 42.33%, 101.35% 105.55%, -0.81% 107.78%);
}

.collect-content h4 {
	font-size: 26px;
	margin-bottom: 0;
	color: #fff;
	line-height: 90px;
	margin-left: 55px;
}

.collect-content h4:hover a {
	color: #fff;
}

.collect-content h4:hover i {
	margin-right: 15px;
}

.collect-content h4 i {
	font-size: 18px;
	margin-right: 20px;
	transition: .3s;
}

.green-bg {
	background: #45bf55;
}

.blue-bg {
	background: #004290;
}

.pink-bg {
	background: #e63a7a;
}

/* 7. shop */
.section-title h2 {
	font-size: 48px;
	letter-spacing: 2px;
	margin-bottom: 20px;
	line-height: 1;
}

.section-title p {
	margin-bottom: 0;
}

.p-relative {
	position: relative;
}

.arrivals-shape {
	position: absolute;
	left: 160px;
	top: 0;
}

.sp-thumb {
	position: relative;
	overflow: hidden;
	margin-bottom: 13px;
}

.sp-thumb img {
	width: 100%;
}

.sp-thumb span {
	position: absolute;
	right: 20px;
	top: 20px;
	width: 58px;
	height: 22px;
	background: #45bf55;
	text-align: center;
	line-height: 20px;
	color: #fff;
	font-size: 14px;
	border-radius: 5px;
	font-weight: 600;
	z-index: 1;
}

.product-action {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.cart-button {
	position: absolute;
	left: 0;
	bottom: -30px;
	right: 0;
	text-align: center;
	z-index: 9;
	opacity: 0;
	transition: .3s;
}

.button-left {
	position: absolute;
	top: 30px;
	left: -20px;
	z-index: 1;
	opacity: 0;
	transition: .3s;
}

.button-left ul li a {
	height: 35px;
	width: 35px;
	display: block;
	color: #fff;
	font-size: 16px;
	line-height: 35px;
	text-align: center;
}

.cart-button .btn {
	font-size: 16px;
	font-weight: 700;
	text-transform: capitalize;
	letter-spacing: 0px;
	padding: 15px 23px;
}

.cart-button .btn:hover {
	background: #e63a7a;
	color: #fff;
}

.product-content h4 {
	font-size: 24px;
	letter-spacing: 1px;
	margin-bottom: 5px;
}

.product-content h4:hover a {
	color: #e63a7a;
}

.price span {
	font-size: 18px;
	color: #45bf55;
	font-weight: 600;
}

.shop-products:hover .button-left {
	left: 20px;
	opacity: 1;
}

.shop-products:hover .cart-button {
	bottom: 30px;
	opacity: 1;
}

.price span.old-price {
	display: inline-block;
	margin-right: 15px;
	color: #666666;
	text-decoration: line-through;
	font-size: 16px;
}

.shop-filter a {
	background: #f1f1f1;
	display: inline-block;
	font-size: 14px;
	font-weight: 700;
	color: #133046;
	padding: 10px 38px;
}

.shop-filter a i {
	margin-right: 10px;
}

.result-count {
	display: inline-block;
}

.short-by {
	display: inline-block;
}

.short-select>.nice-select {
	cursor: pointer;
	font-family: inherit;
	font-size: 14px;
	line-height: 1.6;
	text-align: left !important;
	white-space: nowrap;
	color: #133046;
	background: #f1f1f1;
	border-radius: 0;
	padding: 10px 70px;
	font-weight: 700;
}

.short-select>.nice-select::after {
	border-bottom: 1px solid #133046;
	border-right: 1px solid #133046;
	content: '';
	display: block;
	height: 9px;
	margin-top: -6px;
	pointer-events: none;
	position: absolute;
	right: 50px;
	top: 50%;
	-webkit-transform-origin: 66% 66%;
	-ms-transform-origin: 66% 66%;
	transform-origin: 66% 66%;
	-webkit-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
	-webkit-transition: all 0.15s ease-in-out;
	transition: all 0.15s ease-in-out;
	width: 9px;
}

.result-count p {
	margin: 0;
	border: 1px solid #f1f1f1;
	padding: 10px 38px;
	font-size: 14px;
	font-weight: 700;
	color: #133046;
	display: initial;
}

.pagination-wrap ul li {
	display: inline-block;
	margin: 0 5px;
}

.pagination-wrap ul li a {
	height: 33px;
	width: 33px;
	display: block;
	color: #133046;
	line-height: 33px;
	font-family: 'Baloo', cursive;
}

.pagination-wrap ul li:hover a {
	color: #fff;
	background: #e63a7a;
}

.pagination-wrap ul li:hover a i {
	color: #fff;
}

.pagination-wrap ul li.active a {
	color: #fff;
	background: #e63a7a;
}

.pagination-wrap ul li a i {
	font-size: 14px;
	color: #133046;
}

.p-details-thumb {
	width: 500px;
	float: left;
	display: block;
	margin-right: 20px;
}

.product-thumbnail {
	overflow: hidden;
	display: block;
}

.product-thumbnail .nav-tabs {
	border: none;
	display: block;
	flex-wrap: unset;
}

.product-thumbnail .nav-item {
	margin: 0;
	margin-bottom: 22px;
}

.product-thumbnail .nav-item:last-child {
	margin: 0;
}

.product-thumbnail .nav-item .nav-link {
	background: none;
	padding: 0;
	border: none;
}

.product-thumbnail .nav-item .nav-link.active {
	background: none;
	padding: 0;
	border: none;
}

.cart-plus form {
	width: 120px;
	position: relative;
	margin-right: 20px;
	display: inline-block;
}

.cart-plus-minus {
	position: relative;
}

.cart-plus-minus::before {
	content: "";
	position: absolute;
	left: 30px;
	height: 100%;
	width: 1px;
	background: #dedede;
	top: 0;
}

.cart-plus-minus input {
	width: 100%;
	border: 1px solid #dedede;
	padding: 9px 40px;
	text-align: center;
}

.qtybutton {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	left: 10px;
	font-size: 23px;
	color: #838b97;
	cursor: pointer;
}

.dec.qtybutton {
	font-size: 38px;
	top: 45%;
}

.inc.qtybutton {
	right: 8px;
	left: auto;
}

.cart-plus-minus::after {
	content: "";
	position: absolute;
	right: 30px;
	height: 100%;
	width: 1px;
	background: #e5e5e5;
	top: 0;
}

.cart-plus .btn {
	padding: 14px 35px;
}

.product-features ul li {
	display: inline-block;
	margin-right: 35px;
	font-size: 14px;
}

.product-features ul li span {
	color: #133046;
	font-weight: 400;
	display: inline-block;
	margin-right: 5px;
}

.product-features ul li a {
	color: #666666;
}

.product-features ul li a:hover {
	color: #e63a7a;
}

.d-inline {
	display: inline-block !important;
}

.pd-meta ul li {
	display: inline-block;
	margin-right: 5px;
}

.pd-meta ul li a {
	height: 30px;
	width: 30px;
	display: block;
	border: 1px solid #666;
	text-align: center;
	line-height: 30px;
	font-size: 14px;
	color: #133046;
	background: transparent;
}

.pd-meta ul li a:hover {
	color: #fff;
	background: #e63a7a;
	border-color: #e63a7a;
}

.product-share ul li {
	display: inline-block;
	margin-right: 12px;
}

.product-share ul li a {
	font-size: 14px;
	color: #919191;
}

.product-share ul li a:hover {
	color: #e63a7a;
}

.pd-meta {
	margin-right: 10px;
}

.product-details-t h3 {
	font-size: 26px;
	margin-bottom: 5px;
}

.product-d-price span {
	font-weight: 600;
	color: #45bf55;
	font-size: 18px;
	display: block;
	margin-bottom: 20px;
}

.product-rating i {
	color: #f084ac;
}

.product-rating i:last-child {
	color: #c3c3c3;
}

.product-rating {
	margin-bottom: 20px;
}

.p-details-text p {
	margin-bottom: 40px;
}

.desc-list ul li {
	border-bottom: 1px solid #f0f0f0;
	padding-bottom: 17px;
	margin-bottom: 17px;
}

.desc-list ul li span {
	font-size: 16px;
	font-weight: 600;
	color: #1d274e;
	min-width: 260px;
	display: inline-block;
	text-transform: capitalize;
}

.desc-features ul li {
	margin-bottom: 7px;
}

.desc-features ul li i {
	color: #1cae0d;
	margin-right: 10px;
}

.desc-title h4 {
	margin-bottom: 0;
	font-size: 26px;
}

.review-avatar {
	float: left;
	display: block;
	margin-right: 30px;
}

.review-content h5 {
	font-size: 16px;
	font-family: 'Open Sans', sans-serif;
	font-weight: 600;
	color: #133046;
	margin-bottom: 0;
}

.review-content h5 span {
	font-size: 12px;
	color: #818181;
}

.rating-star i {
	font-size: 12px;
	color: #f084ac;
}

.rating-star i:last-child {
	color: #c3c3c3;
}

.review-content {
	overflow: hidden;
}

.review-content p {
	margin-bottom: 0;
}

.review-wrap li {
	overflow: hidden;
	margin-bottom: 50px;
}

.review-wrap h4 {
	font-size: 20px;
	margin-bottom: 45px;
}

.review-box h4 {
	font-size: 20px;
	margin-bottom: 45px;
}

.review-rating span {
	display: inline-block;
	margin-right: 25px;
}

.review-rating a {
	font-size: 15px;
	color: #c3c3c3;
}

.review-rating a:hover {
	color: #f084ac;
}

.tab-padding {
	padding: 50px;
	border: 1px solid #ebebeb;
}

.review-padding {
	padding: 50px;
	border: 1px solid #ebebeb;
}

.review-form textarea {
	width: 100%;
	border: none;
	background: #f7f7f7;
	height: 140px;
	padding: 15px 20px;
	margin-bottom: 30px;
}

.review-form input {
	width: 100%;
	border: none;
	background: #f7f7f7;
	padding: 15px 20px;
	margin-bottom: 30px;
}

.review-form .btn {
	border-color: #133046;
	background: transparent;
	color: #133046;
}

.review-form .btn:hover {
	border-color: #e63a7a;
	background: #e63a7a;
	color: #fff;
}

.items-dec-wrap ul {
	border: none;
	display: block;
}

.items-dec-wrap ul li.nav-item {
	margin: 0;
	display: inline-block;
}

.items-dec-wrap .nav-tabs .nav-link {
	border: none;
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	border-color: unset;
	font-size: 18px;
	font-family: 'Baloo', cursive;
	line-height: 1;
	padding: 12px 30px;
	color: #133046;
}

.items-dec-wrap .nav-link {
	display: block;
	padding: 0;
}

.items-dec-wrap .nav-link.active {
	color: #fff;
	background-color: #45bf55;
	border-color: unset;
	font-size: 18px;
	font-family: 'Baloo', cursive;
	line-height: 1;
	padding: 12px 30px;
}

.related-title h3 {
	font-size: 30px;
	margin-bottom: 0;
	padding-bottom: 15px;
	position: relative;
}

.related-title h3::before {
	position: absolute;
	content: "";
	height: 2px;
	width: 55px;
	background: #c3c3c3;
	left: 0;
	right: 0;
	margin: auto;
	bottom: 0;
}

.product-active .slick-dots {
	text-align: center;
}

.product-active .slick-dots li {
	display: inline-block;
	margin: 0 3px;
	line-height: 1;
}

.product-active .slick-dots li.slick-active button {
	background: #133046;
}

.product-active .slick-dots li button {
	height: 12px;
	width: 12px;
	padding: 0;
	text-indent: -9999999px;
	background: transparent;
	border: 1px solid #133046;
	line-height: 1;
	border-radius: 50%;
	z-index: 9;
	cursor: pointer;
}

.product-active .slick-arrow {
	position: absolute;
	right: 17px;
	top: -90px;
	z-index: 9;
	height: 30px;
	width: 40px;
	border: 1px solid #133046;
	background: #fff;
	padding: 0;
	line-height: 30px;
	color: #133046;
	cursor: pointer;
	transition: .3s;
}

.product-active .slick-prev {
	right: 75px;
}

.product-active .slick-arrow:hover {
	background: #e63a7a;
	border-color: #e63a7a;
	color: #fff;
}

.sp-shape-wrap {
	position: absolute;
	left: -70px;
	top: -60px;
	z-index: 9;
}

.filter-widget {
	border: 4px solid #f9f9f9;
	padding: 30px;
	display: none;
	padding-bottom: 0;
}

.filter-widget .shop-widget {
	margin-bottom: 30px;
}

.shop-sidebar-banner img {
	width: 100%;
}

.shop-link li a:hover i,
.shop-link li a:hover {
	color: #e63a7a;
}

.shop-title {
	font-size: 20px;
	text-transform: uppercase;
	margin-bottom: 30px;
}

.shop-link li {
	margin-bottom: 15px;
}

.shop-link li a {
	color: #747691;
	font-size: 14px;
	font-weight: 600;
}

.shop-link li a i {
	color: #e7e7e7;
	margin-right: 10px;
	line-height: 1;
	transition: .3s;
}

.shop-link li span {
	height: 16px;
	width: 16px;
	background: transparent;
	display: inline-block;
	border-radius: 50%;
	border: 4px solid #fff;
	box-shadow: 0px 1px 10px 0px rgba(254, 69, 54, 0.2);
	position: relative;
	top: 2px;
	margin-right: 10px;
}

.shop-link li span.vista {
	background: #00fff6;
}

.shop-link li span.blue {
	background: #0066ff;
}

.shop-link li span.green {
	background: #61a344;
}

.shop-link li span.orange {
	background: #ff9600;
}

.shop-link li span.navy {
	background: navy;
}

.shop-link li span.pinkish {
	background: #ff0090;
}

/* 8. offer */
.percent-wrap {
	display: inline-block;
	position: relative;
}

.percent-wrap h3 {
	position: absolute;
	top: 38px;
	right: 0;
	left: 0;
	font-size: 48px;
	margin: 0;
	line-height: 1;
	color: #e63a7a;
}

.offer-content h2 {
	color: #fff;
	font-size: 72px;
	line-height: 1.5;
	margin-bottom: 15px;
}

.offer-time .time-count {
	display: inline-block;
	height: 90px;
	width: 90px;
	line-height: 80px;
	background: #f7f7f7;
	margin: 0 10px;
	font-size: 30px;
	font-weight: 700;
	color: #e63a7a;
	font-family: 'Baloo', cursive;
	margin-bottom: 10px;
}

.offer-time .time-count span {
	font-size: 16px;
	display: block;
	color: #133046;
	line-height: 1;
	margin-top: -22px;
	text-transform: capitalize;
	font-weight: 400;
}

.offer-bg {
	background-position: center;
	background-size: cover;
}

/* 9. feature-product */
.features-shape {
	position: absolute;
	top: 0;
	right: 20%;
	z-index: 1;
}

/* 10. collection */
.collection-bg {
	background-size: cover;
	background-position: center;
}

.collection-title p {
	color: #133046;
}

/* 11. instagram */
.insta-thumb img {
	width: 100%;
}

.single-insta-post {
	position: relative;
	overflow: hidden;
}

.single-insta-post>a {
	position: absolute;
	left: 50%;
	top: 70%;
	transform: translate(-50%, -50%);
	font-size: 38px;
	color: #fff;
	transition: .5s;
	opacity: 0;
	z-index: 9;
}

.insta-thumb {
	position: relative;
	overflow: hidden;
}

.insta-thumb::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	background: #e63a7a;
	opacity: 0;
	transition: .5s;
}

.single-insta-post:hover .insta-thumb::before {
	opacity: .6;
}

.single-insta-post:hover>a {
	top: 50%;
	opacity: 1;
}

/* 14. Blog */
.blog-wrapper {
	background: #f7f7f7;
	overflow: hidden;
	padding: 20px;
}

.home-blog-wrapper {
	transition: .3s;
}

.blog-wrapper.home-blog-wrapper:hover {
	box-shadow: 10px 20px 30px rgba(0, 0, 0, .12);
}

.blog-thumb {
	margin-bottom: 20px;
}

.blog-title {
	display: inline-block;
	font-size: 32px;
	line-height: 1.4;
	margin: 10px 0 15px;
	padding: 0;
	text-align: left;
}

.blog-content.home-blog h2 {
	font-size: 18px;
	margin-bottom: 10px;
}

.blog-content.home-blog p {
	margin-bottom: 10px;
}

.link-box.home-blog-link a {
	font-size: 14px;
	color: #e63a7a;
}

.blog-title a {
	color: #222;
}

.link-box a {
	color: #e63a7a;
}

.blog-title a:hover {
	color: #e63a7a;
}

.meta-info ul li {
	color: #e63a7a;
	display: inline-block;
	font-size: 11px;
	padding: 0 12px;
	position: relative;
	text-transform: uppercase;
	font-weight: 700;
}

.embed-responsive {
	margin-bottom: 20px;
}

.meta-info ul li:first-child {
	padding-left: 0
}

.meta-info ul li a {
	color: #e63a7a;
}

.meta-info ul li a:hover {
	color: #444
}

.meta-info ul li::before {
	border: 1px solid #999;
	border-radius: 5px;
	content: "";
	height: 5px;
	left: -4px;
	position: absolute;
	top: 6px;
	width: 5px;
}

.meta-info ul li:first-child:before {
	display: none
}

.blog-thumb img {
	width: 100%;
}

.link-box a {
	font-weight: 600;
}

.rc-title h2 {
	font-size: 28px;
	margin-bottom: 0;
	position: relative;
	padding-bottom: 20px;
}

.rc-title h2::before {
	content: "";
	position: absolute;
	left: 0;
	width: 60px;
	height: 2px;
	background: #e63a7a;
	bottom: 0;
}

/* sidebar */
.widget {
	background: #f7f7f7;
	overflow: hidden;
	padding: 25px;
}

.widget-title {
	color: #000;
	display: inline-block;
	font-size: 20px;
	height: auto;
	letter-spacing: 1px;
	line-height: 1;
	margin-bottom: 20px;
	padding: 0 0 10px;
	position: relative;
}

.widget-title::before {
	background: #e63a7a;
	bottom: 0;
	content: "";
	height: 2px;
	position: absolute;
	width: 50px;
}

.sidebar-form form {
	position: relative;
}

.sidebar-form form input {
	background: #fff;
	border: 1px solid #e3e3e3;
	color: #000;
	padding: 10px;
	text-indent: 10px;
	transition: all 0.2s ease 0s;
	width: 100%;
}

.sidebar-form form input::placeholder {
	color: rgb(99, 99, 99);
	font-size: 12px;
}

.sidebar-form form button {
	background: #e63a7a;
	border: 0 none;
	color: #fff;
	font-size: 20px;
	height: 100%;
	position: absolute;
	right: 0;
	top: 0;
	transition: all 0.3s ease 0s;
	width: 50px;
	line-height: 50px;
	cursor: pointer;
}

.sidebar-form form button:hover {
	background: #e63a7a;
}

.sidebar-rc-post ul li {
	border-bottom: 1px solid #ddd;
	color: #000;
	display: block;
	font-size: 13px;
	margin-bottom: 15px;
	overflow: hidden;
	padding-bottom: 15px;
}

.sidebar-rc-post ul li:last-child {
	border: 0;
	margin: 0;
	padding: 0;
	;
}

.sidebar-rc-post .rc-post-thumb {
	display: inline-block;
	float: left;
	height: 70px;
	overflow: hidden;
	position: relative;
	width: 90px;
}

.sidebar-rc-post .rc-post-content {
	margin-left: 105px;
}

.sidebar-rc-post .rc-post-content h4 {
	color: #000;
	display: block;
	font-size: 16px;
	line-height: 1.4;
	margin: 0;
	text-indent: 0;
	transition: all 0.2s ease 0s;
}

.sidebar-rc-post .rc-post-content h4 a:hover {
	color: #e63a7a;
}

.widget-date {
	color: #777;
	display: inline-block;
	float: left;
	font-size: 12px;
	line-height: 1;
	margin: 7px 5px 0 0;
	padding: 0 4px 0 0;
	text-decoration: none;
	text-indent: 0;
	text-transform: uppercase;
	font-weight: 500;
}

.sidebar-blog .widget {
	margin-bottom: 35px;
}

.sidebar-blog .widget:last-child {
	margin-bottom: 0;
}

.blog-content p {
	color: #000;
}

.widget-social a {
	background: #000 none repeat scroll 0 0;
	color: #fff;
	display: inline-block;
	font-size: 19px;
	height: 45px;
	line-height: 45px;
	margin-right: 12px;
	text-align: center;
	width: 45px;
}

.widget-social a.facebook {
	background: #3b5998
}

.widget-social a.twitter {
	background: #1da1f2
}

.widget-social a.instagram {
	background: #dd4b39
}

.widget-social a.googleplus {
	background: #dd4b39
}

.widget-social a.linkedin {
	background: #0077b5
}

.widget-social a:hover {
	opacity: 0.8;
}

.sidebar-link li {
	border-bottom: 1px solid #ddd;
	color: #000;
	display: block;
	font-size: 13px;
	line-height: 20px;
	margin-bottom: 10px;
	padding-bottom: 10px;
}

.sidebar-link li:last-child {
	border: 0;
	padding: 0;
	margin: 0;
}

.sidebar-link li a {
	color: #000;
	font-weight: bold;
	padding-left: 5px;
}

.sidebar-link li a:hover {
	color: #e63a7a;
}

.sidebar-link li span {
	float: right;
}

.widget-banner img {
	width: 100%;
}

.instagram-link {
	margin: 0 -5px
}

.instagram-link li {
	float: left;
	margin-bottom: 10px;
	padding: 0 5px;
	width: 33.33%;
}

.sidebar-tad li {
	float: left;
	margin-bottom: 5px;
	margin-right: 5px;
}

.sidebar-tad li a {
	background: #fff;
	color: #222;
	display: inline-block;
	font-size: 11px;
	font-weight: 600;
	line-height: 1;
	padding: 10px 15px;
	text-transform: uppercase;
}

.sidebar-tad li a:hover {
	background: #e63a7a;
	color: #fff
}

/* blog column */
.blog-column .blog-title {
	font-size: 18px;
	line-height: 1.5;
}

.blog-2-column .blog-title {
	font-size: 22px;
	line-height: 1.5;
}

.pagination-col .pagination {
	margin-top: 0;
}

/* blog details */
.blog-details .meta-info {
	margin-bottom: 20px;
}

.blog-details .widget-social {
	margin-bottom: 20px;
}

blockquote {
	margin: 0 0 1rem;
	border-left: 4px solid #e63a7a;
	padding-left: 12px;
	color: #133046;
	font-weight: 700;
	font-style: italic;
}

/* blog details */
.blog-title.blog-title-sm {
	font-size: 20px;
}

.blog-content-img img {
	width: 100%;
}

.blog-post-tag>a {
	border: 1px solid #b7b4b4;
	color: #444;
	display: inline-block;
	font-size: 12px;
	padding: 10px;
	margin-right: 3px;
}

.blog-post-tag>a:hover {
	background: #e63a7a;
	border-color: #e63a7a;
	color: #fff;
}

.blog-share-icon>span {
	color: #595959;
	font-size: 15px;
}

.blog-share-icon>a {
	color: #595959;
	font-size: 15px;
	margin: 0 8px;
}

.blog-share-icon>a:hover {
	color: #e63a7a;
}

.blog-share-icon {
	margin-top: 8px;
}

/* blog comments */
.comments-avatar {
	float: left;
	width: 103px;
}

.comments-text {
	overflow: hidden;
	padding-left: 30px;
}

.avatar-name>h5 {
	float: left;
	font-size: 18px;
	margin: 0;
}

.avatar-name>span {
	color: #646464;
	float: right;
	font-size: 12px;
}

.avatar-name {
	margin-bottom: 10px;
	overflow: hidden;
}

.comments-box {
	margin-bottom: 50px;
}

.comments-reply {
	padding-left: 130px;
}

.comments-text>p {
	color: #000;
	font-size: 14px;
	line-height: 24px;
	margin-bottom: 25px;
}

.comments-text>a {
	border: 1px solid #bdbdbd;
	color: #444;
	display: inline-block;
	font-size: 13px;
	font-weight: 500;
	padding: 7px 20px;
	text-transform: uppercase;
}

.comments-text>a:hover {
	background: #e63a7a;
	border-color: #e63a7a;
	color: #fff;
}

.post-comments-form input {
	border: 1px solid #e6e6e6;
	height: 45px;
	margin-bottom: 25px;
	padding: 0 15px;
	width: 100%;
}

.post-comments-form textarea {
	border: 1px solid #e6e6e6;
	height: 130px;
	margin-bottom: 25px;
	padding: 15px;
	width: 100%;
}

/* 15. Pagination */
.pagination {
	display: block;
	margin: 0;
	overflow: hidden;
	border-radius: 0;
	padding-bottom: 3px;
}

.pagination ul {
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
	list-style: none;
	padding: 0;
	margin: 0;
}

.pagination ul>li {
	display: inline-block;
	margin-right: 2px;
}

.pagination ul>li a:hover {
	color: #fff;
}

.pagination ul>li a:hover span {
	color: #fff;
}

.pagination ul>li.active a span {
	color: #fff;
}

.pagination ul>li>a,
.pagination ul>li>span {
	float: none
}

.pagination ul>li>a {
	background: #FFF;
	font-size: 14px;
	color: #3b3b3b;
	font-weight: 700;
	display: inline-block;
	padding: 9px 18px;
	border: 1px solid #ddd;
}

.pagination ul>li>a:hover,
.pagination ul>li>a:focus,
.pagination ul>.active>a,
.pagination ul>.active>span {
	color: #fff;
	background: #e63a7a;
	border-color: #e63a7a;
}

.maps-area {
	overflow: hidden;
}

/* 13. Contact */
.contact-person h4 {
	margin-bottom: 10px;
}

.contact-person p {
	margin-bottom: 10px;
}

.contact-person a {
	font-size: 14px;
	color: #ff5e14;
}

.contact-person a i {
	padding-right: 5px;
}

.maps {
	height: 420px;
	background-size: cover;
	background-position: center;
	cursor: pointer;
}

.contact-form-title h2 {
	font-weight: 700;
	margin-bottom: 10px;
}

.contact-form-title p {
	font-style: italic;
	width: 60%;
	margin: 0 auto 55px;
}

.contact-form input {
	width: 100%;
	border: 2px solid #eee;
	box-shadow: none;
	padding: 7px 20px;
	color: #999999;
}

.contact-form textarea {
	width: 100%;
	border: 2px solid #eee;
	box-shadow: none;
	padding: 7px 20px;
	color: #999999;
	height: 150px;
}

.contact-form button {
	padding: 12px 34px;
}

/* 12. footer */
.footer-bg {
	background-position: center;
	background-size: cover;
}

.footer-menu ul li {
	display: inline-block;
	margin-right: 25px;
}

.footer-menu ul li a {
	font-size: 16px;
	font-weight: 700;
	color: #133046;
}

.footer-menu ul li a:hover {
	color: #e63a7a;
}

.copyright-text p {
	margin-bottom: 0;
	color: #818181;
	font-size: 20px;
	font-weight: 500;
}

.footer-social ul li {
	display: inline-block;
	margin-left: 15px;
}

.footer-social ul li a {
	font-size: 17px;
	color: #3b3535;
	font-weight: 500;
	line-height: 3;
}

.footer-social ul li a:hover {
	color: #e63a7a;
}

.footer-shape {
	position: absolute;
}

.footer-shape.f-shape1 {
	top: 50%;
	transform: translateY(-50%);
	left: 15%;
}

.footer-shape.f-shape2 {
	left: 49%;
	top: 0;
}

.footer-shape.f-shape2 img {
	width: 135px;
}

/* 13. newsletter */
.newsletter-popup {
	background-color: rgba(0, 0, 0, 0.75);
	position: fixed;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-flex;
	display: -ms-flexbox;
	display: flex;
	z-index: 9999999;
	padding: 10px;
}

.newsletter-popup-modal {
	margin: auto;
	background-color: #fff;
	position: relative;
	max-width: 800px;
	min-height: 365px;
	width: 100%;
}

.custom-close {
	position: absolute;
	right: 30px;
	top: 30px;
	font-size: 16px;
	display: block;
	color: #133046 !important;
	font-weight: 300;
	z-index: 9;
}

.popup-content {
	background-position: left center;
	background-repeat: no-repeat;
	height: 365px;
	display: flex;
	align-items: center;
	justify-content: end;
}

.popup-body {
	width: 335px;
	margin-right: 35px;
}

.popup-body h2 {
	font-size: 40px;
	margin-bottom: 20px;
}

.popup-body p {
	margin-bottom: 0;
	padding-right: 60px;
}

.popup-form input {
	width: 100%;
	border: 1px solid #c3c3c3;
	padding: 16px 20px;
	margin-bottom: 35px;
}

.news-item {
	border: 1px solid #ddd;
	border-radius: 5px;
	overflow: hidden;
	margin-bottom: 20px;
}
.news-thumb img {
	width: 100%;
	height: 230px;
	object-fit: cover;
}
.news-thumb img:hover {
	transform: scale(1.15);
	transition: transform 0.3s ease-in-out;
}
.news-content {
	padding: 10px;
}
.news-content h4 {
	margin-top: 0;
	font-size: 18px;
	font-weight: bold;
}
.news-content p {
	margin: 0;
	font-size: 14px;
}
.read-more {
	display: inline-block;
	margin-top: 10px;
	color: #007bff;
	text-decoration: none;
}
.read-more:hover {
	text-decoration: underline;
}