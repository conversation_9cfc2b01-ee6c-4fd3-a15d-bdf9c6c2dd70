<template>
  <el-col :span="24" style="padding-bottom: 0px; margin-left: 10px">
    <el-form :inline="true" :model="filters">
      <el-form-item>
        <el-input v-model="filters.title" placeholder="反馈主题" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="query" icon="el-icon-upload">搜索</el-button>
      </el-form-item>
    </el-form>
  </el-col>

  <el-table
    :data="datalist"
    border
    stripe
    style="width: 100%"
    v-loading="listLoading"
    highlight-current-row
    max-height="600"
    size="small"
  >
    <el-table-column prop="account" label="账号" align="center"></el-table-column>
    <el-table-column prop="uflag" label="身份" align="center"></el-table-column>
    <el-table-column prop="avatar" label="头像" width="70" align="center">
      <template #default="scope">
        <img
          :src="'http://localhost:8088/TutoringServicePlatform/' + scope.row.avatar"
          style="width: 50px; height: 50px"
        />
      </template>
    </el-table-column>
    <el-table-column prop="title" label="反馈主题" align="center"></el-table-column>

    <el-table-column prop="addtime" label="反馈时间" align="center"></el-table-column>
    <el-table-column prop="adminreply" label="管理员回复" align="center">
      <template #default="scope">
        <span v-if="scope.row.adminreply != null">{{ scope.row.adminreply.substring(0, 5) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="操作" min-width="200" align="center">
      <template #default="scope">
        <el-button
          type="primary"
          size="mini"
          @click="handleShow(scope.$index, scope.row)"
          icon="el-icon-zoom-in"
          style="padding: 3px 6px 3px 6px"
          >详情</el-button
        >
        <el-button
          type="danger"
          size="mini"
          @click="handleDelete(scope.$index, scope.row)"
          icon="el-icon-delete"
          style="padding: 3px 6px 3px 6px"
          >删除</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    @current-change="handleCurrentChange"
    :current-page="page.currentPage"
    :page-size="page.pageSize"
    background
    layout="total, prev, pager, next, jumper"
    :total="page.totalCount"
    style="float: right; margin: 10px 20px 0 0"
  ></el-pagination>
</template>
<script>
import request, { base } from '../../../utils/http';
export default {
  name: 'feedback',
  components: {},
  data() {
    return {
      filters: {
        //列表查询参数
        account: '',
        title: '',
      },

      page: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页显示条目个数
        totalCount: 0, // 总条目数
      },
      isClear: false,

      listLoading: false, //列表加载状态
      btnLoading: false, //保存按钮加载状态
      datalist: [], //表格数据
    };
  },
  created() {
    this.getDatas();
  },

  methods: {
    // 删除留言反馈
    handleDelete(index, row) {
      this.$confirm('确认删除该记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.listLoading = true;
          let url = base + '/feedback/del?id=' + row.id;
          request.post(url).then((res) => {
            this.listLoading = false;

            this.$message({
              message: '删除成功',
              type: 'success',
              offset: 320,
            });
            this.getDatas();
          });
        })
        .catch(() => {});
    },

    // 分页
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getDatas();
    },

    //获取列表数据
    getDatas() {
      var sf = sessionStorage.getItem('sf');
      let para = {
        account: sessionStorage.getItem('lname'),
        uflag: sf,
        title: this.filters.title,
      };
      this.listLoading = true;
      let url =
        base +
        '/feedback/list?currentPage=' +
        this.page.currentPage +
        '&pageSize=' +
        this.page.pageSize;
      request.post(url, para).then((res) => {
        if (res.resdata.length > 0) {
          this.isPage = true;
        } else {
          this.isPage = false;
        }
        this.page.totalCount = res.count;
        this.datalist = res.resdata;
        this.listLoading = false;
      });
    },
    //查询
    query() {
      this.getDatas();
    },

    // 查看
    handleShow(index, row) {
      this.$router.push({
        path: '/Feedback_Show',
        query: {
          id: row.id,
        },
      });
    },

    // 编辑
    handleEdit(index, row) {
      this.$router.push({
        path: '/Feedback_Edit',
        query: {
          id: row.id,
        },
      });
    },
  },
};
</script>

<style></style>
