<template>
  <div class="appointment-form">
    <el-form :model="formData" label-width="120px" ref="formDataRef" :rules="addrules" align="left">
      <el-form-item label="家教教师">
        <el-input v-model="formData.tname" disabled style="width: 50%"></el-input>
      </el-form-item>

      <el-form-item label="收费标准/时">
        <el-input v-model="formData.price" disabled style="width: 50%">
          <template slot="append">元</template>
        </el-input>
      </el-form-item>

      <el-form-item label="预约课时" prop="hours">
        <el-input-number
          v-model="formData.hours"
          :min="1"
          :max="100"
          @change="calculateAmount"
          style="width: 200px"
        ></el-input-number>
        <span class="unit">小时</span>
      </el-form-item>

      <el-form-item label="总金额">
        <b style="font-size: 18px; color: red">{{ formData.amount }}</b
        >元
      </el-form-item>

      <el-form-item label="备注说明" prop="remarks">
        <el-input
          type="textarea"
          :rows="5"
          v-model="formData.remarks"
          placeholder="请输入具体的预约要求，如上课时间、地点等"
          style="width: 80%"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          size="medium"
          @click="save"
          :loading="btnLoading"
          icon="el-icon-check"
        >
          提交预约
        </el-button>
        <el-button type="info" size="medium" @click="back" icon="el-icon-back"> 返回 </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import request, { base } from '../../../utils/http';

export default {
  name: 'Orderinfo_Add',
  data() {
    return {
      btnLoading: false,
      formData: {
        oid: '', // 订单编号
        account: '', // 家长账号
        taccount: '', // 家教账号
        tname: '', // 家教姓名
        price: '', // 收费标准
        hours: 1, // 预约课时
        amount: '', // 总金额
        remarks: '', // 备注说明
        status: '待接单', // 接单状态
        submittime: '', // 提交时间
      },
      addrules: {
        hours: [
          { required: true, message: '请输入预约课时', trigger: 'blur' },
          { type: 'number', message: '课时必须为数字' },
        ],
        remarks: [{ required: true, message: '请输入备注说明', trigger: 'blur' }],
      },
    };
  },
  created() {
    this.initData();
  },
  methods: {
    // 初始化数据
    initData() {
      const tid = this.$route.query.tid;
      const tname = this.$route.query.tname;

      // 获取家教信息
      request.post(base + '/tutors/get?id=' + tid).then((res) => {
        if (res.code === 200 && res.resdata) {
          this.formData.price = res.resdata.price;
          this.formData.tname = tname;
          this.formData.taccount = tid;
          this.calculateAmount();
        }
      });

      // 设置家长账号
      this.formData.account = sessionStorage.getItem('lname');

      // 生成订单编号：当前时间戳
      this.formData.oid = 'DD' + new Date().getTime();

      // 设置提交时间
      this.formData.submittime = this.formatDate(new Date());
    },

    // 计算总金额
    calculateAmount() {
      this.formData.amount = (this.formData.price * this.formData.hours).toFixed(2);
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 提交预约
    save() {
      this.$refs['formDataRef'].validate((valid) => {
        if (valid) {
          let url = base + '/orderinfo/add';
          this.btnLoading = true;

          this.formData.status = '待接单';

          request.post(url, this.formData).then((res) => {
            this.btnLoading = false;
            if (res.code == 200) {
              this.$message({
                message: '预约提交成功，请等待家教确认',
                type: 'success',
                offset: 320,
              });
              this.$router.push('/orderinfo_manage');
            } else {
              this.$message({
                message: res.msg || '服务器错误',
                type: 'error',
                offset: 320,
              });
            }
          });
        }
      });
    },

    // 返回
    back() {
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped>
.appointment-form {
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.unit {
  margin-left: 10px;
  color: #606266;
}

.el-input-number {
  width: 200px;
}

.el-form-item {
  margin-bottom: 25px;
}

.el-button {
  padding: 12px 25px;
}

.el-button + .el-button {
  margin-left: 15px;
}
</style>
