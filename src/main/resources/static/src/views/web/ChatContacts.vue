<template>
  <div class="chat-contacts">
    <h2 class="page-title">我的聊天联系人</h2>

    <el-card class="contact-list-card" v-loading="loading">
      <div v-if="contacts.length === 0" class="empty-state">
        <i class="el-icon-chat-dot-round empty-icon"></i>
        <p>暂无聊天联系人</p>
        <el-button type="primary" @click="goToTutorsList">浏览家教列表</el-button>
      </div>

      <el-table v-else :data="contacts" style="width: 100%">
        <el-table-column label="联系人" align="center" width="180">
          <template #default="scope">
            <div class="contact-info">
              <el-avatar :size="40" :src="scope.row.avatar || defaultAvatar"></el-avatar>
              <span class="contact-name">{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="身份" prop="role" align="center" width="120"></el-table-column>

        <el-table-column label="最后交流时间" prop="lastChatTime" align="center" width="180"></el-table-column>

        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="startChat(scope.row)"
              icon="el-icon-chat-dot-round">
              开始聊天
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 聊天对话框 -->
    <ChatDialog
      v-model:visible="chatVisible"
      :to-user="selectedUser.account"
      :to-name="selectedUser.name"
    />
  </div>
</template>

<script>
import request, { base } from '../../../utils/http';
import ChatDialog from '../../components/ChatDialog.vue';

export default {
  name: 'ChatContacts',
  components: {
    ChatDialog
  },
  data() {
    return {
      contacts: [],
      loading: false,
      chatVisible: false,
      selectedUser: {
        account: '',
        name: ''
      },
      defaultAvatar: 'https://cube.elemecdn.com/3/7c/********************************.png'
    };
  },
  created() {
    this.getContacts();
  },
  methods: {
    // 获取联系人列表
    getContacts() {
      this.loading = true;
      const currentUser = sessionStorage.getItem('lname');

      if (!currentUser) {
        this.$message.warning('请先登录');
        this.$router.push('/ulogin');
        return;
      }

      request.post(base + '/api/chatinfo/contacts?account=' + currentUser).then(async (res) => {
        this.loading = false;
        if (res.code === 0 || res.code === 200) {
          // 获取联系人详细信息
          const contactAccounts = res.resdata || [];
          const contactsPromises = contactAccounts.map(account => this.getContactInfo(account));

          try {
            const contactsInfo = await Promise.all(contactsPromises);
            this.contacts = contactsInfo.filter(contact => contact !== null);
          } catch (error) {
            console.error('获取联系人信息失败:', error);
            this.$message.error('获取联系人信息失败');
          }
        } else {
          console.error('获取联系人列表失败:', res);
          this.$message.error(res.msg || '获取联系人列表失败');
        }
      }).catch(error => {
        this.loading = false;
        console.error('获取联系人列表异常:', error);
        this.$message.error('获取联系人列表失败');
      });
    },

    // 获取联系人详细信息
    async getContactInfo(account) {
      const sf = sessionStorage.getItem('sf');

      try {
        // 根据当前用户身份确定联系人身份和API
        let contactInfo = null;

        if (sf === '家长') {
          // 当前用户是家长，联系人是家教
          const res = await request.get(base + '/api/tutors/get?id=' + account);
          if ((res.code === 0 || res.code === 200) && res.resdata) {
            contactInfo = {
              account: res.resdata.taccount,
              name: res.resdata.tuname || res.resdata.taccount,
              avatar: res.resdata.photo,
              role: '家教',
              lastChatTime: await this.getLastChatTime(account)
            };
          }
        } else {
          // 当前用户是家教，联系人是家长
          const res = await request.get(base + '/api/users/get?id=' + account);
          if ((res.code === 0 || res.code === 200) && res.resdata) {
            contactInfo = {
              account: res.resdata.account,
              name: res.resdata.uname || res.resdata.account,
              avatar: res.resdata.avatar,
              role: '家长',
              lastChatTime: await this.getLastChatTime(account)
            };
          }
        }

        return contactInfo;
      } catch (error) {
        console.error(`获取联系人 ${account} 信息失败:`, error);
        return null;
      }
    },

    // 获取最后聊天时间
    async getLastChatTime(contactAccount) {
      const currentUser = sessionStorage.getItem('lname');

      try {
        const para = {
          condition: ` and ((lname="${currentUser}" and lname2="${contactAccount}") or (lname="${contactAccount}" and lname2="${currentUser}"))`,
        };

        const res = await request.get(base + '/api/chatinfo/list?currentPage=1&pageSize=1&condition=' + encodeURIComponent(para.condition));

        if ((res.code === 0 || res.code === 200) && res.resdata && res.resdata.length > 0) {
          // 按时间倒序排序
          const messages = res.resdata.sort((a, b) => new Date(b.sendtime) - new Date(a.sendtime));
          return messages[0].sendtime;
        }

        return '暂无记录';
      } catch (error) {
        console.error('获取最后聊天时间失败:', error);
        return '暂无记录';
      }
    },

    // 开始聊天
    startChat(contact) {
      this.selectedUser = {
        account: contact.account,
        name: contact.name
      };
      this.chatVisible = true;
    },

    // 跳转到家教列表
    goToTutorsList() {
      this.$router.push('/tutorslist');
    }
  }
};
</script>

<style scoped>
.chat-contacts {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-title {
  margin-bottom: 20px;
  color: #303133;
  font-size: 22px;
  font-weight: 600;
}

.contact-list-card {
  margin-top: 20px;
}

.contact-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-name {
  margin-left: 10px;
  font-weight: 500;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-icon {
  font-size: 60px;
  color: #909399;
  margin-bottom: 20px;
}

.empty-state p {
  color: #909399;
  font-size: 16px;
  margin-bottom: 20px;
}
</style>
