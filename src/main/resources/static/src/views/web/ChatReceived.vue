<template>
  <div class="chat-received">
    <el-table :data="chatList" border stripe style="width: 100%" v-loading="listLoading">
      <el-table-column prop="lname" label="发送人" align="center" width="180"></el-table-column>
      <el-table-column prop="content" label="最新消息" align="left"></el-table-column>
      <el-table-column
        prop="sendtime"
        label="发送时间"
        align="center"
        width="180"
      ></el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click="handleChat(scope.row)"
            icon="el-icon-chat-dot-round"
          >
            回复
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 聊天对话框 -->
    <ChatDialog
      v-model:visible="chatVisible"
      :to-user="selectedUser.account"
      :to-name="selectedUser.name"
    />
  </div>
</template>

<script>
import request, { base } from '../../../utils/http';
import ChatDialog from '../../components/ChatDialog.vue';

export default {
  name: 'ChatReceived',
  components: {
    ChatDialog,
  },
  data() {
    return {
      chatList: [],
      listLoading: false,
      chatVisible: false,
      selectedUser: {
        account: '',
        name: '',
      },
      timer: null, // 添加定时器变量
    };
  },
  created() {
    this.getLatestChats();
  },
  methods: {
    // 获取最新聊天记录
    getLatestChats() {
      this.listLoading = true;
      const currentUser = sessionStorage.getItem('lname');

      let para = {
        condition: ` and lname2='${currentUser}' and lname!='${currentUser}'`, // 查询别人发给当前用户的消息
      };

      request.get(base + '/api/chatinfo/list?currentPage=1&pageSize=1000&condition=' + encodeURIComponent(para.condition)).then((res) => {
        this.listLoading = false;
        if (res.code === 0 || res.code === 200) {
          // 使用 Map 来保存每个发送者的最新消息
          const messageMap = new Map();
          if (res.resdata && Array.isArray(res.resdata)) {
            res.resdata.forEach((msg) => {
              const existingMsg = messageMap.get(msg.lname);
              if (!existingMsg || new Date(msg.sendtime) > new Date(existingMsg.sendtime)) {
                messageMap.set(msg.lname, msg);
              }
            });

            // 转换为数组
            this.chatList = Array.from(messageMap.values());

            // 按时间倒序排序
            this.chatList.sort((a, b) => new Date(b.sendtime) - new Date(a.sendtime));
          } else {
            this.chatList = [];
            console.warn('聊天记录为空或格式不正确');
          }
        } else {
          console.error('获取聊天记录失败:', res);
        }
      }).catch(error => {
        this.listLoading = false;
        console.error('获取聊天记录异常:', error);
      });
    },

    // 处理聊天
    handleChat(row) {
      // 获取发送者的身份
      const sf = sessionStorage.getItem('sf');
      const name = row.lname;

      this.selectedUser = {
        account: row.lname,
        name: name,
      };
      this.chatVisible = true;

      // 打开聊天窗口后启动定时刷新
      this.startPolling();
    },

    // 开始轮询
    startPolling() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        this.getLatestChats();
      }, 3000);
    },

    // 停止轮询
    stopPolling() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
  },
  beforeUnmount() {
    this.stopPolling();
  },
};
</script>

<style scoped>
.chat-received {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-table {
  margin-top: 20px;
}

/* 消息内容最多显示两行 */
.el-table .cell {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
</style>
