<template>
  <div class="ai-container">
    <div class="chat-header">
      <h2 style="color: #fff;">AI小智~</h2>
      <p style="color: #fff;">有任何学习问题都可以向小智提问~</p>
    </div>

    <div class="chat-content" ref="chatContent">
      <div v-for="(msg, index) in messageList" :key="index"
        :class="['message-item', msg.type === 'user' ? 'user-message' : 'ai-message']">
        <div class="avatar">
          <i :class="msg.type === 'user' ? 'el-icon-user' : 'el-icon-s-custom'"></i>
        </div>
        <div class="message-content">
          <div class="message-text" v-html="formatMessage(msg.content)"></div>
          <div class="message-time">{{ msg.time }}</div>
        </div>
      </div>

      <div v-if="loading" class="ai-message">
        <div class="avatar">
          <i class="el-icon-s-custom"></i>
        </div>
        <div class="message-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <div class="chat-input">
      <el-input type="textarea" :rows="3" placeholder="请输入您的问题，按Enter发送（Shift+Enter换行）" v-model="inputMessage"
        @keyup.enter.native="handleEnter"></el-input>

      <div class="input-actions">
        <el-button type="primary" @click="sendMessage" >发送</el-button>
        <el-button @click="clearChat">清空对话</el-button>
      </div>
    </div>
  </div>
</template>

<script>
  import axios from 'axios';

  export default {
    name: 'Ai',
    data() {
      return {
        inputMessage: '',
        messageList: [],
        loading: false,
        // 智谱AI配置
        ZHIPUAI_API: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
        API_KEY: 'cdbe3f5e608394a1353e9ce27397f27b.yOqjVhOmozryAm3f'
      };
    },
    created() {
      // 初始欢迎消息
      this.addMessage({
        type: 'ai',
        content: '您好！我是AI小智，很高兴见到您，有任何学习问题都可以向我提问哦~'
      });
    },
    methods: {
      // 发送消息
      async sendMessage() {
        if (!this.inputMessage.trim()) return;

        // 添加用户消息到列表
        const userMessage = this.inputMessage;
        this.addMessage({
          type: 'user',
          content: userMessage
        });

        this.inputMessage = '';
        this.loading = true;

        try {
          // 调用智谱AI接口
          const response = await axios({
            method: 'post',
            url: this.ZHIPUAI_API,
            headers: {
              'Authorization': `Bearer ${this.API_KEY}`,
              'Content-Type': 'application/json'
            },
            data: {
              model: "glm-4-flash",
              messages: [
                {
                  role: "system",
                  content: "你是一位专业的家教助手，主要提供学习方面的帮助。你的回答应该专业、友好，并且尽可能提供有用的教学建议和解答。"
                },
                {
                  role: "user",
                  content: userMessage
                }
              ]
            }
          });

          // 处理AI回复
          const aiReply = response.data.choices[0].message.content;
          this.addMessage({
            type: 'ai',
            content: aiReply
          });
        } catch (error) {
          console.error('AI回复失败:', error);
          this.addMessage({
            type: 'ai',
            content: '抱歉，我暂时无法回答您的问题，请稍后再试。'
          });
        } finally {
          this.loading = false;
          this.scrollToBottom();
        }
      },

      // 添加消息到列表
      addMessage(msg) {
        const now = new Date();
        const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

        this.messageList.push({
          ...msg,
          time: timeStr
        });

        this.$nextTick(() => {
          this.scrollToBottom();
        });
      },

      // 滚动到底部
      scrollToBottom() {
        if (this.$refs.chatContent) {
          this.$refs.chatContent.scrollTop = this.$refs.chatContent.scrollHeight;
        }
      },

      // 处理Enter键
      handleEnter(e) {
        if (e.shiftKey) return; // Shift+Enter允许换行
        e.preventDefault();
        this.sendMessage();
      },

      // 清空对话
      clearChat() {
        this.$confirm('确定要清空所有对话记录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.messageList = [];
          // 重新添加欢迎消息
          this.addMessage({
            type: 'ai',
            content: '您好！我是AI小智，很高兴见到您，有任何学习问题都可以向我提问哦~'
          });
        }).catch(() => { });
      },

      // 格式化消息内容（处理换行和代码块）
      formatMessage(content) {
        if (!content) return '';

        // 处理换行
        let formatted = content.replace(/\n/g, '<br>');

        // 处理代码块
        formatted = formatted.replace(/```([\s\S]*?)```/g, '<pre class="code-block">$1</pre>');

        return formatted;
      }
    }
  };
</script>

<style scoped>
  .ai-container {
    display: flex;
    flex-direction: column;
    height: 65vh;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 20px;
  }

  .chat-header {
    padding: 15px 20px;
    background-color: #294be0;
    color: white;
  }

  .chat-header h2 {
    margin: 0;
    font-size: 18px;
  }

  .chat-header p {
    margin: 5px 0 0;
    font-size: 14px;
    opacity: 0.8;
  }

  .chat-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #f5f5f5;
  }

  .message-item {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
  }

  .user-message {
    flex-direction: row-reverse;
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin: 0 10px;
  }

  .user-message .avatar {
    background-color: #22d2bb;
    color: white;
  }

  .ai-message .avatar {
    background-color: #207ae9;
    color: white;
  }

  .message-content {
    max-width: 70%;
    border-radius: 8px;
    padding: 10px 15px;
    position: relative;
  }

  .user-message .message-content {
    background-color: #ecf5ff;
    margin-right: 5px;
  }

  .ai-message .message-content {
    background-color: white;
    margin-left: 5px;
  }

  .message-text {
    word-break: break-word;
    line-height: 1.5;
  }

  .message-time {
    font-size: 12px;
    color: #0b0c0c;
    margin-top: 5px;
    text-align: right;
  }

  .typing-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
  }

  .typing-indicator span {
    height: 8px;
    width: 8px;
    border-radius: 50%;
    background-color: #090909;
    margin: 0 3px;
    animation: bounce 1.5s infinite ease-in-out;
  }

  .typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes bounce {

    0%,
    60%,
    100% {
      transform: translateY(0);
    }

    30% {
      transform: translateY(-8px);
    }
  }

  .chat-input {
    padding: 15px 20px;
    background-color: rgb(255, 255, 255);
    border-top: 1px solid #EBEEF5;
  }

  .input-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }

  .code-block {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 10px;
    font-family: monospace;
    white-space: pre-wrap;
    margin: 10px 0;
  }
</style>