<template>
  <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="rules" align="left">
    <el-form-item label="姓名" prop="tuname">
      <el-input v-model="formData.tuname" placeholder="姓名" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="性别" prop="gender">
      <el-radio-group v-model="formData.gender">
        <el-radio label="男"> 男 </el-radio>
        <el-radio label="女"> 女 </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="年龄" prop="age">
      <el-input v-model="formData.age" placeholder="年龄" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="手机号码" prop="phone">
      <el-input v-model="formData.phone" placeholder="手机号码" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="电子邮箱" prop="email">
      <el-input v-model="formData.email" placeholder="电子邮箱" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="学历" prop="education">
      <el-input v-model="formData.education" placeholder="学历" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="教学经验" prop="teachingexperience">
      <el-input
        v-model="formData.teachingexperience"
        placeholder="教学经验"
        style="width: 50%"
      ></el-input>
    </el-form-item>
    <el-form-item prop="photo" label="照片" min-width="20%">
      <el-avatar
        :src="formData.photo ? base.replace('/api', '') + '/' + formData.photo : ''"
        shape="square"
        :size="100"
        :fit="fit"
      ></el-avatar>
      <el-button type="primary" size="small" @click="showUpload">上传</el-button>
    </el-form-item>
    <el-form-item label="家教类型" prop="catid">
      <el-select v-model="formData.catid" placeholder="请选择" size="small">
        <el-option
          v-for="item in categorysList"
          :key="item.catid"
          :label="item.catname"
          :value="item.catid"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="家教科目" prop="subval">
      <el-select
        v-model="formData.selectedSubjects"
        multiple
        placeholder="请选择家教科目"
        style="width: 50%"
        @change="handleSubjectsChange"
      >
        <el-option
          v-for="item in subjectsList"
          :key="item.subid"
          :label="item.subname"
          :value="item.subid"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="执教方式" prop="tflag2">
      <el-radio-group v-model="formData.tflag2">
        <el-radio label="线下"> 线下 </el-radio>
        <el-radio label="线上"> 线上 </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="收费标准/时" prop="price">
      <el-input v-model="formData.price" placeholder="收费标准/时" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="个人介绍" prop="introduction">
      <wang-editor
        ref="wangEditorRef"
        v-model="formData.introduction"
        :config="editorConfig"
        :isClear="isClear"
        @change="editorChange"
      ></wang-editor>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        size="small"
        @click="save"
        :loading="btnLoading"
        icon="el-icon-upload"
        >保 存</el-button
      >
    </el-form-item>
  </el-form>
  <el-dialog
    v-model="uploadVisible"
    title="附件上传"
    custom-class="el-dialog-widthSmall"
    @close="closeDialog"
  >
    <div>
      <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>
    </div>
    <el-upload
      action="http://localhost:8088/TutoringServicePlatform/api/common/uploadFile"
      style="margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px"
      drag
      :limit="1"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :file-list="fileList"
      :on-exceed="handleExceed"
      :auto-upload="false"
      name="file"
      :on-change="fileListChange"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将数据文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip">
        <div
          style="display: inline; color: #d70000; font-size: 14px"
          class="uploadFileWarning"
          id="uploadFileWarning"
        ></div>
      </div>
    </el-upload>
    <span class="dialog-footer">
      <el-button @click="hideUpload">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">提 交</el-button>
    </span>
  </el-dialog>
</template>
<script>
import request, { base } from '../../../utils/http';
import WangEditor from '../../components/WangEditor';

export default {
  name: 'Tinfo',
  components: {
    'wang-editor': WangEditor,
  },
  data() {
    return {
      editorConfig: {},
      formData: {},
      isClear: false,
      subjectsList: [], // 科目列表
      categorysList: [], // 家教类型列表
      rules: {
        taccount: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        tuname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        gender: [{ required: true, message: '请输入性别', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },
        ],
        email: [
          { required: true, message: '请输入电子邮箱', trigger: 'blur' },
          {
            pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/,
            message: '电子邮箱格式不正确',
            trigger: 'blur',
          },
        ],
        education: [{ required: true, message: '请输入学历', trigger: 'blur' }],
        teachingexperience: [{ required: true, message: '请输入教学经验', trigger: 'blur' }],
        photo: [{ required: true, message: '请上传照片', trigger: 'blur' }],
        catid: [{ required: true, message: '请选择家教类型', trigger: 'onchange' }],
        subval: [{ required: true, message: '请选择家教科目', trigger: 'change' }],
        price: [{ required: true, message: '请输入收费标准/时', trigger: 'blur' }],
      },
      btnLoading: false,
      uploadVisible: false,
    };
  },

  created() {
    this.getinfo();
    this.getSubjectsList();
    this.getcategorysList();
  },

  methods: {
    // 获取科目列表
    getSubjectsList() {
      let para = {};
      let url = base + '/subjects/list?currentPage=1&pageSize=1000';
      request.post(url, para).then((res) => {
        this.subjectsList = res.resdata;
      });
    },

    // 获取家教类型列表
    getcategorysList() {
      let para = {};
      let url = base + '/categorys/list?currentPage=1&pageSize=1000';
      request.post(url, para).then((res) => {
        this.categorysList = res.resdata;
      });
    },

    // 处理科目选择变化
    handleSubjectsChange(value) {
      // 更新suids和subval
      this.formData.suids = value.join(',');
      const selectedNames = value.map((id) => {
        const subject = this.subjectsList.find((item) => item.subid === id);
        return subject ? subject.subname : '';
      });
      this.formData.subval = selectedNames.join(',');
    },

    // 富文本编辑器内容变化
    editorChange(val) {
      this.formData.introduction = val;
    },

    // 获取用户信息时处理科目数据
    getinfo() {
      var lname = sessionStorage.getItem('lname');
      let url = base + '/tutors/get?id=' + lname;
      request.post(url).then((res) => {
        if (res.code == 200) {
          this.formData = res.resdata;
          this.$refs['wangEditorRef'].editor.txt.html(this.formData.introduction);
          // 处理已选科目
          if (this.formData.suids) {
            this.formData.selectedSubjects = this.formData.suids
              .split(',')
              .map((id) => parseInt(id));
          } else {
            this.formData.selectedSubjects = [];
          }
        } else {
          this.$message({
            message: '服务器错误',
            type: 'error',
            offset: 320,
          });
        }
      });
    },

    //注册
    save() {
      //表单验证
      this.$refs['formDataRef'].validate((valid) => {
        if (valid) {
          var lname = sessionStorage.getItem('lname');
          let url = base + '/tutors/update'; //请求地址
          this.btnLoading = true; //按钮加载状态
          request.post(url, this.formData).then((res) => {
            //请求接口
            this.btnLoading = false; // 取消按钮加载状态
            if (res.code == 200) {
              this.$message({
                message: '操作成功！',
                type: 'success',
                offset: 320,
              });

              sessionStorage.setItem('us', JSON.stringify(res.resdata));
            } else {
              this.$message({
                message: '服务器错误: ' + (res.msg || '未知错误'),
                type: 'error',
                offset: 320,
              });
            }
          }).catch(err => {
            this.btnLoading = false;
            console.error('更新失败:', err);
            this.$message({
              message: '更新失败，请检查网络连接',
              type: 'error',
              offset: 320,
            });
          });
        }
      });
    },

    //显示上传框
    showUpload() {
      this.uploadVisible = true;
    },

    //隐藏上传框
    hideUpload() {
      this.uploadVisible = false;
    },
    //上传
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    handlePreview(file) {
      console.log(file);
    },
    handleExceed(files, fileList) {
      this.$message({
        duration: 1000,
        message: '只能上传一个文件',
        type: 'error',
        offset: 320,
      });
    },
    // 判断上传文件后缀
    fileListChange(file, fileList) {
      let extendFileName = 'png,jpg';
      let extendFileNames = extendFileName.split(',');
      let regExpRules = [];
      for (let i = 0; i < extendFileNames.length; i++) {
        regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));
      }
      let fileNames = [];
      let files = [];
      let that = this;
      fileList.forEach(function (key, val) {
        let ret = false;
        for (let i = 0; i < regExpRules.length; i++) {
          ret = ret || regExpRules[i].test(key['name']);
        }
        if (!ret) {
          console.log(key['name'] + ':' + ret);
          that.$message({
            duration: 1000,
            message: '上传的文件后缀必须为' + extendFileName + '格式！',
            type: 'error',
            offset: 320,
          });
          return false;
        }
        if (fileNames.indexOf(key['name']) != -1) {
          that.$message({
            duration: 1000,
            message: '上传的文件重复！',
            type: 'error',
            offset: 320,
          });
          return false;
        }
        //只能上传一个文件，用最后上传的覆盖
        if (!that.multiFiles) {
          files = [];
          fileNames = [];
        }
        files.push(key);
        fileNames.push(key['name']);
        if (fileNames !== '') {
          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');
        }
        //$(".uploadFileWarning").text("");
      });
      this.files = fileNames;
      this.fileList = files;
    },
    /**
     * 确认按钮
     */
    handleConfirm() {
      let filePath = this.fileList;
      if (filePath.length === 0) {
        this.$message({
          duration: 1000,
          message: '请选择文件！',
          type: 'error',
          offset: 320,
        });
        return false;
      }
      let formData = new FormData();
      this.fileList.forEach((file) => {
        formData.append('file', file.raw, file.raw.name);
      });
      let url = base + '/api/common/uploadFile';
      console.log('url=' + url);
      request.post(url, formData).then((res) => {
        console.log(res);
        if (res.code === 0) {
          let furl = res.resdata.filePath;
          this.formData.photo = furl; // 上传文件的路径
          this.$message({
            message: '照片上传成功！',
            type: 'success',
            offset: 320,
          });
          this.hideUpload();
        } else {
          this.$message({
            message: '照片上传失败：' + res.msg,
            type: 'error',
            offset: 320,
          });
        }
      }).catch(err => {
        console.error(err);
        this.$message({
          message: '照片上传失败，请检查网络连接',
          type: 'error',
          offset: 320,
        });
      });
    },
  },
};
</script>

<style>
.w-e-text-container {
  min-height: 200px !important;
}
</style>
