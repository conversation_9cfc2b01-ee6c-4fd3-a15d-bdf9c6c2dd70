<template>
  <div class="order-submit">
    <h2 class="page-title">提交订单</h2>

    <el-card class="tutor-info-card" v-if="tutorInfo">
      <div class="tutor-header">
        <h3>家教信息</h3>
      </div>

      <div class="tutor-details">
        <div class="tutor-avatar">
          <img :src="tutorInfo.photo ? 'http://localhost:8088/TutoringServicePlatform/' + tutorInfo.photo : defaultAvatar" alt="家教头像">
        </div>

        <div class="tutor-content">
          <div class="info-row">
            <span class="label">姓名：</span>
            <span class="value">{{ tutorInfo.tuname }}</span>
          </div>

          <div class="info-row">
            <span class="label">性别：</span>
            <span class="value">{{ tutorInfo.gender }}</span>
          </div>

          <div class="info-row">
            <span class="label">年龄：</span>
            <span class="value">{{ tutorInfo.age }}岁</span>
          </div>

          <div class="info-row">
            <span class="label">教育背景：</span>
            <span class="value">{{ tutorInfo.education }}</span>
          </div>

          <div class="info-row">
            <span class="label">教学经验：</span>
            <span class="value">{{ tutorInfo.teachingexperience }}</span>
          </div>

          <div class="info-row">
            <span class="label">科目：</span>
            <span class="value">{{ tutorInfo.subval }}</span>
          </div>

          <div class="info-row highlight">
            <span class="label">收费标准：</span>
            <span class="value price">¥{{ tutorInfo.price }}/小时</span>
          </div>
        </div>
      </div>
    </el-card>

    <el-card class="order-form-card">
      <div class="form-header">
        <h3>订单信息</h3>
      </div>

      <el-form :model="orderForm" :rules="rules" ref="orderForm" label-width="100px" class="order-form">
        <el-form-item label="预约课时" prop="hours">
          <el-input-number v-model="orderForm.hours" :min="1" :max="100" @change="calculateAmount"></el-input-number>
          <span class="unit">小时</span>
        </el-form-item>

        <el-form-item label="总金额">
          <div class="amount">¥{{ orderForm.amount.toFixed(2) }}</div>
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input
            type="textarea"
            v-model="orderForm.remarks"
            placeholder="请输入备注信息，如期望上课时间、具体需求等"
            :rows="4"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitOrder" :loading="submitting">提交订单</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单提交成功对话框 -->
    <el-dialog
      title="订单提交成功"
      v-model="successDialogVisible"
      width="400px"
      :before-close="handleDialogClose"
    >
      <div class="success-content">
        <i class="el-icon-success success-icon"></i>
        <p>您的订单已成功提交！</p>
        <p>订单号：{{ submittedOrderId }}</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewOrderList">查看我的订单</el-button>
          <el-button type="primary" @click="handleDialogClose">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import request, { base } from '../../../utils/http';

export default {
  name: 'OrderSubmit',
  data() {
    return {
      tutorInfo: null,
      defaultAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      orderForm: {
        taccount: '',
        hours: 1,
        price: 0,
        amount: 0,
        remarks: ''
      },
      rules: {
        hours: [
          { required: true, message: '请选择预约课时', trigger: 'blur' },
          { type: 'number', min: 1, message: '课时必须大于0', trigger: 'blur' }
        ],
        remarks: [
          { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
        ]
      },
      submitting: false,
      successDialogVisible: false,
      submittedOrderId: ''
    };
  },
  created() {
    // 从URL参数中获取家教账号
    const taccount = this.$route.query.taccount;
    console.log('路由参数中的家教账号:', taccount);

    if (!taccount) {
      this.$message.error('缺少家教账号参数');
      console.error('初始化失败: 缺少家教账号参数');
      this.goBack();
      return;
    }

    this.orderForm.taccount = taccount;
    console.log('设置订单表单中的家教账号:', this.orderForm.taccount);
    this.getTutorInfo(taccount);

    // 检查用户是否登录
    const account = sessionStorage.getItem('lname');
    const sf = sessionStorage.getItem('sf');
    console.log('当前登录用户:', account, '身份:', sf);

    if (!account || !sf) {
      this.$message.warning('请先登录');
      console.error('用户未登录，跳转到登录页面');
      this.$router.push('/ulogin');
      return;
    }

    // 检查是否是家长身份
    if (sf !== '家长') {
      this.$message.warning('只有家长才能提交订单');
      console.error('用户身份不是家长，无法提交订单');
      this.goBack();
      return;
    }
  },
  methods: {
    // 获取家教信息
    getTutorInfo(taccount) {
      console.log('开始获取家教信息, 家教账号:', taccount);

      // 确保taccount不为undefined
      if (!taccount || taccount === 'undefined') {
        this.$message.error('家教账号无效');
        console.error('获取家教信息失败: 家教账号无效或为undefined');
        this.goBack();
        return;
      }

      request.post(base + '/api/tutors/get', { taccount: taccount }).then(res => {
        console.log('获取家教信息响应:', res);

        if (res.code === 200 && res.resdata) {
          this.tutorInfo = res.resdata;
          this.orderForm.price = this.tutorInfo.price;
          console.log('设置家教价格:', this.orderForm.price);
          this.calculateAmount();
          console.log('计算总金额:', this.orderForm.amount);
        } else {
          this.$message.error(res.msg || '获取家教信息失败');
          console.error('获取家教信息失败:', res);
          this.goBack();
        }
      }).catch(error => {
        console.error('获取家教信息异常:', error);
        this.$message.error('获取家教信息失败，请稍后重试');
        this.goBack();
      });
    },

    // 计算总金额
    calculateAmount() {
      this.orderForm.amount = this.orderForm.price * this.orderForm.hours;
    },

    // 提交订单
    submitOrder() {
      this.$refs.orderForm.validate(valid => {
        if (!valid) {
          return false;
        }

        // 再次检查家教账号是否存在
        if (!this.orderForm.taccount) {
          this.$message.error('缺少家教账号参数');
          console.error('提交订单失败: 缺少家教账号参数');
          return false;
        }

        this.submitting = true;
        console.log('准备提交订单，家教账号:', this.orderForm.taccount);

        // 构建订单数据
        const orderData = {
          account: sessionStorage.getItem('lname'),
          taccount: this.orderForm.taccount,
          price: this.orderForm.price,
          hours: this.orderForm.hours,
          amount: this.orderForm.amount,
          remarks: this.orderForm.remarks,
          status: '待接单'
        };

        console.log('订单数据:', orderData);

        // 提交订单
        request.post(base + '/api/orderinfo/add', orderData).then(res => {
          this.submitting = false;
          console.log('订单提交响应:', res); // 添加详细日志

          console.log('订单提交响应状态码:', res.code);
          if (res.code === 0 || res.code === 200) {
            // 订单提交成功
            this.submittedOrderId = res.resdata && res.resdata.oid ? res.resdata.oid : res.oid;
            this.successDialogVisible = true;
            // 添加绿色成功提示
            this.$message({
              message: '订单提交成功',
              type: 'success',
              offset: 320
            });
          } else {
            this.$message.error(res.msg || '订单提交失败');
            console.error('订单提交失败:', res); // 添加详细错误日志
          }
        }).catch(error => {
          this.submitting = false;
          console.error('提交订单异常:', error);
          this.$message.error('订单提交失败，请稍后重试');
        });
      });
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 关闭成功对话框
    handleDialogClose() {
      this.successDialogVisible = false;
      this.goBack();
    },

    // 查看订单列表
    viewOrderList() {
      this.successDialogVisible = false;
      this.$router.push('/orderinfo_manage');
    }
  }
};
</script>

<style scoped>
.order-submit {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-title {
  margin-bottom: 20px;
  color: #303133;
  font-size: 22px;
  font-weight: 600;
}

.tutor-info-card {
  margin-bottom: 20px;
}

.tutor-header, .form-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.tutor-header h3, .form-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.tutor-details {
  display: flex;
  gap: 20px;
}

.tutor-avatar {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
}

.tutor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.tutor-content {
  flex: 1;
}

.info-row {
  margin-bottom: 10px;
  display: flex;
}

.label {
  color: #606266;
  width: 80px;
  text-align: right;
  margin-right: 10px;
}

.value {
  color: #303133;
  flex: 1;
}

.highlight {
  margin-top: 15px;
}

.price {
  color: #f56c6c;
  font-size: 18px;
  font-weight: bold;
}

.order-form {
  margin-top: 20px;
}

.unit {
  margin-left: 10px;
  color: #606266;
}

.amount {
  font-size: 20px;
  color: #f56c6c;
  font-weight: bold;
}

.success-content {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 60px;
  color: #67c23a;
  margin-bottom: 20px;
}

.success-content p {
  margin: 10px 0;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>
