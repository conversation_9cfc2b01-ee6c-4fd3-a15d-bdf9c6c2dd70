<template>
  <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules" align="left">
    <el-form-item label="账号" prop="taccount">
      <el-input v-model="formData.taccount" placeholder="账号" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="登录密码" prop="password">
      <el-input
        type="password"
        v-model="formData.password"
        placeholder="登录密码"
        style="width: 50%"
      ></el-input>
    </el-form-item>
    <el-form-item label="确认密码" prop="password2">
      <el-input
        type="password"
        v-model="formData.password2"
        placeholder="确认密码"
        style="width: 50%"
      ></el-input>
    </el-form-item>
    <el-form-item label="姓名" prop="tuname">
      <el-input v-model="formData.tuname" placeholder="姓名" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="性别" prop="gender">
      <el-radio-group v-model="formData.gender">
        <el-radio label="男"> 男 </el-radio>
        <el-radio label="女"> 女 </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="年龄" prop="age">
      <el-input v-model="formData.age" placeholder="年龄" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="手机号码" prop="phone">
      <el-input v-model="formData.phone" placeholder="手机号码" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="电子邮箱" prop="email">
      <el-input v-model="formData.email" placeholder="电子邮箱" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="学历" prop="education">
      <el-input v-model="formData.education" placeholder="学历" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="教学经验" prop="teachingexperience">
      <el-input
        v-model="formData.teachingexperience"
        placeholder="教学经验"
        style="width: 50%"
      ></el-input>
    </el-form-item>
    <el-form-item prop="photo" label="照片" min-width="20%">
      <el-input
        v-model="formData.photo"
        placeholder="照片"
        readonly="true"
        style="width: 50%"
      ></el-input>
      <el-button type="primary" size="small" @click="showUpload">上传</el-button>
    </el-form-item>
    <el-form-item label="家教类型" prop="catid">
      <el-select v-model="formData.catid" placeholder="请选择" size="small">
        <el-option
          v-for="item in categorysList"
          :key="item.catid"
          :label="item.catname"
          :value="item.catid"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="家教科目" prop="subval">
      <el-select
        v-model="formData.selectedSubjects"
        multiple
        placeholder="请选择家教科目"
        style="width: 50%"
        @change="handleSubjectsChange"
      >
        <el-option
          v-for="item in subjectsList"
          :key="item.subid"
          :label="item.subname"
          :value="item.subid"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="执教方式" prop="tflag2">
      <el-radio-group v-model="formData.tflag2">
        <el-radio label="线下"> 线下 </el-radio>
        <el-radio label="线上"> 线上 </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="收费标准/时" prop="price">
      <el-input v-model="formData.price" placeholder="收费标准/时" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="个人介绍" prop="introduction">
      <WangEditor
        v-model="formData.introduction"
        :config="editorConfig"
        :isClear="isClear"
        @change="editorChange"
      ></WangEditor>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        size="small"
        @click="save"
        :loading="btnLoading"
        icon="el-icon-upload"
        >注 册</el-button
      >
    </el-form-item>
  </el-form>
  <el-dialog
    v-model="uploadVisible"
    title="附件上传"
    custom-class="el-dialog-widthSmall"
    @close="closeDialog"
  >
    <div>
      <b>请选择文件（png,jpg格式）一寸职业免冠照进行上传！&emsp;</b>
    </div>
    <el-upload
      :action="base + '/upload'"
      style="margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px"
      drag
      :limit="1"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :file-list="fileList"
      :on-exceed="handleExceed"
      :auto-upload="false"
      name="file"
      :on-change="fileListChange"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将数据文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip">
        <div
          style="display: inline; color: #d70000; font-size: 14px"
          class="uploadFileWarning"
          id="uploadFileWarning"
        ></div>
      </div>
    </el-upload>
    <span class="dialog-footer">
      <el-button @click="hideUpload">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">提 交</el-button>
    </span>
  </el-dialog>
</template>
<script>
import request, { base } from '../../../utils/http';
import WangEditor from '../../components/WangEditor';
export default {
  name: 'Treg',
  components: {
    WangEditor,
  },
  data() {
    return {
      formData: {},
      isClear: false,
      uploadVisible: false,
      addrules: {
        taccount: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
        password2: [
          { required: true, message: '请输入登录密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== this.formData.password) {
                callback(new Error('两次输入密码不一致!'));
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        tuname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        gender: [{ required: true, message: '请输入性别', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },
        ],
        email: [
          { required: true, message: '请输入电子邮箱', trigger: 'blur' },
          {
            pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/,
            message: '电子邮箱格式不正确',
            trigger: 'blur',
          },
        ],
        education: [{ required: true, message: '请输入学历', trigger: 'blur' }],
        teachingexperience: [{ required: true, message: '请输入教学经验', trigger: 'blur' }],
        photo: [{ required: true, message: '请上传照片', trigger: 'blur' }],
        catid: [{ required: true, message: '请选择家教类型', trigger: 'onchange' }],
        subval: [{ required: true, message: '请选择家教科目', trigger: 'change' }],
        tflag2: [{ required: true, message: '请选择执教方式', trigger: 'change' }],
        price: [{ required: true, message: '请输入收费标准/时', trigger: 'blur' }],
      },

      btnLoading: false, //按钮是否在加载中
      uploadVisible: false, //上传弹出框
      subjectsList: [], // 科目列表
      formData: {
        selectedSubjects: [], // 选中的科目ID数组
      },
    };
  },
  created() {
    this.getSubjectsList(); // 获取科目列表
    this.getcategorysList(); // 获取家教类型列表
  },
  methods: {
    getcategorysList() {
      let para = {};
      this.listLoading = true;
      let url = base + '/categorys/list?currentPage=1&pageSize=1000';
      request.post(url, para).then((res) => {
        this.categorysList = res.resdata;
      });
    },

    //获取科目
    getSubjectsList() {
      let para = {};
      let url = base + '/subjects/list?currentPage=1&pageSize=1000';
      request.post(url, para).then((res) => {
        this.subjectsList = res.resdata;
      });
    },

    //注册
    save() {
      //表单验证
      this.$refs['formDataRef'].validate((valid) => {
        if (valid) {
          let url = base + '/tutors/register'; //请求地址
          this.btnLoading = true; //按钮加载状态
          this.formData.tflag = '待审核';
          request.post(url, this.formData).then((res) => {
            //请求接口
            if (res.code == 200) {
              this.$message({
                message: '恭喜您注册成功,等待管理员的审核',
                type: 'success',
                offset: 320,
              });
              this.$router.push('/');
            } else if (res.code == 201) {
              this.$message({
                message: res.msg,
                type: 'error',
                offset: 320,
              });
              this.btnLoading = false;
            } else {
              this.$message({
                message: '服务器错误',
                type: 'error',
                offset: 320,
              });
              this.btnLoading = false;
            }
          });
        }
      });
    },
    //显示上传框
    showUpload() {
      this.uploadVisible = true;
    },

    //隐藏上传框
    hideUpload() {
      this.uploadVisible = false;
    },
    //上传
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    handlePreview(file) {
      console.log(file);
    },
    handleExceed(files, fileList) {
      this.$message({
        duration: 1000,
        message: '只能上传一个文件',
        type: 'error',
        offset: 320,
      });
    },
    // 判断上传文件后缀
    fileListChange(file, fileList) {
      let extendFileName = 'png,jpg';
      let extendFileNames = extendFileName.split(',');
      let regExpRules = [];
      for (let i = 0; i < extendFileNames.length; i++) {
        regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));
      }
      let fileNames = [];
      let files = [];
      let that = this;
      fileList.forEach(function (key, val) {
        let ret = false;
        for (let i = 0; i < regExpRules.length; i++) {
          ret = ret || regExpRules[i].test(key['name']);
        }
        if (!ret) {
          console.log(key['name'] + ':' + ret);
          that.$message({
            duration: 1000,
            message: '上传的文件后缀必须为' + extendFileName + '格式！',
            type: 'error',
            offset: 320,
          });
          return false;
        }
        if (fileNames.indexOf(key['name']) != -1) {
          that.$message({
            duration: 1000,
            message: '上传的文件重复！',
            type: 'error',
            offset: 320,
          });
          return false;
        }
        //只能上传一个文件，用最后上传的覆盖
        if (!that.multiFiles) {
          files = [];
          fileNames = [];
        }
        files.push(key);
        fileNames.push(key['name']);
        if (fileNames !== '') {
          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');
        }
        //$(".uploadFileWarning").text("");
      });
      this.files = fileNames;
      this.fileList = files;
    },
    /**
     * 确认按钮
     */
    handleConfirm() {
      let filePath = this.fileList;
      if (filePath.length === 0) {
        this.$message({
          duration: 1000,
          message: '请选择文件！',
          type: 'error',
          offset: 320,
        });
        return false;
      }
      let formData = new FormData();
      this.fileList.forEach((file) => {
        formData.append('file', file.raw, file.raw.name);
      });
      let url = base + '/upload';
      console.log('url=' + url);
      request.post(url, formData).then((res) => {
        console.log(res);
        let furl = res.url;
        this.formData.photo = furl;
        this.hideUpload();
        console.log(res);
      });
    },

    // 处理科目选择变化
    handleSubjectsChange(value) {
      // 更新suids和subval
      this.formData.suids = value.join(',');
      const selectedNames = value.map((id) => {
        const subject = this.subjectsList.find((item) => item.subid === id);
        return subject ? subject.subname : '';
      });
      this.formData.subval = selectedNames.join(',');
    },
  },
};
</script>

<style></style>
