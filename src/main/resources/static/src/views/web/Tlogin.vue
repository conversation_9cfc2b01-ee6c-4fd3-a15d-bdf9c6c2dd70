<template>
  <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules" align="left">
    <el-form-item label="账号" prop="taccount">
      <el-input v-model="formData.taccount" placeholder="账号" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="登录密码" prop="password">
      <el-input type="password" v-model="formData.password" placeholder="登录密码" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="small" @click="login" :loading="btnLoading" icon="el-icon-upload">登 录</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import request, { base } from '../../../utils/http';
export default {
  name: 'Tlogin',
  data() {
    return {
      formData: {},

      addrules: {
        taccount: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
      },

      btnLoading: false, //按钮是否在加载中
    };
  },
  created() { },
  methods: {
    //登录
    login() {
      //表单验证
      this.$refs['formDataRef'].validate((valid) => {
        if (valid) {
          let url = base + '/tutors/login'; //请求地址
          this.btnLoading = true; //按钮加载状态
          request.post(url, this.formData).then((res) => {
            //请求接口
            if (res.code == 200) {
              this.$message({
                message: '登录成功',
                type: 'success',
                offset: 320,
              });

              sessionStorage.setItem('us', JSON.stringify(res.resdata));

              sessionStorage.setItem('lname', this.formData.taccount); //保存用户信息
              sessionStorage.setItem('sf', '家教'); //保存用户类型
              this.$router.push('/uweclome'); //跳转到个人中心首页
            }
            else if (res.code == 201) {
              this.$message({
                message: res.msg,
                type: 'error',
                offset: 320,
              });
              this.btnLoading = false;
            } else {
              this.$message({
                message: '服务器错误',
                type: 'error',
                offset: 320,
              });
              this.btnLoading = false;
            }
          });
        }
      });
    },
  },
};
</script>

<style></style>
