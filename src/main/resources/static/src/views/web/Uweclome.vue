<template>
  <table
    style="width: 100%; text-align: center; line-height: 28px; text-indent: 10px; margin-top: 50px"
  >
    <tr>
      <td align="right" width="30%">
        <img
          :src="'http://localhost:8088/TutoringServicePlatform/' + photo"
          style="width: 150px; height: 150px; border-radius: 70px"
        />
      </td>
      <td class="style1" valign="top" style="vertical-align: middle; padding-left: 20px">
        <table>
          <tr>
            <td class="style1">
              您好：<b style="color: red">
                {{ lname }}
              </b>
            </td>
          </tr>
          <tr>
            <td class="style1">
              您的身份 : <b style="color: red">{{ sf }}</b>
            </td>
          </tr>
          <tr>
            <td class="style1">登录时间：{{ time }}</td>
          </tr>
          <tr>
            <td class="style1"></td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</template>
<script>
import request, { base } from '../../../utils/http';
export default {
  name: 'Uweclome',
  data() {
    return {
      lname: '',
      formData: {}, //表单数据
      time: '', //当前时间
      photo: '', //头像
    };
  },
  created() {
    this.lname = sessionStorage.getItem('lname');
    this.sf = sessionStorage.getItem('sf');
    this.getDatas();
    this.time = new Date().toLocaleString();
  },
  methods: {
    //获取列表数据
    getDatas() {
      let para = {};
      this.listLoading = true;

      var url = '';

      if (this.sf == '家长') {
        url = base + '/users/get?id=' + this.lname;
        request.post(url, para).then((res) => {
          this.formData = JSON.parse(JSON.stringify(res.resdata));
          this.photo = this.formData.avatar;
          this.listLoading = false;
        });
      } else {
        url = base + '/tutors/get?id=' + this.lname;
        request.post(url, para).then((res) => {
          this.formData = JSON.parse(JSON.stringify(res.resdata));
          this.photo = this.formData.photo;
          this.listLoading = false;
        });
      }
    },
  },
};
</script>

<style></style>
