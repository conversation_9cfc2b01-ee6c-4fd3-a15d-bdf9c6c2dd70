<template>
  <el-col :span="24" style="padding-bottom: 0px; margin-left: 10px">
    <el-form :inline="true" :model="filters">
      <el-form-item>
        <el-input v-model="filters.oid" placeholder="订单编号" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="query" icon="el-icon-upload">搜索</el-button>
      </el-form-item>
    </el-form>
  </el-col>

  <el-table
    :data="datalist"
    border
    stripe
    style="width: 100%"
    v-loading="listLoading"
    highlight-current-row
    max-height="600"
    size="small"
  >
    <el-table-column prop="oid" label="订单编号" align="center"></el-table-column>
    <el-table-column prop="account" label="家长账号" align="center"></el-table-column>
    <el-table-column prop="amount" label="总金额" align="center"></el-table-column>

    <el-table-column prop="submittime" label="提交时间" align="center"></el-table-column>
    <el-table-column prop="status" label="接单状态" align="center"></el-table-column>
    <el-table-column label="操作" min-width="330" align="center">
      <template #default="scope">
        <el-button
          type="primary"
          size="mini"
          @click="handleShow(scope.$index, scope.row)"
          icon="el-icon-zoom-in"
          style="padding: 3px 6px 3px 6px"
          >详情</el-button
        >

        <template v-if="scope.row.status === '待接单'">
          <el-button
            type="success"
            size="mini"
            @click="handleAccept(scope.$index, scope.row)"
            icon="el-icon-check"
            style="padding: 3px 6px 3px 6px"
            >同意接单</el-button
          >
          <el-button
            type="warning"
            size="mini"
            @click="handleReject(scope.$index, scope.row)"
            icon="el-icon-close"
            style="padding: 3px 6px 3px 6px"
            >拒绝接单</el-button
          >
        </template>

        <el-button
          type="danger"
          size="mini"
          @click="handleDelete(scope.$index, scope.row)"
          icon="el-icon-delete"
          style="padding: 3px 6px 3px 6px"
          >删除</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    @current-change="handleCurrentChange"
    :current-page="page.currentPage"
    :page-size="page.pageSize"
    background
    layout="total, prev, pager, next, jumper"
    :total="page.totalCount"
    style="float: right; margin: 10px 20px 0 0"
  ></el-pagination>
</template>
<script>
import request, { base } from '../../../utils/http';
export default {
  name: 'orderinfo',
  components: {},
  data() {
    return {
      filters: {
        //列表查询参数
        oid: '',
        account: '',
        taccount: '',
      },

      page: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页显示条目个数
        totalCount: 0, // 总条目数
      },
      isClear: false,

      listLoading: false, //列表加载状态
      btnLoading: false, //保存按钮加载状态
      datalist: [], //表格数据
    };
  },
  created() {
    this.getDatas();
  },

  methods: {
    // 删除订单
    handleDelete(index, row) {
      this.$confirm('确认删除该记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.listLoading = true;
          let url = base + '/orderinfo/del?id=' + row.oid;
          request.post(url).then((res) => {
            this.listLoading = false;

            this.$message({
              message: '删除成功',
              type: 'success',
              offset: 320,
            });
            this.getDatas();
          });
        })
        .catch(() => {});
    },

    // 分页
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getDatas();
    },

    //获取列表数据
    getDatas() {
      var lname = sessionStorage.getItem('lname');
      let para = {
        oid: this.filters.oid,
        taccount: lname,
      };
      this.listLoading = true;
      let url =
        base +
        '/orderinfo/list?currentPage=' +
        this.page.currentPage +
        '&pageSize=' +
        this.page.pageSize;
      request.post(url, para).then((res) => {
        if (res.resdata.length > 0) {
          this.isPage = true;
        } else {
          this.isPage = false;
        }
        this.page.totalCount = res.count;
        this.datalist = res.resdata;
        this.listLoading = false;
      });
    },
    //查询
    query() {
      this.getDatas();
    },

    // 查看
    handleShow(index, row) {
      this.$router.push({
        path: '/Orderinfo_Show',
        query: {
          id: row.oid,
        },
      });
    },

    // 编辑
    handleEdit(index, row) {
      this.$router.push({
        path: '/Orderinfo_Edit',
        query: {
          id: row.oid,
        },
      });
    },

    // 处理接单
    handleAccept(index, row) {
      this.$confirm('确认接受该订单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success',
      })
        .then(() => {
          this.listLoading = true;
          let url = base + '/orderinfo/update';
          let para = {
            oid: row.oid,
            status: '已接单',
          };

          request.post(url, para).then((res) => {
            this.listLoading = false;
            if (res.code === 200) {
              this.$message({
                message: '已成功接单',
                type: 'success',
                offset: 320,
              });
              this.getDatas();
            } else {
              this.$message({
                message: res.msg || '操作失败',
                type: 'error',
                offset: 320,
              });
            }
          });
        })
        .catch(() => {});
    },

    // 处理拒绝
    handleReject(index, row) {
      this.$confirm('确认拒绝该订单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.listLoading = true;
          let url = base + '/orderinfo/update';
          let para = {
            oid: row.oid,
            status: '已拒绝',
          };

          request.post(url, para).then((res) => {
            this.listLoading = false;
            if (res.code === 200) {
              this.$message({
                message: '已拒绝该订单',
                type: 'warning',
                offset: 320,
              });
              this.getDatas();
            } else {
              this.$message({
                message: res.msg || '操作失败',
                type: 'error',
                offset: 320,
              });
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
/* 调整按钮间距 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 让表格内容更紧凑 */
.el-table {
  font-size: 13px;
}

/* 状态列的颜色 */
.el-table .cell {
  .status-pending {
    color: #e6a23c;
  }
  .status-accepted {
    color: #67c23a;
  }
  .status-rejected {
    color: #f56c6c;
  }
}
</style>
