<template>
  <div>
    <!-- 添加筛选区域 -->
    <div class="filter-container">
      <!-- 科目筛选标签组 -->
      <div class="filter-group">
        <div class="filter-header">
          <span class="filter-label">科目:</span>
          <div class="filter-tags">
            <el-tag
              :effect="selectedSubject === '' ? 'dark' : 'plain'"
              @click="handleSubjectSelect('')"
              class="filter-tag"
            >全部</el-tag>
            <el-tag
              v-for="item in subjectsList"
              :key="item.subid"
              :effect="selectedSubject === item.subid ? 'dark' : 'plain'"
              @click="handleSubjectSelect(item.subid)"
              class="filter-tag"
            >{{ item.subname }}</el-tag>
          </div>
        </div>
      </div>

      <!-- 家教类型筛选标签组 -->
      <div class="filter-group">
        <div class="filter-header">
          <span class="filter-label">类型:</span>
          <div class="filter-tags">
            <el-tag
              :effect="selectedCategory === '' ? 'dark' : 'plain'"
              @click="handleCategorySelect('')"
              class="filter-tag"
            >全部</el-tag>
            <el-tag
              v-for="item in categorysList"
              :key="item.catid"
              :effect="selectedCategory === item.catid ? 'dark' : 'plain'"
              @click="handleCategorySelect(item.catid)"
              class="filter-tag"
            >{{ item.catname }}</el-tag>
          </div>
        </div>
      </div>

      <!-- 性别筛选标签组 -->
      <div class="filter-group">
        <div class="filter-header">
          <span class="filter-label">性别:</span>
          <div class="filter-tags">
            <el-tag
              :effect="selectedGender === '' ? 'dark' : 'plain'"
              @click="handleGenderSelect('')"
              class="filter-tag"
            >全部</el-tag>
            <el-tag
              v-for="gender in ['男', '女']"
              :key="gender"
              :effect="selectedGender === gender ? 'dark' : 'plain'"
              @click="handleGenderSelect(gender)"
              class="filter-tag"
            >{{ gender }}</el-tag>
          </div>
        </div>
      </div>

      <!-- 执教方式筛选标签组 -->
      <div class="filter-group">
        <div class="filter-header">
          <span class="filter-label">执教方式:</span>
          <div class="filter-tags">
            <el-tag
              :effect="selectedTeachingMode === '' ? 'dark' : 'plain'"
              @click="handleTeachingModeSelect('')"
              class="filter-tag"
            >全部</el-tag>
            <el-tag
              v-for="mode in ['线下', '线上']"
              :key="mode"
              :effect="selectedTeachingMode === mode ? 'dark' : 'plain'"
              @click="handleTeachingModeSelect(mode)"
              class="filter-tag"
            >{{ mode }}</el-tag>
          </div>
        </div>
      </div>

      <!-- 价格区间筛选标签组 -->
      <div class="filter-group">
        <div class="filter-header">
          <span class="filter-label">价格区间:</span>
          <div class="filter-tags">
            <el-tag
              :effect="selectedPrice === '' ? 'dark' : 'plain'"
              @click="handlePriceSelect('')"
              class="filter-tag"
            >全部</el-tag>
            <el-tag
              v-for="price in priceRanges"
              :key="price.value"
              :effect="selectedPrice === price.value ? 'dark' : 'plain'"
              @click="handlePriceSelect(price.value)"
              class="filter-tag"
            >{{ price.label }}</el-tag>
          </div>
        </div>
      </div>

      <!-- 已选择的筛选条件标签 -->
      <div class="selected-tags">
        <el-tag
          v-for="tag in activeTags"
          :key="tag.type + tag.value"
          closable
          :type="getTagType(tag.type)"
          @close="removeTag(tag)"
        >
          {{ tag.label }}: {{ tag.value }}
        </el-tag>
      </div>
    </div>

    <!-- 原有的列表内容 -->
    <div class="divlist">
      <ul>
        <li class="widthk4" v-for="item in tulist" :key="item.taccount">
          <a :href="'#tutorsView?id=' + item.taccount">
            <img :src="'http://localhost:8088/TutoringServicePlatform/' + item.photo" />
          </a>
          <span class="wspan">
            <a :href="'#tutorsView?id=' + item.taccount">{{ item.tuname }}</a>
          </span>
          <span class="price-span">
            价格：<b>{{ item.price }}</b
            >元/时
          </span>
        </li>
      </ul>
    </div>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="page.currentPage"
      :page-size="page.pageSize"
      background
      layout="total, prev, pager, next, jumper"
      :total="page.totalCount"
      style="float: right; margin: 10px 20px 0 0"
    ></el-pagination>
  </div>
</template>

<script>
import request, { base } from '../../../utils/http';

export default {
  name: 'TutorsList',
  data() {
    return {
      page: {
        currentPage: 1,
        pageSize: 12,
        totalCount: 0,
      },
      tulist: '',
      subjectsList: [], // 科目列表
      categorysList: [], // 家教类型列表
      selectedSubject: '', // 选中的科目
      selectedCategory: '', // 选中的类型
      selectedGender: '', // 选中的性别
      selectedPrice: '', // 选中的价格区间
      selectedTeachingMode: '', // 选中的执教方式
      activeTags: [], // 已选择的筛选标签
      priceRanges: [
        { label: '0-100元/时', value: '0-100' },
        { label: '100-200元/时', value: '100-200' },
        { label: '200-300元/时', value: '200-300' },
        { label: '300元以上/时', value: '300-999999' }
      ],
    };
  },
  created() {
    this.selectedSubject = this.$route.query.subid == null ? '' : this.$route.query.subid;
    this.getSubjectsList();
    this.getCategorysList();
    this.getDatas();
  },
  methods: {
    // 获取科目列表
    getSubjectsList() {
      let url = base + '/subjects/list?currentPage=1&pageSize=1000';
      request.post(url, {}).then((res) => {
        this.subjectsList = res.resdata;
      });
    },

    // 获取家教类型列表
    getCategorysList() {
      let url = base + '/categorys/list?currentPage=1&pageSize=1000';
      request.post(url, {}).then((res) => {
        this.categorysList = res.resdata;
      });
    },

    // 处理筛选条件变化
    handleFilter() {
      this.page.currentPage = 1;
      this.updateTags();
      this.getDatas();
    },

    // 更新筛选标签
    updateTags() {
      this.activeTags = [];

      if (this.selectedSubject) {
        const subject = this.subjectsList.find((s) => s.subid === this.selectedSubject);
        if (subject) {
          this.activeTags.push({
            type: 'subject',
            label: '科目',
            value: subject.subname,
          });
        }
      }

      if (this.selectedCategory) {
        const category = this.categorysList.find((c) => c.catid === this.selectedCategory);
        if (category) {
          this.activeTags.push({
            type: 'category',
            label: '类型',
            value: category.catname,
          });
        }
      }

      if (this.selectedGender) {
        this.activeTags.push({
          type: 'gender',
          label: '性别',
          value: this.selectedGender,
        });
      }

      if (this.selectedPrice) {
        this.activeTags.push({
          type: 'price',
          label: '价格',
          value: this.selectedPrice + '元/时',
        });
      }

      if (this.selectedTeachingMode) {
        this.activeTags.push({
          type: 'teachingMode',
          label: '执教方式',
          value: this.selectedTeachingMode
        });
      }
    },

    // 移除筛选标签
    removeTag(tag) {
      switch (tag.type) {
        case 'subject':
          this.selectedSubject = '';
          break;
        case 'category':
          this.selectedCategory = '';
          break;
        case 'gender':
          this.selectedGender = '';
          break;
        case 'price':
          this.selectedPrice = '';
          break;
        case 'teachingMode':
          this.selectedTeachingMode = '';
          break;
      }
      this.handleFilter();
    },

    // 分页
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getDatas();
    },

    // 获取列表数据
    getDatas() {
      let para = {
        tflag: '审核通过',
        catid: this.selectedCategory,
        gender: this.selectedGender,
        tflag2: this.selectedTeachingMode,
        condition: ''
      };

      // 处理价格区间
      if (this.selectedPrice) {
        var arr = this.selectedPrice.split('-');
        para.condition += ' and price between ' + arr[0] + ' and ' + arr[1];
      }

      // 处理科目筛选
      if (this.selectedSubject) {
        para.condition += ' and suids like "%' + this.selectedSubject + '%"';
      }

      this.listLoading = true;
      let url = base + '/tutors/list?currentPage=' + this.page.currentPage + '&pageSize=' + this.page.pageSize;

      request.post(url, para).then(res => {
        this.page.totalCount = res.count;
        this.tulist = res.resdata;
        this.listLoading = false;
      });
    },

    // 获取标签类型
    getTagType(type) {
      const types = {
        subject: 'success',
        category: 'warning',
        gender: 'info',
        price: 'danger',
        teachingMode: 'info'
      };
      return types[type] || '';
    },

    // 处理科目选择
    handleSubjectSelect(value) {
      this.selectedSubject = value;
      this.handleFilter();
    },

    // 处理类型选择
    handleCategorySelect(value) {
      this.selectedCategory = value;
      this.handleFilter();
    },

    // 处理性别选择
    handleGenderSelect(value) {
      this.selectedGender = value;
      this.handleFilter();
    },

    // 处理执教方式选择
    handleTeachingModeSelect(value) {
      this.selectedTeachingMode = value;
      this.handleFilter();
    },

    // 处理价格选择
    handlePriceSelect(value) {
      this.selectedPrice = value;
      this.handleFilter();
    },
  },
};
</script>

<style scoped>
.filter-container {
  padding: 20px;
  background: #fff;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.filter-group {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.filter-label {
  min-width: 80px;
  color: #606266;
  font-weight: 500;
  margin-right: 15px;
  padding-top: 5px;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  flex: 1;
}

.filter-tag {
  cursor: pointer;
  user-select: none;
  transition: all 0.3s;
  margin-bottom: 5px;
}

.filter-tag:hover {
  transform: translateY(-2px);
}

.filter-tag.el-tag--dark {
  background-color: #409EFF;
  border-color: #409EFF;
  color: #fff;
}

.selected-tags {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.selected-tags .el-tag {
  margin-right: 10px;
  margin-bottom: 5px;
  padding: 0 10px;
  height: 28px;
  line-height: 26px;
}

.selected-tags .el-tag + .el-tag {
  margin-left: 5px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 列表样式优化 */
.divlist {
  margin-top: 20px;
}

.divlist ul {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 0;
  margin: 0;
  list-style: none;
}

.divlist .widthk4 {
  flex: 0 0 calc(22% - 5px);
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.divlist .widthk4:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.divlist img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 8px;
}

.divlist .wspan {
  display: block;
  margin: 12px 0 8px;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.divlist .price-span {
  display: block;
  color: #666;
  font-size: 14px;
}

.divlist .price-span b {
  color: #f56c6c;
  font-size: 18px;
  margin: 0 4px;
}

/* 响应式布局 */
@media screen and (max-width: 1400px) {
  .divlist .widthk4 {
    flex: 0 0 calc(33.33% - 14px);
  }
}

@media screen and (max-width: 1200px) {
  .divlist .widthk4 {
    flex: 0 0 calc(50% - 10px);
  }
}

@media screen and (max-width: 768px) {
  .divlist .widthk4 {
    flex: 0 0 100%;
  }
}

.divlist a {
  color: #303133;
  text-decoration: none;
}

.divlist a:hover {
  color: #409eff;
}
</style>
