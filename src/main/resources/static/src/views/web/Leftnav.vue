<template>
    <TopMenu />
    

<section id="parallax" class="breadcrumb-area fix" style="transform: translate3d(0px, 0px, 0px); transform-style: preserve-3d; backface-visibility: hidden; position: relative;">
    <div class="breadcrumb-shape b-shape01 layer" data-depth="0.10" style="transform: translate3d(8.70133px, -2.38465px, 0px); transform-style: preserve-3d; backface-visibility: hidden; position: relative; display: block; left: 0px; top: 0px;"><img src="@/assets/img/shape/b_shape01.png" alt="shape"></div>
    <div class="breadcrumb-shape b-shape02 layer" data-depth="0.20" style="transform: translate3d(17.4027px, -4.7693px, 0px); transform-style: preserve-3d; backface-visibility: hidden; position: absolute; display: block; left: 0px; top: 0px;"><img src="@/assets/img/shape/b_shape02.png" alt="shape"></div>
    <div class="breadcrumb-shape b-shape03 layer" data-depth="0.30" style="transform: translate3d(26.104px, -7.15394px, 0px); transform-style: preserve-3d; backface-visibility: hidden; position: absolute; display: block; left: 0px; top: 0px;"><img src="@/assets/img/shape/b_shape03.png" alt="shape"></div>
    <div class="breadcrumb-shape b-shape04 layer" data-depth="0.10" style="transform: translate3d(8.70133px, -2.38465px, 0px); transform-style: preserve-3d; backface-visibility: hidden; position: absolute; display: block; left: 0px; top: 0px;"><img src="@/assets/img/shape/b_shape04.png" alt="shape"></div>
    <div class="breadcrumb-shape b-shape05 layer" data-depth="0.50" style="transform: translate3d(43.5067px, -11.9232px, 0px); transform-style: preserve-3d; backface-visibility: hidden; position: absolute; display: block; left: 0px; top: 0px;"><img src="@/assets/img/shape/b_shape05.png" alt="shape"></div>
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 col-md-6">
                <div class="breadcrumb-wrap">
                    <h2>{{ $route.meta.title }}</h2>
                    <nav>
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="#/index">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $route.meta.title }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
            <div class="col-xl-5 offset-xl-1 col-md-6">
                <div class="breadcrumb-img text-center">
                    <img src="@/assets/img/bg/b_img.png" alt="img">
                </div>
            </div>
        </div>
    </div>
</section>

<div class="basic-blog-area pt-100 pb-100">
    <div class="container">
        <div class="row">
             <Left />
            <div class="col-lg-8 blog-post-items">
                    <router-view />
            </div>
            
        </div>
    </div>
</div>




 
    <Foot />
</template>
<script>
import request, { base } from "../../../utils/http";
import Left from "../../components/Left";
import TopMenu from "../../components/TopMenu";
import Foot from "../../components/Foot";

export default {
    name: "Leftnav",
    components: {
        Left,
        TopMenu,
        Foot,
    },
    data() {
        return {

        };
    },
    mounted() {

    },
    created() {

    },
    methods: {

    },
};
</script>


<style></style>
 

