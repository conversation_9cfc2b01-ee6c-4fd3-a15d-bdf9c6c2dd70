<template>
  
  <el-form :model="formData" label-width="20%" align="left">
<el-form-item label="订单编号">
{{formData.oid}}</el-form-item>
<el-form-item label="家长账号">
{{formData.account}}</el-form-item>
<el-form-item label="家教账号">
{{formData.taccount}}</el-form-item>
<el-form-item label="收费标准/时">
{{formData.price}}</el-form-item>
<el-form-item label="预约课时">
{{formData.hours}}</el-form-item>
<el-form-item label="总金额">
{{formData.amount}}</el-form-item>
<el-form-item label="备注说明">
{{formData.remarks}}</el-form-item>
<el-form-item label="提交时间">
{{formData.submittime}}</el-form-item>
<el-form-item label="接单状态">
{{formData.status}}</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="back"  icon="el-icon-back" >返 回</el-button>
</el-form-item>
</el-form>



</template>
<script>
        
        import request, { base } from "../../../utils/http";
        export default {
            name: 'Orderinfo_Show',
            components: {
            },
            data() {
                return {
                    id: '',
                    formData: {}, //表单数据         
        
                };
            },
            created() {
                this.id = this.$route.query.id; //获取参数
                this.getDatas();
            },
        
        
            methods: {
        
                //获取列表数据
                getDatas() {
                    let para = {
                    };
                    this.listLoading = true;
                    let url = base + "/orderinfo/get?id=" + this.id;
                    request.post(url, para).then((res) => {
                        this.formData = JSON.parse(JSON.stringify(res.resdata));
                        this.listLoading = false;
                    });
                },
        
                // 返回
                back() {
                    //返回上一页
                    this.$router.go(-1);
                },
        
            },
        }

</script>

<style></style>



