<template>
  <div class="container222">
    <div class="card222" v-for="item in holist" :key="item.id">
      <img
        :src="'http://localhost:8088/TutoringServicePlatform/' + item.pimage"
        style="height: 230px"
      />
      <div class="card-content222">
        <h2>
          <a :href="'#hometutorinfoView?id=' + item.id">{{ item.title }}</a>
        </h2>
        <div class="card-footer222">
          <span>发布时间：{{ item.publishtime }}</span>
        </div>
        <p>
          {{ item.content.length > 70 ? item.content.substring(0, 70) + '...' : item.content }}
        </p>
        <a :href="'#hometutorinfoView?id=' + item.id" class="read-more222">READ MORE</a>
      </div>
    </div>
  </div>
  <el-pagination
    @current-change="handleCurrentChange"
    :current-page="page.currentPage"
    :page-size="page.pageSize"
    background
    layout="total, prev, pager, next, jumper"
    :total="page.totalCount"
    style="float: right; margin: 10px 20px 0 0"
  ></el-pagination>
</template>
<script>
import request, { base } from '../../../utils/http';
export default {
  name: 'HometutorinfoList',
  data() {
    return {
      page: {
        currentPage: 1, // 当前页
        pageSize: 12, // 每页显示条目个数
        totalCount: 0, // 总条目数
      },
      holist: '',
    };
  },
  created() {
    this.getDatas();
  },
  methods: {
    // 分页
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getDatas();
    },

    //获取列表数据
    getDatas() {
      let para = {};
      this.listLoading = true;
      let url =
        base +
        '/hometutorinfo/list?currentPage=' +
        this.page.currentPage +
        '&pageSize=' +
        this.page.pageSize;
      request.post(url, para).then((res) => {
        if (res.resdata.length > 0) {
          this.isPage = true;
        } else {
          this.isPage = false;
        }
        this.page.totalCount = res.count;
        this.holist = res.resdata;
        this.listLoading = false;
      });
    },
  },
};
</script>

<style>
.container222 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  width: 100%;
}
.card222 {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}
.card222 a {
  text-decoration: none;
}
.card222:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}
.card222 img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}
.card-content222 {
  padding: 15px;
}
.card-content222 h2 {
  font-size: 18px;
  margin: 10px 0;
}
.card-content222 p {
  color: #555;
  font-size: 14px;
  margin: 10px 0;
}
.card-footer222 {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #888;
}
.read-more222 {
  color: #007bff;
  text-decoration: none;
  font-weight: bold;
  font-size: 12px;
}
.read-more222:hover {
  text-decoration: underline;
}
</style>
