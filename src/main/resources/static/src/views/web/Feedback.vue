<template>
  <div class="message-list__container-xyz789 bg-white shadow-lg rounded-lg">
    <div class="message-list__message-xyz789" v-for="item in felist" :key="item.id">
      <div class="message-list__avatar-xyz789">
        <img :src="'http://localhost:8088/TutoringServicePlatform/' + item.avatar" />
      </div>
      <div class="message-list__content-xyz789">
        <div class="message-list__header-xyz789">
          <span class="message-list__topic-xyz789">{{ item.title }}</span>
          <span class="message-list__time-xyz789">{{ item.addtime }}</span>
        </div>
        <div class="message-list__username-xyz789">
          {{ item.account }}
          <el-tag>{{ item.uflag }}</el-tag>
        </div>
        <p class="message-list__text-xyz789">{{ item.content }}</p>
        <div class="message-list__admin-reply-xyz789" v-if="item.adminreply != null">
          <strong>管理员回复：</strong>{{ item.adminreply }}
        </div>
      </div>
    </div>
  </div>

  <table style="width: 100%">
    <tr>
      <td>
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          background
          layout="total, prev, pager, next, jumper"
          :total="page.totalCount"
          style="float: right; margin: 10px 20px 0 0"
        ></el-pagination>
      </td>
    </tr>
  </table>

  <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules" align="left">
    <el-form-item label="留言标题" prop="title">
      <el-input v-model="formData.title" placeholder="留言标题" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="留言内容" prop="content">
      <el-input
        type="textarea"
        :rows="5"
        v-model="formData.content"
        placeholder="留言内容"
        size="small"
      ></el-input>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        size="small"
        @click="save"
        :loading="btnLoading"
        icon="el-icon-upload"
        >发 表</el-button
      >
    </el-form-item>
  </el-form>
</template>
<script>
import request, { base } from '../../../utils/http';
export default {
  name: 'Feedback',
  data() {
    return {
      page: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页显示条目个数
        totalCount: 0, // 总条目数
      },
      felist: '',
      formData: {},
      addrules: {
        title: [{ required: true, message: '请输入留言标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入留言内容', trigger: 'blur' }],
      },
      btnLoading: false,
    };
  },
  created() {
    this.getDatas();
  },
  methods: {
    // 分页
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getDatas();
    },

    //获取列表数据
    getDatas() {
      let para = {};
      this.listLoading = true;
      let url =
        base +
        '/feedback/list?currentPage=' +
        this.page.currentPage +
        '&pageSize=' +
        this.page.pageSize;
      request.post(url, para).then((res) => {
        if (res.resdata.length > 0) {
          this.isPage = true;
        } else {
          this.isPage = false;
        }
        this.page.totalCount = res.count;
        this.felist = res.resdata;
        this.listLoading = false;
      });
    },

    //提交留言
    save() {
      //如果没有ID就新增数据
      this.$refs['formDataRef'].validate((valid) => {
        if (valid) {
          var lname = sessionStorage.getItem('lname'); //获取登录名
          if (lname == null) {
            this.$message({
              message: '请先登录',
              type: 'error',
              offset: 320,
            });
            return;
          }

          var user = JSON.parse(sessionStorage.getItem('us'));
          var sf = sessionStorage.getItem('sf');

          if (sf == '家长') {
            this.formData.avatar = user.avatar;
          } else {
            this.formData.avatar = user.photo;
          }

          let url = base + '/feedback/add'; //提交地址
          this.btnLoading = true; //打开按钮加载状态
          this.formData.account = lname; //留言人
          this.formData.uflag = sf;
          request.post(url, this.formData).then((res) => {
            //提交数据

            if (res.code == 200) {
              this.formData = {}; //清空表单
              this.btnLoading = false; //关闭按钮加载状态
              this.getDatas(); //刷新列表

              this.$message({
                message: '恭喜您，留言成功，请等待管理员的回复！',
                type: 'success',
                offset: 320,
              });
            } else {
              this.$message({
                message: '服务器错误',
                type: 'error',
                offset: 320,
              });
              this.btnLoading = false;
            }
          });
        }
      });
    },
  },
};
</script>

<style>
.message-list__container-xyz789 {
  max-width: 100%;
  margin: 20px auto;
  padding: 20px;
}
.message-list__message-xyz789 {
  display: flex;
  gap: 20px;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}
.message-list__avatar-xyz789 img {
  width: 60px;
  height: 60px;
  border-radius: 9999px;
  object-fit: cover;
}
.message-list__content-xyz789 {
  flex: 1;
}
.message-list__header-xyz789 {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 10px;
}
.message-list__topic-xyz789 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}
.message-list__username-xyz789 {
  font-size: 0.9rem;
  font-weight: 500;
  color: #4b5563;
}
.message-list__time-xyz789 {
  font-size: 0.8rem;
  color: #6b7280;
}
.message-list__text-xyz789 {
  margin-top: 5px;
  color: #374151;
  line-height: 1.6;
}
.message-list__admin-reply-xyz789 {
  margin-top: 15px;
  padding: 10px 15px;
  background-color: #f3f4f6;
  border-left: 4px solid #fa0926;
  color: #fa0926;
  font-size: 0.95rem;
}
@media (max-width: 640px) {
  .message-list__message-xyz789 {
    flex-direction: column;
  }
  .message-list__avatar-xyz789 {
    align-self: center;
    margin-bottom: 10px;
  }
}
</style>
