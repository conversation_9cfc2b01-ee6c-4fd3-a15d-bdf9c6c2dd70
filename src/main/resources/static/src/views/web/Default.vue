<template>
  <!--幻灯片大图开始-->
  <div id="banner_main">
    <div id="banner" class="banner" style="height: 400px">
      <div class="swiper-container swiper-container1">
        <div class="swiper-wrapper">
          <div class="swiper-slide">
            <img style="width: 100%; height: 405px" :src="banner1" />
          </div>
          <div class="swiper-slide">
            <img style="width: 100%; height: 405px" :src="banner2" />
          </div>
          <div class="swiper-slide">
            <img style="width: 100%; height: 405px" :src="banner3" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--幻灯片大图结束-->
  <div class="index">
    <section class="shop-area pt-70 pb-40 p-relative">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-xl-6 col-lg-8 col-md-10">
            <div class="section-title text-center mb-60">
              <h3>最新家教</h3>
            </div>
          </div>
        </div>
        <div class="row">
          <div
            class="col-xl-3 col-lg-4 col-sm-6 wow fadeInUp"
            data-wow-delay="0.4s"
            v-for="(item, index) in list1"
            :key="index"
          >
            <div class="shop-products mb-55">
              <div class="sp-thumb">
                <a :href="'#tutorsView?id=' + item.taccount"
                  ><img :src="'http://localhost:8088/TutoringServicePlatform/' + item.photo"
                /></a>
                <span>New</span>
                <div class="product-action">
                  <div class="button-left">
                    <ul>
                      <li>
                        <a :href="'#tutorsView?id=' + item.taccount" class="blue-bg"
                          ><i class="ti-eye"></i
                        ></a>
                      </li>
                    </ul>
                  </div>
                  <div class="cart-button">
                    <a :href="'#tutorsView?id=' + item.taccount" class="btn">查看详情</a>
                  </div>
                </div>
              </div>
              <div class="product-content text-center">
                <h4>
                  <a :href="'#tutorsView?id=' + item.taccount">{{ item.tuname }}</a>
                </h4>
                <div class="price">
                  <span
                    >￥<text>{{ item.price }}</text></span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="shop-area pt-70 pb-40 p-relative">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-xl-6 col-lg-8 col-md-10">
            <div class="section-title text-center mb-60">
              <h3>热门家教</h3>
            </div>
          </div>
        </div>
        <div class="row">
          <div
            class="col-xl-3 col-lg-4 col-sm-6 wow fadeInUp"
            data-wow-delay="0.4s"
            v-for="(item, index) in list2"
            :key="index"
          >
            <div class="shop-products mb-55">
              <div class="sp-thumb">
                <a :href="'#tutorsView?id=' + item.taccount"
                  ><img :src="'http://localhost:8088/TutoringServicePlatform/' + item.photo"
                /></a>
                <span>Hot</span>
                <div class="product-action">
                  <div class="button-left">
                    <ul>
                      <li>
                        <a :href="'#tutorsView?id=' + item.taccount" class="blue-bg"
                          ><i class="ti-eye"></i
                        ></a>
                      </li>
                    </ul>
                  </div>
                  <div class="cart-button">
                    <a :href="'#tutorsView?id=' + item.taccount" class="btn">查看详情</a>
                  </div>
                </div>
              </div>
              <div class="product-content text-center">
                <h4>
                  <a :href="'#tutorsView?id=' + item.taccount">{{ item.tuname }}</a>
                </h4>
                <div class="price">
                  <span
                    >￥<text>{{ item.price }}</text></span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="features-product p-relative fix pt-95 pb-40">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-xl-6 col-lg-8 col-md-10">
            <div class="section-title text-center mb-60">
              <h3>家教资讯</h3>
            </div>
          </div>
        </div>
        <div class="row">
          <div
            class="col-xl-4 col-lg-6 col-md-6 wow fadeInUp"
            data-wow-delay="0.2s"
            v-for="(item, index) in list3"
            :key="index"
          >
            <div class="news-item mb-30">
              <div class="news-thumb">
                <a :href="'#hometutorinfoView?id=' + item.id"
                  ><img :src="'http://localhost:8088/TutoringServicePlatform/' + item.pimage"
                /></a>
              </div>
              <div class="news-content">
                <h4>
                  <a :href="'#hometutorinfoView?id=' + item.id">{{ item.title }}</a>
                </h4>
                <p>
                  {{
                    item.content.length > 70 ? item.content.substring(0, 70) + '...' : item.content
                  }}
                </p>
                <a :href="'#hometutorinfoView?id=' + item.id" class="read-more"
                  >阅读更多 <i class="fa fa-angle-right"></i
                ></a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
<script>
import request, { base } from '../../../utils/http';
import Swiper from 'swiper';
import 'swiper/dist/css/swiper.min.css';
import 'swiper/dist/js/swiper.min';

export default {
  name: 'Default',
  data() {
    return {
      banner1: require('@/assets/img/11.jpg'),
      banner2: require('@/assets/img/22.jpg'),
      banner3: require('@/assets/img/33.jpg'),
      list1: '',
      list2: '',
      list3: '',
    };
  },
  mounted() {
    new Swiper('.swiper-container', {
      slidesPerView: 1,
      spaceBetween: 0,
      loop: true,
      autoplay: 3000,
    });
  },
  created() {
    this.getlist1();
    this.getlist2();
    this.getlist3();
  },
  methods: {
    // 获取最新家教
    getlist1() {
      let para = {
        tflag: '审核通过',
      };
      this.listLoading = true;
      let url = base + '/tutors/list?currentPage=1&pageSize=8';
      request.post(url, para).then((res) => {
        this.list1 = res.resdata;
        this.listLoading = false;
      });
    },

    // 获取热门家教
    getlist2() {
      let para = {
        tflag: '审核通过',
        sort: ' by1 desc,',
      };
      this.listLoading = true;
      let url = base + '/tutors/list?currentPage=1&pageSize=8';
      request.post(url, para).then((res) => {
        this.list2 = res.resdata;
        this.listLoading = false;
      });
    },

    // 获取家教资讯
    getlist3() {
      let para = {};
      this.listLoading = true;
      let url = base + '/hometutorinfo/list?currentPage=1&pageSize=6';
      request.post(url, para).then((res) => {
        this.list3 = res.resdata;
        this.listLoading = false;
      });
    },
  },
};
</script>

<style></style>
