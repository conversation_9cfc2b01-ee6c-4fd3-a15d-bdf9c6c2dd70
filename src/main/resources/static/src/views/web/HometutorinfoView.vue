<template>
   
<table style=" width: 100%;">
    <tr>
        <td style="text-align:center;    font-size: 20px; ">{{holist.title}}</td>
    </tr>
    <tr>
        <td style="text-align:center;">发布时间：{{holist.publishtime}}&nbsp;&nbsp;</td>
    </tr>
    <tr>
        <td align="center">
            <img :src="'http://localhost:8088/TutoringServicePlatform/' +holist.pimage"  style='width:350px' />
        </td>
    </tr>
    <tr>
        <td style="text-indent:24px;"><div v-html="holist.content"></div></td>
    </tr>
</table>





</template>
<script>
import request, { base } from "../../../utils/http";
export default {
  name: "HometutorinfoView",
  data() {
    return {
          holist: "",
 

    };
  },
  created() {
    
    this.getDatas();


  },
  methods: {  
    
    //获取列表数据
    getDatas() {
        let id = this.$route.query.id;
        let para = {           
        };
        this.listLoading = true;
        let url = base + "/hometutorinfo/get?id=" + id ;
        request.post(url, para).then((res) => {
            this.holist = res.resdata;
        });
    },    


  },
};
</script>

<style>

</style>



