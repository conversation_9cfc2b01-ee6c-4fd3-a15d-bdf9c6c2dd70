<template>
  <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="rules" align="left">
    <el-form-item label="姓名" prop="uname">
      <el-input v-model="formData.uname" placeholder="姓名" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="孩子性别" prop="gender">
      <el-radio-group v-model="formData.gender">
        <el-radio label="男"> 男 </el-radio>
        <el-radio label="女"> 女 </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="孩子年龄" prop="age">
      <el-input v-model="formData.age" placeholder="孩子年龄" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="手机号码" prop="phone">
      <el-input v-model="formData.phone" placeholder="手机号码" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="电子邮箱" prop="email">
      <el-input v-model="formData.email" placeholder="电子邮箱" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item label="联系地址" prop="address">
      <el-input v-model="formData.address" placeholder="联系地址" style="width: 50%"></el-input>
    </el-form-item>
    <el-form-item prop="avatar" label="个人头像" min-width="20%">
      <el-avatar
        :src="formData.avatar ? base.replace('/api', '') + '/' + formData.avatar : ''"
        shape="square"
        :size="100"
        :fit="fit"
      ></el-avatar>
      <el-button type="primary" size="small" @click="showUpload">上传</el-button>
    </el-form-item>

    <el-form-item>
      <el-button
        type="primary"
        size="small"
        @click="save"
        :loading="btnLoading"
        icon="el-icon-upload"
        >保 存</el-button
      >
    </el-form-item>
  </el-form>
  <el-dialog
    v-model="uploadVisible"
    title="附件上传"
    custom-class="el-dialog-widthSmall"
    @close="closeDialog"
  >
    <div>
      <b>请选择文件（png,jpg格式）进行上传！&emsp;</b>
    </div>
    <el-upload
      action="http://localhost:8088/TutoringServicePlatform/api/common/uploadFile"
      style="margin: auto; margin-top: 10px; border: 1px solid #dcdfe6; border-radius: 4px"
      drag
      :limit="1"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :file-list="fileList"
      :on-exceed="handleExceed"
      :auto-upload="false"
      name="file"
      :on-change="fileListChange"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将数据文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip">
        <div
          style="display: inline; color: #d70000; font-size: 14px"
          class="uploadFileWarning"
          id="uploadFileWarning"
        ></div>
      </div>
    </el-upload>
    <span class="dialog-footer">
      <el-button @click="hideUpload">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">提 交</el-button>
    </span>
  </el-dialog>
</template>
<script>
import request, { base } from '../../../utils/http';
export default {
  name: 'Uinfo',
  data() {
    return {
      formData: {},

      rules: {
        account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        uname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        gender: [{ required: true, message: '请输入孩子性别', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },
        ],
        email: [
          { required: true, message: '请输入电子邮箱', trigger: 'blur' },
          {
            pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/,
            message: '电子邮箱格式不正确',
            trigger: 'blur',
          },
        ],
        address: [{ required: true, message: '请输入联系地址', trigger: 'blur' }],
        avatar: [{ required: true, message: '请上传个人头像', trigger: 'blur' }],
        uflag: [{ required: true, message: '请输入审核状态', trigger: 'blur' }],
      },

      btnLoading: false, //按钮是否在加载中
      uploadVisible: false, //上传弹出框
    };
  },
  created() {
    this.getinfo();
  },
  methods: {
    //得到用户信息
    getinfo() {
      var lname = sessionStorage.getItem('lname');
      let url = base + '/users/get?id=' + lname; //请求地址
      request.post(url).then((res) => {
        //请求接口
        if (res.code == 200) {
          this.formData = res.resdata;
        } else {
          this.$message({
            message: '服务器错误',
            type: 'error',
            offset: 320,
          });
        }
      });
    },

    //注册
    save() {
      //表单验证
      this.$refs['formDataRef'].validate((valid) => {
        if (valid) {
          var lname = sessionStorage.getItem('lname');
          let url = base + '/users/update'; //请求地址
          this.btnLoading = true; //按钮加载状态
          request.post(url, this.formData).then((res) => {
            //请求接口
            this.btnLoading = false; // 取消按钮加载状态
            if (res.code == 200) {
              this.$message({
                message: '操作成功！',
                type: 'success',
                offset: 320,
              });
            } else {
              this.$message({
                message: '服务器错误: ' + (res.msg || '未知错误'),
                type: 'error',
                offset: 320,
              });
            }
          }).catch(err => {
            this.btnLoading = false;
            console.error('更新失败:', err);
            this.$message({
              message: '更新失败，请检查网络连接',
              type: 'error',
              offset: 320,
            });
          });
        }
      });
    },
    //显示上传框
    showUpload() {
      this.uploadVisible = true;
    },

    //隐藏上传框
    hideUpload() {
      this.uploadVisible = false;
    },
    //上传
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    handlePreview(file) {
      console.log(file);
    },
    handleExceed(files, fileList) {
      this.$message({
        duration: 1000,
        message: '只能上传一个文件',
        type: 'error',
        offset: 320,
      });
    },
    // 判断上传文件后缀
    fileListChange(file, fileList) {
      let extendFileName = 'png,jpg';
      let extendFileNames = extendFileName.split(',');
      let regExpRules = [];
      for (let i = 0; i < extendFileNames.length; i++) {
        regExpRules.push(new RegExp('(.*).(' + extendFileNames[i] + ')$', 'gim'));
      }
      let fileNames = [];
      let files = [];
      let that = this;
      fileList.forEach(function (key, val) {
        let ret = false;
        for (let i = 0; i < regExpRules.length; i++) {
          ret = ret || regExpRules[i].test(key['name']);
        }
        if (!ret) {
          console.log(key['name'] + ':' + ret);
          that.$message({
            duration: 1000,
            message: '上传的文件后缀必须为' + extendFileName + '格式！',
            type: 'error',
            offset: 320,
          });
          return false;
        }
        if (fileNames.indexOf(key['name']) != -1) {
          that.$message({
            duration: 1000,
            message: '上传的文件重复！',
            type: 'error',
            offset: 320,
          });
          return false;
        }
        //只能上传一个文件，用最后上传的覆盖
        if (!that.multiFiles) {
          files = [];
          fileNames = [];
        }
        files.push(key);
        fileNames.push(key['name']);
        if (fileNames !== '') {
          // $('#uploadMad .el-upload-dragger').css('border-color', '#409eff');
        }
        //$(".uploadFileWarning").text("");
      });
      this.files = fileNames;
      this.fileList = files;
    },
    /**
     * 确认按钮
     */
    handleConfirm() {
      let filePath = this.fileList;
      if (filePath.length === 0) {
        this.$message({
          duration: 1000,
          message: '请选择文件！',
          type: 'error',
          offset: 320,
        });
        return false;
      }
      let formData = new FormData();
      this.fileList.forEach((file) => {
        formData.append('file', file.raw, file.raw.name);
      });
      let url = base + '/api/common/uploadFile';
      console.log('url=' + url);
      request.post(url, formData).then((res) => {
        console.log(res);
        if (res.code === 0) {
          let furl = res.resdata.filePath;
          this.formData.avatar = furl; // 上传文件的路径
          this.$message({
            message: '头像上传成功！',
            type: 'success',
            offset: 320,
          });
          this.hideUpload();
        } else {
          this.$message({
            message: '头像上传失败：' + res.msg,
            type: 'error',
            offset: 320,
          });
        }
      }).catch(err => {
        console.error(err);
        this.$message({
          message: '头像上传失败，请检查网络连接',
          type: 'error',
          offset: 320,
        });
      });
    },
  },
};
</script>

<style></style>
