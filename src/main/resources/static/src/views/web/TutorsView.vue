<template>
  <div class="tutor-detail">
    <div class="basic-info">
      <div class="photo-section">
        <img :src="'http://localhost:8088/TutoringServicePlatform/' + tulist.photo" />
      </div>
      <div class="info-section">
        <h2>{{ tulist.tuname }}</h2>
        <div class="info-item">
          <span class="label">性别：</span>
          <span>{{ tulist.gender }}</span>
        </div>
        <div class="info-item">
          <span class="label">年龄：</span>
          <span>{{ tulist.age }}</span>
        </div>
        <div class="info-item">
          <span class="label">手机号码：</span>
          <span>{{ tulist.phone }}</span>
        </div>
        <div class="info-item">
          <span class="label">电子邮箱：</span>
          <span>{{ tulist.email }}</span>
        </div>
        <div class="info-item">
          <span class="label">学历：</span>
          <span>{{ tulist.education }}</span>
        </div>
        <div class="info-item">
          <span class="label">教学经验：</span>
          <span>{{ tulist.teachingexperience }}</span>
        </div>
        <div class="info-item">
          <span class="label">执教方式：</span>
          <span>{{ tulist.tflag2 }}</span>
        </div>
        <div class="info-item price">
          <span class="label" style="font-size: 16px">收费标准：</span>
          <span class="price-value">¥{{ tulist.price }}/时</span>
        </div>

        <div class="action-buttons">
          <el-button type="primary" @click="handleAppointment">我要预约</el-button>
          <el-button type="success" @click="handleChat">在线咨询</el-button>
        </div>
      </div>
    </div>

    <div class="detail-tabs">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="家教科目" name="subjects">
          <div class="subjects-list">
            <el-tag
              v-for="subject in subjectsList"
              :key="subject"
              size="medium"
              effect="plain"
              class="subject-tag"
            >
              {{ subject }}
            </el-tag>
          </div>
        </el-tab-pane>
        <el-tab-pane label="个人介绍" name="introduction">
          <div class="introduction" v-html="tulist.introduction"></div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <ChatDialog v-model:visible="chatVisible" :to-user="tulist.taccount" :to-name="tulist.tuname" />
  </div>
</template>

<script>
import ChatDialog from '../../components/ChatDialog.vue';
import request, { base } from '../../../utils/http';

export default {
  name: 'TutorsView',
  components: {
    ChatDialog,
  },
  data() {
    return {
      tulist: '',
      activeTab: 'subjects',
      subjectsList: [],
      chatVisible: false,
    };
  },

  watch:{
    "$route.query.id"(){
      this.getDatas();
    }
  },

  created() {
    this.getDatas();
  },
  methods: {
    getDatas() {
      let id = this.$route.query.id;
      let url = base + '/tutors/get?id=' + id;
      request.post(url, {}).then((res) => {
        this.tulist = res.resdata;
        // 将科目字符串转换为数组
        if (this.tulist.subval) {
          this.subjectsList = this.tulist.subval.split(',');
        }
      });
    },

    // 处理预约按钮点击
    handleAppointment() {
      // 检查是否登录
      var lname = sessionStorage.getItem('lname');
      var sf = sessionStorage.getItem('sf');
      if (!lname || !sf) {
        this.$message.warning('请先登录');
        this.$router.push('/ulogin');
        return;
      }

      // 检查是否是家长身份
      if (sf !== '家长') {
        this.$message.warning('只有家长才能预约家教');
        return;
      }

      // 跳转到预约页面
      this.$router.push({
        path: '/orderinfo_add',
        query: {
          tid: this.tulist.taccount,
          tname: this.tulist.tuname,
        },
      });
    },

    // 处理聊天按钮点击
    handleChat() {
      // 检查是否登录
      var lname = sessionStorage.getItem('lname');
      var sf = sessionStorage.getItem('sf');
      if (!lname || !sf) {
        this.$message.warning('请先登录');
        this.$router.push('/ulogin');
        return;
      }

      // 检查是否是家长身份
      if (sf !== '家长') {
        this.$message.warning('只有家长才能与家教聊天');
        return;
      }

      // 显示聊天对话框
      this.chatVisible = true;
    },
  },
};
</script>

<style scoped>
.tutor-detail {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.basic-info {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
}

.photo-section {
  flex: 0 0 350px;
}

.photo-section img {
  width: 100%;
  height: 350px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.info-section {
  flex: 1;
}

.info-section h2 {
  margin: 0 0 20px;
  font-size: 24px;
  color: #303133;
}

.info-item {
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
}

.info-item .label {
  color: #909399;
  margin-right: 10px;
  display: inline-block;
  width: 80px;
}

.price {
  margin: 20px 0;
  padding: 15px;
  background: #f8f8f8;
  border-radius: 4px;
}

.price-value {
  color: #f56c6c;
  font-size: 20px;
  font-weight: bold;
}

.action-buttons {
  margin-top: 30px;
}

.action-buttons .el-button {
  margin-right: 15px;
  padding: 12px 25px;
}

.detail-tabs {
  margin-top: 30px;
}

.subjects-list {
  padding: 20px 0;
}

.subject-tag {
  margin: 0 10px 10px 0;
}

.introduction {
  padding: 20px 0;
  line-height: 1.8;
  color: #606266;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .basic-info {
    flex-direction: column;
  }

  .photo-section {
    flex: 0 0 auto;
  }
}
</style>
