<template>

<el-config-provider :locale="locale">
 
    <div id="wrapper">

 <Header />

<LeftMenu />

<!-- Page Content Start -->
<div class="content-page">
    <div class="content">
        <div class="container-fluid">
            <router-view />        

        </div>
    </div>
</div>
<!-- End Page Content-->



</div>



</el-config-provider>
</template>

<script>
import Header from "../components/Header";
import LeftMenu from "../components/LeftMenu";
import { ElConfigProvider } from "element-plus";
import zhCn from "element-plus/lib/locale/lang/zh-cn";

export default {
  name: "MainLayout",
  components: {
    Header,
    LeftMenu,
    [ElConfigProvider.name]: ElConfigProvider,
  },
  data() {
    return {
      locale: zhCn,
    };
  },
  mounted() {




  },

  methods: {

  },
};
</script>


<style scoped>
@import url(../assets/libs/@mdi/font/css/materialdesignicons.min.css);
@import url(../assets/libs/dripicons/webfont/webfont.css);
@import url(../assets/css/app.css);
</style>

