<template>
  <div class="page-title-box">
    <ol class="breadcrumb float-right">
      <li class="breadcrumb-item"><a href="javascript:void(0);" id="title1">留言反馈管理</a></li>
      <li class="breadcrumb-item active" id="title2">{{ this.$route.meta.title }}</li>
    </ol>
    <h4 class="page-title" id="title3">{{ this.$route.meta.title }}</h4>
  </div>


    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" align="left">
<el-form-item label="留言id">
{{formData.id}}</el-form-item>
<el-form-item label="账号">
{{formData.account}}</el-form-item>
<el-form-item label="身份">
{{formData.uflag}}</el-form-item>
<el-form-item label="头像" prop="avatar">
<img :src="'http://localhost:8088/TutoringServicePlatform/' +formData.avatar" style="width: 150px;height: 150px" />
</el-form-item>
<el-form-item label="反馈主题">
{{formData.title}}</el-form-item>
<el-form-item label="反馈内容">
{{formData.content}}</el-form-item>
<el-form-item label="反馈时间">
{{formData.addtime}}</el-form-item>
<el-form-item label="管理员回复">
{{formData.adminreply}}</el-form-item>
<el-form-item>
<el-button type="info" size="small" @click="back" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>



    </div>

</template>

<script>
        
        import request, { base } from "../../../../utils/http";
        export default {
            name: 'FeedbackDetail',
            components: {
            },
            data() {
                return {
                    id: '',
                    formData: {}, //表单数据         
        
                };
            },
            created() {
                this.id = this.$route.query.id; //获取参数
                this.getDatas();
            },
        
        
            methods: {
        
                //获取列表数据
                getDatas() {
                    let para = {
                    };
                    this.listLoading = true;
                    let url = base + "/feedback/get?id=" + this.id;
                    request.post(url, para).then((res) => {
                        this.formData = JSON.parse(JSON.stringify(res.resdata));
                        this.listLoading = false;
                    });
                },
        
                // 返回
                back() {
                    //返回上一页
                    this.$router.go(-1);
                },
        
            },
        }

</script>
<style scoped>
</style>
 

