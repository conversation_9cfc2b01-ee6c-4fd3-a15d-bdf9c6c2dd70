<template>
  <div class="page-title-box">
    <ol class="breadcrumb float-right">
      <li class="breadcrumb-item"><a href="javascript:void(0);" id="title1">留言反馈管理</a></li>
      <li class="breadcrumb-item active" id="title2">{{ this.$route.meta.title }}</li>
    </ol>
    <h4 class="page-title" id="title3">{{ this.$route.meta.title }}</h4>
  </div>


    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="留言id">
{{formData.id}}</el-form-item>
<el-form-item label="账号">
{{formData.account}}</el-form-item>
<el-form-item label="身份">
{{formData.uflag}}</el-form-item>
<el-form-item label="头像" prop="avatar">
<img :src="'http://localhost:8088/TutoringServicePlatform/' +formData.avatar" style="width: 150px;height: 150px" />
</el-form-item>
<el-form-item label="反馈主题">
{{formData.title}}</el-form-item>
<el-form-item label="反馈内容">
{{formData.content}}</el-form-item>
<el-form-item label="反馈时间">
{{formData.addtime}}</el-form-item>
<el-form-item label="管理员回复" prop="adminreply">
<el-input type="textarea" :rows="5" v-model="formData.adminreply" placeholder="管理员回复"  size="small"></el-input>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>



    </div>

</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'FeedbackEdit',
  components: {
    
  },  
    data() {
      return {   
        id: '',
        isClear: false,
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          adminreply: [{ required: true, message: '请输入管理员回复', trigger: 'blur' },
],
        }
        }
    },
    created() {
    this.id = this.$route.query.id;
    this.getDatas();
    },

 
    methods: {    

//获取列表数据
        getDatas() {
          let para = {
          };
          this.listLoading = true;
          let url = base + "/feedback/get?id=" + this.id;
          request.post(url, para).then((res) => {
            this.formData = JSON.parse(JSON.stringify(res.resdata));
            this.listLoading = false;
            
            
          });
        },
    
        // 添加
        save() {
          this.$refs["formDataRef"].validate((valid) => { //验证表单
            if (valid) {
              let url = base + "/feedback/update";
              this.btnLoading = true;
              
              request.post(url, this.formData).then((res) => { //发送请求         
                if (res.code == 200) {
                  this.$message({
                    message: "操作成功",
                    type: "success",
                    offset: 320,
                  });
                  this.$router.push({
                    path: "/FeedbackManage",
                  });
                } else {
                  this.$message({
                    message:res.msg,
                    type: "error",
                    offset: 320,
                  });
                }
                this.btnLoading = false;
              });
            }
    
          });
        },
        
       // 返回
        goBack() {
          this.$router.push({
            path: "/FeedbackManage",
          });
        },       
              
          
           
           
      },
}

</script>
<style scoped>
</style>
 

