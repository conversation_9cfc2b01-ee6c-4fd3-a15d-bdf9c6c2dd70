<template>
  <div class="page-title-box">
    <ol class="breadcrumb float-right">
      <li class="breadcrumb-item"><a href="javascript:void(0);" id="title1">订单管理</a></li>
      <li class="breadcrumb-item active" id="title2">{{ this.$route.meta.title }}</li>
    </ol>
    <h4 class="page-title" id="title3">{{ this.$route.meta.title }}</h4>
  </div>

  <div style="width: 100%; line-height: 30px; text-align: left">
    <el-col :span="24" style="padding-bottom: 0px; margin-left: 10px">
      <el-form :inline="true" :model="filters">
        <el-form-item>
          <el-input v-model="filters.oid" placeholder="订单编号" size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="filters.account" placeholder="家长账号" size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="filters.taccount" placeholder="家教账号" size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="query" icon="el-icon-search"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
    </el-col>

    <el-table
      :data="datalist"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
      highlight-current-row
      max-height="600"
      size="small"
    >
      <el-table-column prop="oid" label="订单编号" align="center"></el-table-column>
      <el-table-column prop="account" label="家长账号" align="center"></el-table-column>
      <el-table-column prop="taccount" label="家教账号" align="center"></el-table-column>
      <el-table-column prop="hours" label="预约课时" align="center"></el-table-column>
      <el-table-column prop="amount" label="总金额" align="center"></el-table-column>

      <el-table-column prop="submittime" label="提交时间" align="center"></el-table-column>
      <el-table-column prop="status" label="接单状态" align="center"></el-table-column>
      <el-table-column label="操作" min-width="200" align="center">
        <template #default="scope">
          <el-button
            type="primary"
            size="mini"
            @click="handleShow(scope.$index, scope.row)"
            icon="el-icon-zoom-in"
            style="padding: 3px 6px 3px 6px"
            >详情</el-button
          >
          <el-button
            type="danger"
            size="mini"
            @click="handleDelete(scope.$index, scope.row)"
            icon="el-icon-delete"
            style="padding: 3px 6px 3px 6px"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="page.currentPage"
      :page-size="page.pageSize"
      background
      layout="total, prev, pager, next, jumper"
      :total="page.totalCount"
      style="float: right; margin: 10px 20px 0 0"
    ></el-pagination>
  </div>
</template>

<script>
import request, { base } from '../../../../utils/http';
export default {
  name: 'orderinfo',
  components: {},
  data() {
    return {
      filters: {
        //列表查询参数
        oid: '',
        account: '',
        taccount: '',
      },

      page: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页显示条目个数
        totalCount: 0, // 总条目数
      },
      isClear: false,

      listLoading: false, //列表加载状态
      btnLoading: false, //保存按钮加载状态
      datalist: [], //表格数据
    };
  },
  created() {
    this.getDatas();
  },

  methods: {
    // 删除订单
    handleDelete(index, row) {
      this.$confirm('确认删除该记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.listLoading = true;
          let url = base + '/orderinfo/del?id=' + row.oid;
          request.post(url).then((res) => {
            this.listLoading = false;

            this.$message({
              message: '删除成功',
              type: 'success',
              offset: 320,
            });
            this.getDatas();
          });
        })
        .catch(() => {});
    },

    // 分页
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getDatas();
    },

    //获取列表数据
    getDatas() {
      let para = {
        oid: this.filters.oid,
        account: this.filters.account,
        taccount: this.filters.taccount,
      };
      this.listLoading = true;
      let url =
        base +
        '/orderinfo/list?currentPage=' +
        this.page.currentPage +
        '&pageSize=' +
        this.page.pageSize;
      request.post(url, para).then((res) => {
        if (res.resdata.length > 0) {
          this.isPage = true;
        } else {
          this.isPage = false;
        }
        this.page.totalCount = res.count;
        this.datalist = res.resdata;
        this.listLoading = false;
      });
    },
    //查询
    query() {
      this.getDatas();
    },

    // 查看
    handleShow(index, row) {
      this.$router.push({
        path: '/OrderinfoDetail',
        query: {
          id: row.oid,
        },
      });
    },
  },
};
</script>
<style scoped></style>
