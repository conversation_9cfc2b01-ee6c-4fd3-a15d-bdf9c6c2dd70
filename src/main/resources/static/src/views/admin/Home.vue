<template>

<div class="page-title-box">
                <ol class="breadcrumb float-right">
                    <li class="breadcrumb-item"><a href="javascript:void(0);" id="title1">个人中心</a></li>
                    <li class="breadcrumb-item active" id="title2">{{ this.$route.meta.title }}</li>
                </ol>
                <h4 class="page-title" id="title3">{{ this.$route.meta.title }}</h4>
            </div>
                                     
  

  <div style="width: 100%;line-height: 30px;text-align: center; padding: 100px;" id="home">

    账号：<b style="color: red;">{{ userLname }}</b>，
    身份：<b style="color: red;">{{ role }}</b><br>

      您好，欢迎使用家教服务平台！<br>
     
  

  </div>



 
</template>

<script>
  export default {
    data() {
      return {
        userLname: "",
        role: "",
      };
    },
    mounted() {
      this.userLname = sessionStorage.getItem("userLname");
      this.role = sessionStorage.getItem("role");  

    },
  };

</script>

<style scoped></style>

