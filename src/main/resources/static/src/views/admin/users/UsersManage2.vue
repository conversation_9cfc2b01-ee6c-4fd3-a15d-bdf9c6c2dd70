<template>
  <div class="page-title-box">
    <ol class="breadcrumb float-right">
      <li class="breadcrumb-item"><a href="javascript:void(0);" id="title1">家长管理</a></li>
      <li class="breadcrumb-item active" id="title2">{{ this.$route.meta.title }}</li>
    </ol>
    <h4 class="page-title" id="title3">{{ this.$route.meta.title }}</h4>
  </div>

  <div style="width: 100%; line-height: 30px; text-align: left">
    <el-col :span="24" style="padding-bottom: 0px; margin-left: 10px">
      <el-form :inline="true" :model="filters">
        <el-form-item>
          <el-input v-model="filters.account" placeholder="账号" size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="filters.uname" placeholder="姓名" size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="filters.phone" placeholder="手机号码" size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="query" icon="el-icon-search">搜索</el-button>
        </el-form-item>
      </el-form>
    </el-col>

    <el-table :data="datalist" border stripe style="width: 100%" v-loading="listLoading" highlight-current-row
      max-height="600" size="small">
      <el-table-column prop="account" label="账号" align="center"></el-table-column>
      <el-table-column prop="password" label="密码" align="center"></el-table-column>
      <el-table-column prop="uname" label="姓名" align="center"></el-table-column>
      <el-table-column prop="gender" label="孩子性别" align="center"></el-table-column>
      <el-table-column prop="age" label="孩子年龄" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机号码" align="center"></el-table-column>
      <el-table-column prop="avatar" label="个人头像" width="70" align="center">
        <template #default="scope">
          <img :src="'http://localhost:8088/TutoringServicePlatform/' + scope.row.avatar"
            style="width: 50px; height: 50px" />
        </template>
      </el-table-column>
      <el-table-column prop="regtime" label="注册时间" align="center"></el-table-column>
      <el-table-column label="操作" min-width="300" align="center">
        <template #default="scope">
          <el-button type="primary" size="mini" @click="handleShow(scope.$index, scope.row)" icon="el-icon-zoom-in"
            style="padding: 3px 6px 3px 6px">详情</el-button>
          <el-button type="success" size="mini" @click="handleApprove(scope.$index, scope.row)" icon="el-icon-check"
            style="padding: 3px 6px 3px 6px">审核通过</el-button>
          <el-button type="danger" size="mini" @click="handleReject(scope.$index, scope.row)" icon="el-icon-close"
            style="padding: 3px 6px 3px 6px">审核不通过</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @current-change="handleCurrentChange" :current-page="page.currentPage" :page-size="page.pageSize"
      background layout="total, prev, pager, next, jumper" :total="page.totalCount"
      style="float: right; margin: 10px 20px 0 0"></el-pagination>
  </div>

  <el-dialog title="审核不通过原因" v-model="rejectDialogVisible" width="30%">
    <el-input type="textarea" v-model="rejectReason" placeholder="请输入审核不通过原因"></el-input>
    <span slot="footer" class="dialog-footer">
      <el-button @click="rejectDialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleRejectConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import request, { base } from '../../../../utils/http';
export default {
  name: 'users',
  components: {},
  data() {
    return {
      filters: {
        //列表查询参数
        account: '',
        uname: '',
        phone: '',
      },

      page: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页显示条目个数
        totalCount: 0, // 总条目数
      },
      isClear: false,

      listLoading: false, //列表加载状态
      btnLoading: false, //保存按钮加载状态
      datalist: [], //表格数据

      rejectDialogVisible: false, // 审核不通过弹窗是否显示
      rejectReason: '', // 审核不通过原因
      currentRow: null, // 当前操作的用户行数据
    };
  },
  created() {//
    this.getDatas();
  },

  methods: {
    // 删除家长
    handleDelete(index, row) {
      this.$confirm('确认删除该记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.listLoading = true;
          let url = base + '/users/del?id=' + row.account;
          request.post(url).then((res) => {
            this.listLoading = false;

            this.$message({
              message: '删除成功',
              type: 'success',
              offset: 320,
            });
            this.getDatas();
          });
        })
        .catch(() => { });
    },

    // 分页
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getDatas();
    },

    //获取列表数据
    getDatas() {
      let para = {
        account: this.filters.account,
        uname: this.filters.uname,
        phone: this.filters.phone,
        uflag: '待审核',
      };
      this.listLoading = true;
      let url =
        base +
        '/users/list?currentPage=' +
        this.page.currentPage +
        '&pageSize=' +
        this.page.pageSize;
      request.post(url, para).then((res) => {
        if (res.resdata.length > 0) {
          this.isPage = true;
        } else {
          this.isPage = false;
        }
        this.page.totalCount = res.count;
        this.datalist = res.resdata;
        this.listLoading = false;
      });
    },
    //查询
    query() {
      this.getDatas();
    },

    // 查看
    handleShow(index, row) {
      this.$router.push({
        path: '/UsersDetail',
        query: {
          id: row.account,
        },
      });
    },

    // 编辑
    handleEdit(index, row) {
      this.$router.push({
        path: '/UsersEdit',
        query: {
          id: row.account,
        },
      });
    },

    // 审核通过
    handleApprove(index, row) {
      this.$confirm('确认审核通过该家长吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.listLoading = true;
          let para = {
            account: row.account,
            uflag: '审核通过',
          };
          let url = base + '/users/update';
          request.post(url, para).then((res) => {
            this.listLoading = false;
            this.$message({
              message: '审核通过成功',
              type: 'success',
              offset: 320,
            });
            this.getDatas();
          });
        })
        .catch(() => { });
    },

    // 审核不通过
    handleReject(index, row) {
      this.currentRow = row; // 保存当前行数据
      this.rejectDialogVisible = true; // 显示弹窗
    },

    // 审核不通过确认
    handleRejectConfirm() {
      if (!this.rejectReason) {
        this.$message.warning('请填写审核不通过原因');
        return;
      }
      this.listLoading = true;
      let para = {
        account: this.currentRow.account,
        uflag: '审核不通过：' + this.rejectReason, // 将填写的原因添加到 uflag 中
      };
      let url = base + '/users/update';
      request.post(url, para).then((res) => {
        this.listLoading = false;
        this.rejectDialogVisible = false; // 关闭弹窗
        this.rejectReason = ''; // 清空原因
        this.$message({
          message: '审核不通过成功',
          type: 'success',
          offset: 320,
        });
        this.getDatas();
      });
    },
  },
};
</script>
<style scoped></style>
