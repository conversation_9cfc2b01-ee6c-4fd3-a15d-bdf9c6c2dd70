<template>
  <div class="page-title-box">
    <ol class="breadcrumb float-right">
      <li class="breadcrumb-item"><a href="javascript:void(0);" id="title1">家长管理</a></li>
      <li class="breadcrumb-item active" id="title2">{{ this.$route.meta.title }}</li>
    </ol>
    <h4 class="page-title" id="title3">{{ this.$route.meta.title }}</h4>
  </div>


    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" align="left">
<el-form-item label="账号">
{{formData.account}}</el-form-item>
<el-form-item label="密码">
{{formData.password}}</el-form-item>
<el-form-item label="姓名">
{{formData.uname}}</el-form-item>
<el-form-item label="孩子性别">
{{formData.gender}}</el-form-item>
<el-form-item label="孩子年龄">
{{formData.age}}</el-form-item>
<el-form-item label="手机号码">
{{formData.phone}}</el-form-item>
<el-form-item label="电子邮箱">
{{formData.email}}</el-form-item>
<el-form-item label="联系地址">
{{formData.address}}</el-form-item>
<el-form-item label="个人头像" prop="avatar">
<img v-if="formData.avatar" :src="base.replace('/api', '') + '/' + formData.avatar" style="width: 150px;height: 150px" />
</el-form-item>
<el-form-item label="注册时间">
{{formData.regtime}}</el-form-item>
<el-form-item label="审核状态">
{{formData.uflag}}</el-form-item>
<el-form-item>
<el-button type="info" size="small" @click="back" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>



    </div>

</template>

<script>
        
        import request, { base } from "../../../../utils/http";
        export default {
            name: 'UsersDetail',
            components: {
            },
            data() {
                return {
                    id: '',
                    formData: {}, //表单数据         
        
                };
            },
            created() {
                this.id = this.$route.query.id; //获取参数
                this.getDatas();
            },
        
        
            methods: {
        
                //获取列表数据
                getDatas() {
                    let para = {
                    };
                    this.listLoading = true;
                    let url = base + "/users/get?id=" + this.id;
                    request.post(url, para).then((res) => {
                        this.formData = JSON.parse(JSON.stringify(res.resdata));
                        this.listLoading = false;
                    });
                },
        
                // 返回
                back() {
                    //返回上一页
                    this.$router.go(-1);
                },
        
            },
        }

</script>
<style scoped>
</style>
 

