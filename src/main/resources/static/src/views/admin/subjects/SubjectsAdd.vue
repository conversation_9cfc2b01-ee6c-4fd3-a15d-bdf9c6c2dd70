<template>
  <div class="page-title-box">
    <ol class="breadcrumb float-right">
      <li class="breadcrumb-item"><a href="javascript:void(0);" id="title1">科目管理</a></li>
      <li class="breadcrumb-item active" id="title2">{{ this.$route.meta.title }}</li>
    </ol>
    <h4 class="page-title" id="title3">{{ this.$route.meta.title }}</h4>
  </div>


    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules"  align="left">
<el-form-item label="科目名称" prop="subname">
<el-input v-model="formData.subname" placeholder="科目名称"  style="width:50%;" ></el-input>
</el-form-item>
<el-form-item>
<el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
<el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>



    </div>

</template>

<script>
import request, { base } from "../../../../utils/http";

export default {
  name: 'SubjectsAdd',
  components: {
    
  },  
    data() {
      return {   
        uploadVisible: false, 
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          subname: [{ required: true, message: '请输入科目名称', trigger: 'blur' },
],        },

      };
    },
    mounted() {
    
    },

 
    methods: {    
   // 添加
    save() {       
         this.$refs["formDataRef"].validate((valid) => { //验证表单
           if (valid) {
             let url = base + "/subjects/add";
             this.btnLoading = true;
             request.post(url, this.formData).then((res) => { //发送请求         
               if (res.code == 200) {
                 this.$message({
                   message: "操作成功",
                   type: "success",
                   offset: 320,
                 });              
                this.$router.push({
                path: "/SubjectsManage",
                });
               } else {
                 this.$message({
                   message: res.msg,
                   type: "error",
                   offset: 320,
                 });
               }
               this.btnLoading=false;
             });
           }        
           
         });
    },
    
       // 返回
        goBack() {
          this.$router.push({
            path: "/SubjectsManage",
          });
        },       
              
          
           
           
      },
}

</script>
<style scoped>
</style>
 

