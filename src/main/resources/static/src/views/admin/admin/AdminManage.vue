<template>
  <div class="page-title-box">
    <ol class="breadcrumb float-right">
      <li class="breadcrumb-item"><a href="javascript:void(0);" id="title1">管理员管理</a></li>
      <li class="breadcrumb-item active" id="title2">{{ this.$route.meta.title }}</li>
    </ol>
    <h4 class="page-title" id="title3">{{ this.$route.meta.title }}</h4>
  </div>

  <div style="width: 100%; line-height: 30px; text-align: left">
    <el-table
      :data="datalist"
      border
      stripe
      style="width: 100%"
      v-loading="listLoading"
      highlight-current-row
      max-height="600"
      size="small"
    >
      <el-table-column prop="aname" label="账号" align="center"></el-table-column>
      <el-table-column prop="loginpassword" label="登录密码" align="center"></el-table-column>
      <el-table-column prop="arole" label="身份" align="center"></el-table-column>
      <el-table-column label="操作" min-width="200" align="center">
        <template #default="scope">
          <el-button
            type="success"
            size="mini"
            @click="handleEdit(scope.$index, scope.row)"
            icon="el-icon-edit"
            style="padding: 3px 6px 3px 6px"
            >编辑</el-button
          >
          <el-button
            type="danger"
            size="mini"
            @click="handleDelete(scope.$index, scope.row)"
            icon="el-icon-delete"
            style="padding: 3px 6px 3px 6px"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="page.currentPage"
      :page-size="page.pageSize"
      background
      layout="total, prev, pager, next, jumper"
      :total="page.totalCount"
      style="float: right; margin: 10px 20px 0 0"
    ></el-pagination>
  </div>
</template>

<script>
import request, { base } from '../../../../utils/http';
export default {
  name: 'admin',
  components: {},
  data() {
    return {
      page: {
        currentPage: 1, // 当前页
        pageSize: 10, // 每页显示条目个数
        totalCount: 0, // 总条目数
      },
      isClear: false,

      listLoading: false, //列表加载状态
      btnLoading: false, //保存按钮加载状态
      datalist: [], //表格数据
    };
  },
  created() {
    this.getDatas();
  },

  methods: {
    // 删除管理员
    handleDelete(index, row) {
      this.$confirm('确认删除该记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.listLoading = true;
          let url = base + '/admin/del?id=' + row.aid;
          request.post(url).then((res) => {
            this.listLoading = false;

            this.$message({
              message: '删除成功',
              type: 'success',
              offset: 320,
            });
            this.getDatas();
          });
        })
        .catch(() => {});
    },

    // 分页
    handleCurrentChange(val) {
      this.page.currentPage = val;
      this.getDatas();
    },

    //获取列表数据
    getDatas() {
      let para = {
        arole: '管理员',
      };
      this.listLoading = true;
      let url =
        base +
        '/admin/list?currentPage=' +
        this.page.currentPage +
        '&pageSize=' +
        this.page.pageSize;
      request.post(url, para).then((res) => {
        if (res.resdata.length > 0) {
          this.isPage = true;
        } else {
          this.isPage = false;
        }
        this.page.totalCount = res.count;
        this.datalist = res.resdata;
        this.listLoading = false;
      });
    },

    // 查看
    handleShow(index, row) {
      this.$router.push({
        path: '/AdminDetail',
        query: {
          id: row.aid,
        },
      });
    },

    // 编辑
    handleEdit(index, row) {
      this.$router.push({
        path: '/AdminEdit',
        query: {
          id: row.aid,
        },
      });
    },
  },
};
</script>
<style scoped></style>
