<template>
  <div class="page-title-box">
    <ol class="breadcrumb float-right">
      <li class="breadcrumb-item"><a href="javascript:void(0);" id="title1">管理员管理</a></li>
      <li class="breadcrumb-item active" id="title2">{{ this.$route.meta.title }}</li>
    </ol>
    <h4 class="page-title" id="title3">{{ this.$route.meta.title }}</h4>
  </div>

  <div style="width: 100%; line-height: 30px; text-align: left">
    <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules" align="left">
      <el-form-item label="账号" prop="aname">
        <el-input v-model="formData.aname" placeholder="账号" style="width: 50%"></el-input>
      </el-form-item>
      <el-form-item label="登录密码" prop="loginpassword">
        <el-input
          v-model="formData.loginpassword"
          placeholder="登录密码"
          style="width: 50%"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          size="small"
          @click="save"
          :loading="btnLoading"
          icon="el-icon-upload"
          >提 交</el-button
        >
        <el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import request, { base } from '../../../../utils/http';

export default {
  name: 'AdminAdd',
  components: {},
  data() {
    return {
      uploadVisible: false,
      btnLoading: false, //保存按钮加载状态
      formData: {}, //表单数据
      addrules: {
        aname: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        loginpassword: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
      },
    };
  },
  mounted() {},

  methods: {
    // 添加
    save() {
      this.$refs['formDataRef'].validate((valid) => {
        //验证表单
        if (valid) {
          let url = base + '/admin/add';
          this.btnLoading = true;
          this.formData.arole = '管理员';
          request.post(url, this.formData).then((res) => {
            //发送请求
            if (res.code == 200) {
              this.$message({
                message: '操作成功',
                type: 'success',
                offset: 320,
              });
              this.$router.push({
                path: '/AdminManage',
              });
            } else {
              this.$message({
                message: res.msg,
                type: 'error',
                offset: 320,
              });
            }
            this.btnLoading = false;
          });
        }
      });
    },

    // 返回
    goBack() {
      this.$router.push({
        path: '/AdminManage',
      });
    },
  },
};
</script>
<style scoped></style>
