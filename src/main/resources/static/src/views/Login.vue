<template>
  <body class="bg-account-pages">
    <section>
      <div class="container">
        <div class="row">
          <div class="col-12">
            <div class="wrapper-page">
              <div class="account-pages">
                <div class="account-box">
                  <!-- Logo box-->
                  <div class="account-logo-box">
                    <h2 class="text-uppercase text-center">
                      <a class="text-success">
                        <span><img src="assets/img/logo_sm.c0ad174b.png" alt="" height="28" />
                          &nbsp;&nbsp;家教服务平台
                        </span>
                      </a>
                    </h2>
                  </div>

                  <div class="account-content">
                    <form action="#">
                      <div class="form-group mb-3">
                        <label for="emailaddress" class="font-weight-medium">账号</label>
                        <input
                          class="form-control"
                          type="text"
                          required=""
                          placeholder="请输入账号"
                          v-model="loginModel.username"
                        />
                      </div>

                      <div class="form-group mb-3">
                        <label for="password" class="font-weight-medium">密码</label>
                        <input
                          class="form-control"
                          type="password"
                          required=""
                          placeholder="请输入密码"
                          v-model="loginModel.password"
                        />
                      </div>

                      <div class="form-group row text-center">
                        <div class="col-12">
                          <button
                            class="btn btn-block btn-success waves-effect waves-light"
                            type="submit"
                            @click.prevent="login"
                          >
                            登录
                          </button>
                        </div>
                      </div>
                    </form>

                    <!--                                        <div class="row mt-3">
                                            <div class="col-12 text-center">
                                                <p class="text-muted">没有账号? <a href="auth-register.html" class="text-dark m-l-5"><b>注册</b></a></p>
                                            </div>
                                        </div>-->
                  </div>
                  <!-- end account-content -->
                </div>
                <!-- end account-box -->
              </div>
              <!-- end account-page-->
            </div>
            <!-- end wrapper-page -->
          </div>
          <!-- end col -->
        </div>
        <!-- end row -->
      </div>
      <!-- end container -->
    </section>
  </body>
</template>

<script>
import request, { base } from '../../utils/http';
export default {
  name: 'Login',
  data() {
    return {
      year: new Date().getFullYear(),
      loginModel: {
        username: '',
        password: '',
        radio: '管理员',
      },
      loginModel2: {},
    };
  },
  mounted() {},
  created() {},
  methods: {
    login() {
      let that = this;

      if (that.loginModel.username == '') {
        that.$message({
          message: '请输入账号',
          type: 'warning',
        });
        return;
      }
      if (that.loginModel.password == '') {
        that.$message({
          message: '请输入密码',
          type: 'warning',
        });
        return;
      }

      this.loading = true;
      let url = base + '/admin/login';
      this.loginModel2.aname = this.loginModel.username;
      this.loginModel2.loginpassword = this.loginModel.password;
      request.post(url, this.loginModel2).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          console.log(JSON.stringify(res.resdata));
          sessionStorage.setItem('user', JSON.stringify(res.resdata));
          sessionStorage.setItem('userLname', res.resdata.aname);
          sessionStorage.setItem('role', res.resdata.arole);
          this.$router.push('/main');
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          });
        }
      });
    },
  },
};
</script>

<style scoped>
@import url(../assets/css/app.css);
</style>
