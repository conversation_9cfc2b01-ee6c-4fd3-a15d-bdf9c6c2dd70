import {createRouter, createWebHashHistory} from 'vue-router';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login'),
    meta: {
      requireAuth: false,
    },
  },

  {
    path: '/main',
    name: 'Main',
    component: () => import('../views/Main'),
    redirect: '/home',
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('../views/admin/Home'),
        meta: {
          requireAuth: true,
          title: '首页',
        },
      },

      {
        path: '/tutorsEdit',
        name: 'TutorsEdit',
        component: () => import('../views/admin/tutors/TutorsEdit'),
        meta: { requiresAuth: true, title: '家教修改' },
      },
      {
        path: '/tutorsManage',
        name: 'TutorsManage',
        component: () => import('../views/admin/tutors/TutorsManage'),
        meta: { requiresAuth: true, title: '家教管理' },
      },
      {
        path: '/tutorsManage2',
        name: 'TutorsManage2',
        component: () => import('../views/admin/tutors/TutorsManage2'),
        meta: { requiresAuth: true, title: '审核家教' },
      },
      {
        path: '/tutorsManage3',
        name: 'TutorsManage3',
        component: () => import('../views/admin/tutors/TutorsManage3'),
        meta: { requiresAuth: true, title: '审核不通过家教' },
      },
      {
        path: '/tutorsDetail',
        name: 'TutorsDetail',
        component: () => import('../views/admin/tutors/TutorsDetail'),
        meta: { requiresAuth: true, title: '家教详情' },
      },
      {
        path: '/usersEdit',
        name: 'UsersEdit',
        component: () => import('../views/admin/users/UsersEdit'),
        meta: { requiresAuth: true, title: '家长修改' },
      },
      {
        path: '/usersManage',
        name: 'UsersManage',
        component: () => import('../views/admin/users/UsersManage'),
        meta: { requiresAuth: true, title: '家长管理' },
      },
      {
        path: '/usersManage2',
        name: 'UsersManage2',
        component: () => import('../views/admin/users/UsersManage2'),
        meta: { requiresAuth: true, title: '审核家长' },
      },
      {
        path: '/usersManage3',
        name: 'UsersManage3',
        component: () => import('../views/admin/users/UsersManage3'),
        meta: { requiresAuth: true, title: '审核不通过家长' },
      },
      {
        path: '/usersDetail',
        name: 'UsersDetail',
        component: () => import('../views/admin/users/UsersDetail'),
        meta: { requiresAuth: true, title: '家长详情' },
      },
      {
        path: '/categorysAdd',
        name: 'CategorysAdd',
        component: () => import('../views/admin/categorys/CategorysAdd'),
        meta: { requiresAuth: true, title: '家教类型添加' },
      },
      {
        path: '/categorysEdit',
        name: 'CategorysEdit',
        component: () => import('../views/admin/categorys/CategorysEdit'),
        meta: { requiresAuth: true, title: '家教类型修改' },
      },
      {
        path: '/categorysManage',
        name: 'CategorysManage',
        component: () => import('../views/admin/categorys/CategorysManage'),
        meta: { requiresAuth: true, title: '家教类型管理' },
      },
      {
        path: '/adminAdd',
        name: 'AdminAdd',
        component: () => import('../views/admin/admin/AdminAdd'),
        meta: { requiresAuth: true, title: '管理员添加' },
      },
      {
        path: '/adminEdit',
        name: 'AdminEdit',
        component: () => import('../views/admin/admin/AdminEdit'),
        meta: { requiresAuth: true, title: '管理员修改' },
      },
      {
        path: '/adminManage',
        name: 'AdminManage',
        component: () => import('../views/admin/admin/AdminManage'),
        meta: { requiresAuth: true, title: '管理员管理' },
      },
      {
        path: '/hometutorinfoAdd',
        name: 'HometutorinfoAdd',
        component: () => import('../views/admin/hometutorinfo/HometutorinfoAdd'),
        meta: { requiresAuth: true, title: '家教资讯添加' },
      },
      {
        path: '/hometutorinfoEdit',
        name: 'HometutorinfoEdit',
        component: () => import('../views/admin/hometutorinfo/HometutorinfoEdit'),
        meta: { requiresAuth: true, title: '家教资讯修改' },
      },
      {
        path: '/hometutorinfoManage',
        name: 'HometutorinfoManage',
        component: () => import('../views/admin/hometutorinfo/HometutorinfoManage'),
        meta: { requiresAuth: true, title: '家教资讯管理' },
      },
      {
        path: '/hometutorinfoDetail',
        name: 'HometutorinfoDetail',
        component: () => import('../views/admin/hometutorinfo/HometutorinfoDetail'),
        meta: { requiresAuth: true, title: '家教资讯详情' },
      },
      {
        path: '/feedbackEdit',
        name: 'FeedbackEdit',
        component: () => import('../views/admin/feedback/FeedbackEdit'),
        meta: { requiresAuth: true, title: '留言反馈修改' },
      },
      {
        path: '/feedbackManage',
        name: 'FeedbackManage',
        component: () => import('../views/admin/feedback/FeedbackManage'),
        meta: { requiresAuth: true, title: '留言反馈管理' },
      },
      {
        path: '/feedbackDetail',
        name: 'FeedbackDetail',
        component: () => import('../views/admin/feedback/FeedbackDetail'),
        meta: { requiresAuth: true, title: '留言反馈详情' },
      },
      {
        path: '/subjectsAdd',
        name: 'SubjectsAdd',
        component: () => import('../views/admin/subjects/SubjectsAdd'),
        meta: { requiresAuth: true, title: '科目添加' },
      },
      {
        path: '/subjectsEdit',
        name: 'SubjectsEdit',
        component: () => import('../views/admin/subjects/SubjectsEdit'),
        meta: { requiresAuth: true, title: '科目修改' },
      },
      {
        path: '/subjectsManage',
        name: 'SubjectsManage',
        component: () => import('../views/admin/subjects/SubjectsManage'),
        meta: { requiresAuth: true, title: '科目管理' },
      },
      {
        path: '/orderinfoManage',
        name: 'OrderinfoManage',
        component: () => import('../views/admin/orderinfo/OrderinfoManage'),
        meta: { requiresAuth: true, title: '订单管理' },
      },
      {
        path: '/orderinfoDetail',
        name: 'OrderinfoDetail',
        component: () => import('../views/admin/orderinfo/OrderinfoDetail'),
        meta: { requiresAuth: true, title: '订单详情' },
      },

      {
        path: '/password',
        name: 'Password',
        component: () => import('../views/admin/system/Password'),
        meta: {
          requireAuth: true,
          title: '修改密码',
        },
      },
    ],
  },

  {
    path: '/',
    name: '/',
    component: () => import('../views/Index'),
    redirect: '/default',
    children: [
      {
        path: '/default',
        name: 'Default',
        component: () => import('../views/web/Default'),
      },
    ],
  },
  {
    path: '/index',
    name: 'Index',
    component: () => import('../views/Index'),
    redirect: '/default',
    children: [
      {
        path: '/default',
        name: 'Default',
        component: () => import('../views/web/Default'),
      },
    ],
  },

  {
    path: '/web',
    name: 'web',
    component: () => import('../views/web/Leftnav'),
    children: [
      {
        path: '/ureg',
        name: 'Ureg',
        component: () => import('../views/web/Ureg'),
        meta: {
          title: '家长注册',
        },
      },

      {
        path: '/ulogin',
        name: 'Ulogin',
        component: () => import('../views/web/Ulogin'),
        meta: {
          title: '家长登录',
        },
      },

      {
        path: '/treg',
        name: 'Treg',
        component: () => import('../views/web/Treg'),
        meta: {
          title: '家教注册',
        },
      },

      {
        path: '/tlogin',
        name: 'Tlogin',
        component: () => import('../views/web/Tlogin'),
        meta: {
          title: '家教登录',
        },
      },

      {
        path: '/hometutorinfolist',
        name: 'HometutorinfoList',
        component: () => import('../views/web/HometutorinfoList'),
        meta: {
          title: '家教资讯',
        },
      },

      {
        path: '/tutorslist',
        name: 'TutorsList',
        component: () => import('../views/web/TutorsList'),
        meta: {
          title: '家教列表',
        },
      },

      {
        path: '/hometutorinfoview',
        name: 'HometutorinfoView',
        component: () => import('../views/web/HometutorinfoView'),
        meta: {
          title: '家教资讯详情',
        },
      },

      {
        path: '/tutorsview',
        name: 'TutorsView',
        component: () => import('../views/web/TutorsView'),
        meta: {
          title: '家教详情',
        },
      },

      {
        path: '/feedback',
        name: 'Feedback',
        component: () => import('../views/web/Feedback'),
        meta: {
          title: '留言反馈',
        },
      },
      {
        path: '/Ai',
        name: 'Ai',
        component: () => import('../views/web/Ai'),
        meta: {
          title: 'AI答疑',
        },
      },
    ],
  },

  {
    path: '/menunav',
    name: 'Menunav',
    component: () => import('../views/web/Menunav'),
    children: [
      {
        path: '/uweclome',
        name: 'Uweclome',
        component: () => import('../views/web/Uweclome'),
        meta: {
          title: '欢迎页面',
        },
      },

      {
        path: '/chat_received',
        name: 'ChatReceived',
        component: () => import('../views/web/ChatReceived'),
        meta: {
          title: '收到的聊天',
        },
      },

      {
        path: '/uinfo',
        name: 'Uinfo',
        component: () => import('../views/web/Uinfo'),
        meta: {
          title: '修改个人信息',
        },
      },

      {
        path: '/upassword',
        name: 'Upassword',
        component: () => import('../views/web/Upassword'),
        meta: {
          title: '修改密码',
        },
      },

      {
        path: '/tinfo',
        name: 'Tinfo',
        component: () => import('../views/web/Tinfo'),
        meta: {
          title: '修改个人信息',
        },
      },

      {
        path: '/tpassword',
        name: 'Tpassword',
        component: () => import('../views/web/Tpassword'),
        meta: {
          title: '修改密码',
        },
      },

      {
        path: '/feedback_manage',
        name: 'Feedback_Manage',
        component: () => import('../views/web/Feedback_Manage'),
        meta: {
          title: '留言反馈管理',
        },
      },

      {
        path: '/feedback_show',
        name: 'Feedback_Show',
        component: () => import('../views/web/Feedback_Show'),
        meta: {
          title: '留言反馈详情',
        },
      },

      {
        path: '/orderinfo_add',
        name: 'Orderinfo_Add',
        component: () => import('../views/web/Orderinfo_Add'),
        meta: {
          title: '提交订单',
        },
      },

      {
        path: '/orderinfo_manage',
        name: 'Orderinfo_Manage',
        component: () => import('../views/web/Orderinfo_Manage'),
        meta: {
          title: '我的订单',
        },
      },

      {
        path: '/orderinfo_manage2',
        name: 'Orderinfo_Manage2',
        component: () => import('../views/web/Orderinfo_Manage2'),
        meta: {
          title: '管理订单',
        },
      },

      {
        path: '/orderinfo_show',
        name: 'Orderinfo_Show',
        component: () => import('../views/web/Orderinfo_Show'),
        meta: {
          title: '订单详情',
        },
      },
    ],
  },
];
console.log(process.env.BASE_URL)
const router = createRouter({
  history: createWebHashHistory(process.env.BASE_URL),
  routes,
});

router.beforeEach((to, from, next) => {
  if (to.name == '/') {
    sessionStorage.removeItem('userLname');
    sessionStorage.removeItem('role');
  }
  let currentUser = sessionStorage.getItem('userLname');
  console.log(to + '  to.meta.requireAuth');

  if (to.meta.requireAuth) {
    if (!currentUser && to.name != 'Login') {
      console.log('no user');
      next({ name: 'Login' });
    } else {
      next();
    }
  } else {
    next();
  }
});

export default router;
