package com.bkty.turtorsystem.entity;


public class Tutors {

  private String taccount;
  private String password;
  private String tuname;
  private String gender;
  private long age;
  private String phone;
  private String email;
  private String education;
  private String teachingexperience;
  private String photo;
  private long catid;
  private String suids;
  private String subval;
  private double price;
  private String introduction;
  private java.sql.Timestamp registrationdate;
  private String tflag;
  private String tflag2;
  
  // 非数据库字段，用于前端传递额外的查询条件
  private String condition;
  
  // 非数据库字段，用于显示家教类型名称
  private String catname;

  public String getCatname() {
    return catname;
  }

  public void setCatname(String catname) {
    this.catname = catname;
  }

  public String getCondition() {
    return condition;
  }

  public void setCondition(String condition) {
    this.condition = condition;
  }

  public String getTaccount() {
    return taccount;
  }

  public void setTaccount(String taccount) {
    this.taccount = taccount;
  }


  public String getPassword() {
    return password;
  }

  public void setPassword(String password) {
    this.password = password;
  }


  public String getTuname() {
    return tuname;
  }

  public void setTuname(String tuname) {
    this.tuname = tuname;
  }


  public String getGender() {
    return gender;
  }

  public void setGender(String gender) {
    this.gender = gender;
  }


  public long getAge() {
    return age;
  }

  public void setAge(long age) {
    this.age = age;
  }


  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }


  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }


  public String getEducation() {
    return education;
  }

  public void setEducation(String education) {
    this.education = education;
  }


  public String getTeachingexperience() {
    return teachingexperience;
  }

  public void setTeachingexperience(String teachingexperience) {
    this.teachingexperience = teachingexperience;
  }


  public String getPhoto() {
    return photo;
  }

  public void setPhoto(String photo) {
    this.photo = photo;
  }


  public long getCatid() {
    return catid;
  }

  public void setCatid(long catid) {
    this.catid = catid;
  }


  public String getSuids() {
    return suids;
  }

  public void setSuids(String suids) {
    this.suids = suids;
  }


  public String getSubval() {
    return subval;
  }

  public void setSubval(String subval) {
    this.subval = subval;
  }


  public double getPrice() {
    return price;
  }

  public void setPrice(double price) {
    this.price = price;
  }


  public String getIntroduction() {
    return introduction;
  }

  public void setIntroduction(String introduction) {
    this.introduction = introduction;
  }


  public java.sql.Timestamp getRegistrationdate() {
    return registrationdate;
  }

  public void setRegistrationdate(java.sql.Timestamp registrationdate) {
    this.registrationdate = registrationdate;
  }


  public String getTflag() {
    return tflag;
  }

  public void setTflag(String tflag) {
    this.tflag = tflag;
  }


  public String getTflag2() {
    return tflag2;
  }

  public void setTflag2(String tflag2) {
    this.tflag2 = tflag2;
  }

}
