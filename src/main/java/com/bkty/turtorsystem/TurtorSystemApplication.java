package com.bkty.turtorsystem;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.beans.factory.annotation.Value;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermissions;

@SpringBootApplication(scanBasePackages = "com.bkty.turtorsystem")
@MapperScan("com.bkty.turtorsystem.mapper")
public class TurtorSystemApplication {

    private static final Logger logger = LoggerFactory.getLogger(TurtorSystemApplication.class);
    
    @Value("${file.upload.path}")
    private String uploadPath;

    public static void main(String[] args) {
        SpringApplication.run(TurtorSystemApplication.class, args);
        System.out.println("http://localhost:8088/TutoringServicePlatform/dist/index.html");
    }
    
    @Bean
    public CommandLineRunner initializeUploadDirectory() {
        return args -> {
            logger.info("应用启动 - 初始化上传目录: {}", uploadPath);
            
            try {
                File uploadDir = new File(uploadPath);
                if (!uploadDir.exists()) {
                    boolean created = uploadDir.mkdirs();
                    logger.info("创建上传目录结果: {}, 路径: {}", created, uploadPath);
                    
                    if (!created) {
                        logger.error("无法创建上传目录: {}", uploadPath);
                    }
                } else {
                    logger.info("上传目录已存在: {}", uploadPath);
                }
                
                // 确保目录有适当的权限
                if (uploadDir.exists()) {
                    boolean readableSet = uploadDir.setReadable(true, false);
                    boolean writableSet = uploadDir.setWritable(true, false);
                    boolean executableSet = uploadDir.setExecutable(true, false);
                    
                    logger.info("设置目录权限 - 可读: {}, 可写: {}, 可执行: {}", 
                              readableSet, writableSet, executableSet);
                    
                    // Windows系统不需要设置POSIX权限
                    if (!System.getProperty("os.name").toLowerCase().contains("windows")) {
                        try {
                            Files.setPosixFilePermissions(Paths.get(uploadPath),
                                PosixFilePermissions.fromString("rwxrwxrwx"));
                            logger.info("设置POSIX权限为777成功");
                        } catch (Exception e) {
                            logger.warn("设置POSIX权限失败: {}", e.getMessage());
                        }
                    } else {
                        logger.info("Windows系统，跳过POSIX权限设置");
                    }
                }
                
                logger.info("上传目录初始化完成: {}", uploadPath);
            } catch (Exception e) {
                logger.error("初始化上传目录时发生错误: ", e);
            }
        };
    }
}
