package com.bkty.turtorsystem.service;

import com.bkty.turtorsystem.entity.Hometutorinfo;
import java.util.Map;

/**
 * 家教资讯服务接口
 */
public interface HometutorinfoService {
    
    /**
     * 分页查询家教资讯列表
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param hometutorinfo 查询条件
     * @return 包含家教资讯列表和总数量的Map
     */
    Map<String, Object> listHometutorinfo(Integer currentPage, Integer pageSize, Hometutorinfo hometutorinfo);
    
    /**
     * 添加家教资讯
     * @param hometutorinfo 家教资讯信息对象
     * @return 添加结果
     */
    Map<String, Object> addHometutorinfo(Hometutorinfo hometutorinfo);
    
    /**
     * 根据ID获取家教资讯
     * @param id 家教资讯ID
     * @return 家教资讯信息
     */
    Map<String, Object> getHometutorinfoById(Long id);
    
    /**
     * 更新家教资讯
     * @param hometutorinfo 家教资讯信息对象
     * @return 更新结果
     */
    Map<String, Object> updateHometutorinfo(Hometutorinfo hometutorinfo);
    
    /**
     * 删除家教资讯
     * @param id 家教资讯ID
     * @return 删除结果
     */
    Map<String, Object> deleteHometutorinfo(Long id);
}
