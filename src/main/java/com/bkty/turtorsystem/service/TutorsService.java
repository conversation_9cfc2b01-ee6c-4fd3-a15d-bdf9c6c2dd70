package com.bkty.turtorsystem.service;

import com.bkty.turtorsystem.entity.Tutors;
import com.bkty.turtorsystem.response.Result;
import java.util.Map;

/**
 * 家教服务接口
 */
public interface TutorsService {

    /**
     * 家教注册
     * @param tutor 家教对象
     * @return 注册结果
     */
    Result<Void> register(Tu<PERSON> tutor);

    Map<String, Object> listTutors(int currentPage, int pageSize, Tutors tutor, String queryType);

    Map<String, Object> getTutorById(String id);

    Map<String, Object> updateTutor(Tutors tutor);

    Map<String, Object> deleteTutor(String id);
} 