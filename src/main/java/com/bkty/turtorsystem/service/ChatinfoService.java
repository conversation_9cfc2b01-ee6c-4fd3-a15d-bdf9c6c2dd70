package com.bkty.turtorsystem.service;

import com.bkty.turtorsystem.entity.Chatinfo;
import java.util.Map;
import java.util.List;

/**
 * 聊天信息服务接口
 */
public interface ChatinfoService {
    
    /**
     * 分页查询聊天记录
     * @param condition 查询条件
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 包含聊天记录列表和总数量的Map
     */
    Map<String, Object> listChatinfo(String condition, Integer currentPage, Integer pageSize);
    
    /**
     * 添加聊天记录
     * @param chatinfo 聊天信息对象
     * @return 是否添加成功
     */
    boolean addChatinfo(Chatinfo chatinfo);
    
    /**
     * 根据ID获取聊天记录
     * @param cid 聊天记录ID
     * @return 聊天记录
     */
    Chatinfo getChatinfoById(Long cid);
    
    /**
     * 删除聊天记录
     * @param cid 聊天记录ID
     * @return 是否删除成功
     */
    boolean deleteChatinfo(Long cid);
    
    /**
     * 获取用户的聊天联系人列表
     * @param account 用户账号
     * @return 聊天联系人列表
     */
    List<String> getContactsByAccount(String account);
}
