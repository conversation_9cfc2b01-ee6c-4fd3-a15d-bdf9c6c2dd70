package com.bkty.turtorsystem.service;

import com.bkty.turtorsystem.entity.Feedback;
import com.github.pagehelper.PageInfo;

/**
 * 留言反馈服务接口
 */
public interface IFeedbackService {
    /**
     * 添加反馈信息
     * @param feedback 反馈对象
     * @return 受影响的行数
     */
    int addFeedback(Feedback feedback);

    /**
     * 查询反馈列表
     * @param feedback 查询条件
     * @param currentPage 当前页码
     * @param pageSize 每页记录数
     * @return 分页结果
     */
    PageInfo<Feedback> queryFeedbackList(Feedback feedback, Integer currentPage, Integer pageSize);

    /**
     * 更新反馈回复
     * @param id 反馈ID
     * @param reply 回复内容
     * @return 受影响的行数
     */
    int updateFeedbackReply(Long id, String reply);
    
    /**
     * 根据ID获取单个反馈信息
     * @param id 反馈ID
     * @return 反馈信息
     */
    Feedback getFeedbackById(Long id);
    
    /**
     * 根据ID删除反馈
     * @param id 反馈ID
     * @return 受影响的行数
     */
    int deleteFeedbackById(Long id);

    // 后台管理相关方法稍后添加
} 