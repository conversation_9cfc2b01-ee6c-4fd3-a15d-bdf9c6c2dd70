package com.bkty.turtorsystem.service.impl;

import com.bkty.turtorsystem.entity.Tutors;
import com.bkty.turtorsystem.mapper.TutorsMapper;
import com.bkty.turtorsystem.response.Result;
import com.bkty.turtorsystem.service.TutorsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 家教服务实现类
 */
@Service
public class TutorsServiceImpl implements TutorsService {
    // 添加日志记录器
    private static final Logger logger = LoggerFactory.getLogger(TutorsServiceImpl.class);

    @Autowired
    private TutorsMapper tutorsMapper;

    /**
     * 家教注册
     * @param tutor 家教对象
     * @return 注册结果
     */
    @Override
    public Result<Void> register(Tutors tutor) {
        // 检查账号是否已存在
        Tutors existTutor = tutorsMapper.findByAccount(tutor.getTaccount());
        if (existTutor != null) {
            return Result.error("账号已存在，请更换账号");
        }

        // 设置注册时间
        tutor.setRegistrationdate(new Timestamp(new Date().getTime()));
        
        // 设置家教状态（默认为待审核状态）
        tutor.setTflag("待审核");
        
        // 如果没有设置线上/线下标志，设置默认值
        if (tutor.getTflag2() == null || tutor.getTflag2().isEmpty()) {
            tutor.setTflag2("线下"); // 默认为线下授课
        }
        
        // 保存家教信息
        int rows = tutorsMapper.insert(tutor);
        if (rows > 0) {
            logger.info("家教注册成功: {}", tutor.getTaccount());
            return Result.success("注册成功，请等待管理员审核");
        } else {
            logger.error("家教注册失败: 数据库插入失败，影响行数为0");
            return Result.error("注册失败，请稍后再试");
        }
    }

    /**
     * 查询家教列表
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param tutor 查询条件
     * @param queryType 查询类型（null:普通列表, "latest":最新家教, "hot":热门家教）
     * @return 家教列表和分页信息
     */
    @Override
    public Map<String, Object> listTutors(int currentPage, int pageSize, Tutors tutor, String queryType) {
        logger.info("查询家教列表, 页码:{}, 每页大小:{}, 查询类型:{}", currentPage, pageSize, queryType);
        if (tutor != null) {
            logger.info("查询条件: 账号:{}, 姓名:{}, 手机:{}, 状态:{}, 类型:{}, 性别:{}, 授课方式:{}",
                    tutor.getTaccount(), tutor.getTuname(), tutor.getPhone(), tutor.getTflag(), 
                    tutor.getCatid(), tutor.getGender(), tutor.getTflag2());
        }
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 计算偏移量
            int offset = (currentPage - 1) * pageSize;
            
            // 设置查询类型
            if (tutor == null) {
                tutor = new Tutors();
            }
            tutor.setQueryType(queryType);
            
            List<Tutors> tutors;
            int total;
            
            // 根据查询类型使用不同的方法
            if ("hot".equals(queryType)) {
                // 热门家教查询（已接单的家教）
                tutors = tutorsMapper.findHotTutorsByCondition(offset, pageSize, tutor);
                total = tutorsMapper.countHotTutorsByCondition(tutor);
                logger.info("查询热门家教列表成功, 总记录数: {}", total);
            } else {
                // 普通列表或最新家教查询
                tutors = tutorsMapper.findByCondition(offset, pageSize, tutor);
                total = tutorsMapper.countByCondition(tutor);
                logger.info("查询家教列表成功, 总记录数: {}", total);
            }
            
            result.put("code", 200);
            result.put("msg", "获取成功");
            result.put("count", total);
            result.put("resdata", tutors);
        } catch (Exception e) {
            logger.error("查询家教列表异常:", e);
            result.put("code", 500);
            result.put("msg", "查询失败: " + e.getMessage());
            result.put("count", 0);
            result.put("resdata", new ArrayList<>());
        }
        
        return result;
    }
    
    /**
     * 获取单个家教信息
     * @param id 家教账号
     * @return 家教信息
     */
    @Override
    public Map<String, Object> getTutorById(String id) {
        logger.info("获取单个家教信息, id:{}", id);
        Map<String, Object> result = new HashMap<>();
        
        try {
            Tutors tutor = tutorsMapper.findById(id);
            
            if (tutor != null) {
                logger.info("获取家教信息成功, id:{}", id);
                result.put("code", 200);
                result.put("msg", "获取成功");
                result.put("resdata", tutor);
            } else {
                logger.warn("家教不存在, id:{}", id);
                result.put("code", 404);
                result.put("msg", "家教不存在");
            }
        } catch (Exception e) {
            logger.error("查询单个家教异常:", e);
            result.put("code", 500);
            result.put("msg", "查询失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 更新家教信息
     * @param tutor 家教信息
     * @return 更新结果
     */
    @Override
    public Map<String, Object> updateTutor(Tutors tutor) {
        logger.info("更新家教信息, taccount:{}", tutor.getTaccount());
        Map<String, Object> result = new HashMap<>();
        
        if (tutor.getTaccount() == null || tutor.getTaccount().isEmpty()) {
            logger.error("更新家教失败: 账号为空");
            result.put("code", 400);
            result.put("msg", "更新失败: 账号不能为空");
            return result;
        }
        
        try {
            // 先查询家教是否存在
            Tutors existTutor = tutorsMapper.findById(tutor.getTaccount());
            if (existTutor == null) {
                logger.error("更新家教失败: 家教不存在, 账号:{}", tutor.getTaccount());
                result.put("code", 404);
                result.put("msg", "更新失败: 家教不存在");
                return result;
            }
            
            // 执行更新
            int rows = tutorsMapper.update(tutor);
            if (rows > 0) {
                // 更新成功
                logger.info("更新家教成功, taccount:{}", tutor.getTaccount());
                result.put("code", 200);
                result.put("msg", "更新成功");
            } else {
                logger.error("更新家教失败: 数据库更新失败，影响行数为0, taccount:{}", tutor.getTaccount());
                result.put("code", 500);
                result.put("msg", "更新失败: 未能更新数据库记录");
            }
        } catch (Exception e) {
            logger.error("更新家教异常:", e);
            result.put("code", 500);
            result.put("msg", "更新失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 删除家教
     * @param id 家教账号
     * @return 删除结果
     */
    @Override
    public Map<String, Object> deleteTutor(String id) {
        logger.info("删除家教, id:{}", id);
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 先查询家教是否存在
            Tutors existTutor = tutorsMapper.findById(id);
            if (existTutor == null) {
                logger.error("删除家教失败: 家教不存在, id:{}", id);
                result.put("code", 404);
                result.put("msg", "删除失败: 家教不存在");
                return result;
            }
            
            // 执行删除
            int rows = tutorsMapper.delete(id);
            if (rows > 0) {
                // 删除成功
                logger.info("删除家教成功, id:{}", id);
                result.put("code", 200);
                result.put("msg", "删除成功");
            } else {
                logger.error("删除家教失败: 数据库删除失败，影响行数为0, id:{}", id);
                result.put("code", 500);
                result.put("msg", "删除失败: 未能删除数据库记录");
            }
        } catch (Exception e) {
            logger.error("删除家教异常:", e);
            result.put("code", 500);
            result.put("msg", "删除失败: " + e.getMessage());
        }
        
        return result;
    }
} 