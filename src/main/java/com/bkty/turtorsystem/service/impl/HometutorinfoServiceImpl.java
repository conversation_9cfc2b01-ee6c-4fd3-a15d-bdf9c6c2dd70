package com.bkty.turtorsystem.service.impl;

import com.bkty.turtorsystem.entity.Hometutorinfo;
import com.bkty.turtorsystem.mapper.HometutorinfoMapper;
import com.bkty.turtorsystem.service.HometutorinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 家教资讯服务实现类
 */
@Service
public class HometutorinfoServiceImpl implements HometutorinfoService {

    private static final Logger logger = LoggerFactory.getLogger(HometutorinfoServiceImpl.class);

    @Autowired
    private HometutorinfoMapper hometutorinfoMapper;

    @Override
    public Map<String, Object> listHometutorinfo(Integer currentPage, Integer pageSize, Hometutorinfo hometutorinfo) {
        logger.info("查询家教资讯列表, 页码: {}, 每页大小: {}", currentPage, pageSize);

        Map<String, Object> result = new HashMap<>();

        try {
            // 计算偏移量
            int offset = (currentPage - 1) * pageSize;

            // 查询家教资讯列表
            List<Hometutorinfo> list = hometutorinfoMapper.findByCondition(offset, pageSize, hometutorinfo);

            // 查询总数量
            int total = hometutorinfoMapper.countByCondition(hometutorinfo);

            logger.info("查询家教资讯列表成功, 总记录数: {}", total);

            result.put("code", 200);
            result.put("msg", "查询成功");
            result.put("resdata", list);
            result.put("count", total);
            result.put("session", true);
        } catch (Exception e) {
            logger.error("查询家教资讯列表异常:", e);
            result.put("code", 500);
            result.put("msg", "查询失败: " + e.getMessage());
            result.put("session", true);
        }

        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> addHometutorinfo(Hometutorinfo hometutorinfo) {
        logger.info("添加家教资讯, 标题: {}", hometutorinfo.getTitle());

        Map<String, Object> result = new HashMap<>();

        // 参数校验
        if (hometutorinfo.getTitle() == null || hometutorinfo.getTitle().trim().isEmpty()) {
            result.put("code", 400);
            result.put("msg", "资讯标题不能为空");
            result.put("session", true);
            return result;
        }

        if (hometutorinfo.getContent() == null || hometutorinfo.getContent().trim().isEmpty()) {
            result.put("code", 400);
            result.put("msg", "资讯内容不能为空");
            result.put("session", true);
            return result;
        }

        try {
            // 设置发布时间
            hometutorinfo.setPublishtime(new Timestamp(new Date().getTime()));

            // 插入家教资讯
            int rows = hometutorinfoMapper.insert(hometutorinfo);

            if (rows > 0) {
                logger.info("添加家教资讯成功, 标题: {}", hometutorinfo.getTitle());
                result.put("code", 200);
                result.put("msg", "添加成功");
                result.put("resdata", hometutorinfo);
                result.put("session", true);
            } else {
                logger.error("添加家教资讯失败: 数据库插入失败，影响行数为0");
                result.put("code", 500);
                result.put("msg", "添加失败");
                result.put("session", true);
            }
        } catch (Exception e) {
            logger.error("添加家教资讯异常:", e);
            result.put("code", 500);
            result.put("msg", "添加失败: " + e.getMessage());
            result.put("session", true);
        }

        return result;
    }

    @Override
    public Map<String, Object> getHometutorinfoById(Long id) {
        logger.info("获取家教资讯详情, ID: {}", id);

        Map<String, Object> result = new HashMap<>();

        if (id == null || id <= 0) {
            result.put("code", 400);
            result.put("msg", "资讯ID不能为空");
            result.put("session", true);
            return result;
        }

        try {
            Hometutorinfo info = hometutorinfoMapper.findById(id);

            if (info != null) {
                logger.info("获取家教资讯详情成功, ID: {}", id);
                result.put("code", 200);
                result.put("msg", "获取成功");
                result.put("resdata", info);
                result.put("session", true);
            } else {
                logger.warn("家教资讯不存在, ID: {}", id);
                result.put("code", 404);
                result.put("msg", "家教资讯不存在");
                result.put("session", true);
            }
        } catch (Exception e) {
            logger.error("查询家教资讯详情异常:", e);
            result.put("code", 500);
            result.put("msg", "查询失败: " + e.getMessage());
            result.put("session", true);
        }

        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> updateHometutorinfo(Hometutorinfo hometutorinfo) {
        logger.info("更新家教资讯, ID: {}", hometutorinfo.getId());

        Map<String, Object> result = new HashMap<>();

        // 参数校验
        if (hometutorinfo.getId() <= 0) {
            result.put("code", 400);
            result.put("msg", "资讯ID不能为空");
            result.put("session", true);
            return result;
        }

        try {
            // 查询家教资讯是否存在
            Hometutorinfo existInfo = hometutorinfoMapper.findById(hometutorinfo.getId());
            if (existInfo == null) {
                logger.error("更新家教资讯失败: 家教资讯不存在, ID: {}", hometutorinfo.getId());
                result.put("code", 404);
                result.put("msg", "更新失败: 家教资讯不存在");
                result.put("session", true);
                return result;
            }

            // 执行更新
            int rows = hometutorinfoMapper.update(hometutorinfo);

            if (rows > 0) {
                // 获取更新后的数据
                Hometutorinfo updatedInfo = hometutorinfoMapper.findById(hometutorinfo.getId());

                logger.info("更新家教资讯成功, ID: {}", hometutorinfo.getId());
                result.put("code", 200);
                result.put("msg", "操作成功！");
                result.put("resdata", updatedInfo);
                result.put("session", true);
            } else {
                logger.error("更新家教资讯失败: 数据库更新失败，影响行数为0, ID: {}", hometutorinfo.getId());
                result.put("code", 500);
                result.put("msg", "更新失败");
                result.put("session", true);
            }
        } catch (Exception e) {
            logger.error("更新家教资讯异常:", e);
            result.put("code", 500);
            result.put("msg", "更新失败: " + e.getMessage());
            result.put("session", true);
        }

        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> deleteHometutorinfo(Long id) {
        logger.info("删除家教资讯, ID: {}", id);

        Map<String, Object> result = new HashMap<>();

        if (id == null || id <= 0) {
            result.put("code", 400);
            result.put("msg", "资讯ID不能为空");
            result.put("session", true);
            return result;
        }

        try {
            // 先查询家教资讯是否存在
            Hometutorinfo existInfo = hometutorinfoMapper.findById(id);
            if (existInfo == null) {
                logger.error("删除家教资讯失败: 家教资讯不存在, ID: {}", id);
                result.put("code", 404);
                result.put("msg", "删除失败: 家教资讯不存在");
                result.put("session", true);
                return result;
            }

            // 执行删除
            int rows = hometutorinfoMapper.delete(id);

            if (rows > 0) {
                logger.info("删除家教资讯成功, ID: {}", id);
                result.put("code", 200);
                result.put("msg", "删除成功");
                result.put("session", true);
            } else {
                logger.error("删除家教资讯失败: 数据库删除失败，影响行数为0, ID: {}", id);
                result.put("code", 500);
                result.put("msg", "删除失败");
                result.put("session", true);
            }
        } catch (Exception e) {
            logger.error("删除家教资讯异常:", e);
            result.put("code", 500);
            result.put("msg", "删除失败: " + e.getMessage());
            result.put("session", true);
        }

        return result;
    }
}
