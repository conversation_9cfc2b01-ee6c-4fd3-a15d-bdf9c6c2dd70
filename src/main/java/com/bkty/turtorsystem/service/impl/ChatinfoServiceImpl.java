package com.bkty.turtorsystem.service.impl;

import com.bkty.turtorsystem.entity.Chatinfo;
import com.bkty.turtorsystem.mapper.ChatinfoMapper;
import com.bkty.turtorsystem.service.ChatinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 聊天信息服务实现类
 */
@Service
public class ChatinfoServiceImpl implements ChatinfoService {

    private static final Logger logger = LoggerFactory.getLogger(ChatinfoServiceImpl.class);

    @Autowired
    private ChatinfoMapper chatinfoMapper;

    @Override
    public Map<String, Object> listChatinfo(String condition, Integer currentPage, Integer pageSize) {
        logger.info("查询聊天记录列表, 条件: {}, 页码: {}, 每页大小: {}", condition, currentPage, pageSize);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 计算偏移量
            int offset = (currentPage - 1) * pageSize;
            
            // 查询数据
            List<Chatinfo> list = chatinfoMapper.findByCondition(offset, pageSize, condition);
            
            // 获取符合条件的总记录数
            int total = chatinfoMapper.countByCondition(condition);
            
            result.put("code", 200);
            result.put("msg", "获取成功");
            result.put("count", total);
            result.put("resdata", list);
        } catch (Exception e) {
            logger.error("查询聊天记录列表异常:", e);
            result.put("code", 500);
            result.put("msg", "查询失败: " + e.getMessage());
            result.put("count", 0);
            result.put("resdata", new ArrayList<>());
        }
        
        return result;
    }

    @Override
    @Transactional
    public boolean addChatinfo(Chatinfo chatinfo) {
        logger.info("添加聊天记录, 发送人: {}, 接收人: {}", chatinfo.getLname(), chatinfo.getLname2());
        
        try {
            // 如果没有设置发送时间，则设置为当前时间
            if (chatinfo.getSendtime() == null) {
                chatinfo.setSendtime(new Timestamp(new Date().getTime()));
            }
            
            // 插入聊天记录
            int rows = chatinfoMapper.insert(chatinfo);
            
            return rows > 0;
        } catch (Exception e) {
            logger.error("添加聊天记录异常:", e);
            return false;
        }
    }

    @Override
    public Chatinfo getChatinfoById(Long cid) {
        logger.info("获取聊天记录, ID: {}", cid);
        
        try {
            return chatinfoMapper.findById(cid);
        } catch (Exception e) {
            logger.error("获取聊天记录异常:", e);
            return null;
        }
    }

    @Override
    @Transactional
    public boolean deleteChatinfo(Long cid) {
        logger.info("删除聊天记录, ID: {}", cid);
        
        try {
            int rows = chatinfoMapper.delete(cid);
            
            return rows > 0;
        } catch (Exception e) {
            logger.error("删除聊天记录异常:", e);
            return false;
        }
    }

    @Override
    public List<String> getContactsByAccount(String account) {
        logger.info("获取用户聊天联系人列表, 账号: {}", account);
        
        try {
            return chatinfoMapper.findContactsByAccount(account);
        } catch (Exception e) {
            logger.error("获取用户聊天联系人列表异常:", e);
            return new ArrayList<>();
        }
    }
}
