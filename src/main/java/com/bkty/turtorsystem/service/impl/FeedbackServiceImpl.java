package com.bkty.turtorsystem.service.impl;

import com.bkty.turtorsystem.entity.Feedback;
import com.bkty.turtorsystem.mapper.FeedbackMapper;
import com.bkty.turtorsystem.service.IFeedbackService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 留言反馈服务实现类
 */
@Service
public class FeedbackServiceImpl implements IFeedbackService {

    @Autowired
    private FeedbackMapper mapper;

    @Override
    @Transactional // 添加事务管理
    public int addFeedback(Feedback feedback) {
        // 可以在这里添加更复杂的业务逻辑，例如敏感词过滤等
        return mapper.addFeedback(feedback);
    }

    @Override
    public PageInfo<Feedback> queryFeedbackList(Feedback feedback, Integer currentPage, Integer pageSize) {
        // 启用分页
        PageHelper.startPage(currentPage, pageSize);
        // 执行查询
        List<Feedback> list = mapper.queryFeedbackList(feedback);
        // 返回PageInfo对象
        return new PageInfo<>(list);
    }

    @Override
    @Transactional
    public int updateFeedbackReply(Long id, String reply) {
        return mapper.updateFeedbackReply(id, reply);
    }
    
    @Override
    public Feedback getFeedbackById(Long id) {
        return mapper.getFeedbackById(id);
    }
    
    @Override
    @Transactional
    public int deleteFeedbackById(Long id) {
        return mapper.deleteFeedbackById(id);
    }

    // 后台管理相关方法稍后添加
} 