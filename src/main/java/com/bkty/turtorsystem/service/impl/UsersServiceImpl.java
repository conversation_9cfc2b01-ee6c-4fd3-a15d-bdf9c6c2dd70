package com.bkty.turtorsystem.service.impl;

import com.bkty.turtorsystem.entity.Users;
import com.bkty.turtorsystem.mapper.UsersMapper;
import com.bkty.turtorsystem.response.Result;
import com.bkty.turtorsystem.service.UsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 用户服务实现类
 */
@Service
public class UsersServiceImpl implements UsersService {

    @Autowired
    private UsersMapper usersMapper;

    /**
     * 用户注册
     * @param user 用户对象
     * @return 注册结果
     */
    @Override
    public Result<Void> register(Users user) {
        // 检查账号是否已存在
        Users existUser = usersMapper.findByAccount(user.getAccount());
        if (existUser != null) {
            return Result.error("账号已存在，请更换账号");
        }

        // 设置注册时间
        user.setRegtime(new Timestamp(new Date().getTime()));
        
        // 设置用户标识（默认为待审核状态）
        user.setUflag("0");  // 0-待审核，1-已审核
        
        // 保存用户信息
        int rows = usersMapper.insert(user);
        if (rows > 0) {
            return Result.success("注册成功，请等待管理员审核");
        } else {
            return Result.error("注册失败，请稍后再试");
        }
    }
} 