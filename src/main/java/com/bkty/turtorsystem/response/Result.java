package com.bkty.turtorsystem.response;

/**
 * 通用响应结果类
 * @param <T> 响应数据类型
 */
public class Result<T> {

    private Integer code; // 状态码：0-成功，1-失败
    private String msg;   // 消息
    private T data;       // 数据

    public Result() {
    }

    public Result(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Result(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 成功响应
     * @return Result对象
     */
    public static <T> Result<T> success() {
        return new Result<>(0, "操作成功");
    }

    /**
     * 成功响应（带数据）
     * @param data 响应数据
     * @return Result对象
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(0, "操作成功", data);
    }

    /**
     * 成功响应（带消息）
     * @param msg 响应消息
     * @return Result对象
     */
    public static <T> Result<T> success(String msg) {
        return new Result<>(0, msg);
    }

    /**
     * 失败响应
     * @return Result对象
     */
    public static <T> Result<T> error() {
        return new Result<>(1, "操作失败");
    }

    /**
     * 失败响应（带消息）
     * @param msg 错误消息
     * @return Result对象
     */
    public static <T> Result<T> error(String msg) {
        return new Result<>(1, msg);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
} 