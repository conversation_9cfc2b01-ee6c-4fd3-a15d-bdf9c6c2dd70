package com.bkty.turtorsystem.response;

/**
 * 文件上传响应类
 */
public class UploadResponse {

    private Integer code; // 状态码：0-成功，1-失败
    private String msg;   // 消息
    private String url;   // 文件URL路径
    private String path;  // 文件物理路径

    public UploadResponse() {
    }

    public UploadResponse(Integer code, String msg, String url, String path) {
        this.code = code;
        this.msg = msg;
        this.url = url;
        this.path = path;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
} 