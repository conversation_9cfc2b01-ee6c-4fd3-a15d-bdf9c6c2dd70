package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Feedback;
import com.bkty.turtorsystem.response.Response;
import com.bkty.turtorsystem.service.IFeedbackService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/feedback")
@CrossOrigin
public class FeedbackController {

    @Autowired
    private IFeedbackService service;

    /**
     * 前台用户提交留言反馈
     * @param feedback 留言信息
     * @return Response
     */
    @PostMapping("/add")
    public Response addFeedback(@RequestBody Feedback feedback) {
        try {
            // 参数校验
            if (feedback == null) {
                return Response.error(205, "反馈信息不能为空");
            }
            if (feedback.getAccount() == null || feedback.getAccount().isEmpty()) {
                return Response.error(205, "用户账号不能为空");
            }
            if (feedback.getUflag() == null || feedback.getUflag().isEmpty()) {
                return Response.error(205, "用户身份不能为空");
            }
            if (feedback.getTitle() == null || feedback.getTitle().isEmpty()) {
                return Response.error(205, "反馈主题不能为空");
            }
            if (feedback.getContent() == null || feedback.getContent().isEmpty()) {
                return Response.error(205, "反馈内容不能为空");
            }

            // 设置默认值
            feedback.setAddtime(LocalDateTime.now().toString());
            feedback.setAdminreply(""); // 管理员回复默认为空

            // 调用service层方法，添加反馈信息
            service.addFeedback(feedback);
            
            // 返回响应数据
            return Response.success("提交反馈成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error(500, "提交反馈失败");
        }
    }

    /**
     * 后台管理 - 查询留言反馈列表（分页）
     * @param feedback 查询条件
     * @param currentPage 当前页码
     * @param pageSize 每页记录数
     * @return Response
     */
    @PostMapping("/list")
    public Response queryFeedbackList(@RequestBody Feedback feedback,
                                     @RequestParam("currentPage") Integer currentPage,
                                     @RequestParam("pageSize") Integer pageSize) {
        try {
            // 调用service层方法，查询留言反馈列表
            PageInfo<Feedback> pageInfo = service.queryFeedbackList(feedback, currentPage, pageSize);
            // 获取列表数据
            List<Feedback> list = pageInfo.getList();
            // 获取总记录数
            int total = (int)pageInfo.getTotal(); // 将long转为int
            // 返回响应数据
            return Response.success(list, total, pageInfo.getPages());
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error(500, "查询留言反馈列表失败");
        }
    }

    /**
     * 获取单个留言反馈详情
     * @param id 留言ID
     * @return 单个留言详情
     */
    @RequestMapping(value = "/get")
    public Response getFeedback(@RequestParam("id") Long id) {
        try {
            if (id == null || id <= 0L) { // 使用0L表示long类型的0
                return Response.error(205, "留言ID不能为空");
            }
            
            // 调用service获取留言详情
            Feedback feedback = service.getFeedbackById(id);
            
            if (feedback == null) {
                return Response.error(404, "留言不存在");
            }
            
            return Response.success(feedback);
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error(500, "获取留言详情失败");
        }
    }

    /**
     * 后台管理 - 回复留言
     * @param feedback 包含id和adminreply的Feedback对象
     * @return Response 返回操作结果
     */
    @PostMapping("/reply")
    public Response replyFeedback(@RequestBody Feedback feedback) {
        try {
            // 参数校验
            if (feedback == null || feedback.getId() == 0) {
                return Response.error(205, "反馈ID不能为空");
            }
            if (feedback.getAdminreply() == null || feedback.getAdminreply().isEmpty()) {
                return Response.error(205, "回复内容不能为空");
            }
            
            // 调用service层方法，更新反馈回复
            int result = service.updateFeedbackReply(feedback.getId(), feedback.getAdminreply());
            
            // 返回响应数据
            if (result > 0) {
                return Response.success("回复成功");
            } else {
                return Response.error(500, "回复失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error(500, "回复失败");
        }
    }
    
    /**
     * 删除留言反馈
     * @param id 留言ID
     * @return 操作结果
     */
    @PostMapping("/del")
    public Response deleteFeedback(@RequestParam("id") Long id) {
        try {
            if (id == null || id <= 0L) {
                return Response.error(205, "留言ID不能为空");
            }
            
            // 调用service层删除留言
            int result = service.deleteFeedbackById(id);
            
            if (result > 0) {
                return Response.success("删除成功");
            } else {
                return Response.error(500, "删除失败，可能记录不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error(500, "删除失败");
        }
    }
} 