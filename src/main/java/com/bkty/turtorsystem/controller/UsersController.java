package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Users;
import com.bkty.turtorsystem.mapper.UsersMapper;
import com.bkty.turtorsystem.response.Result;
import com.bkty.turtorsystem.response.Response;
import com.bkty.turtorsystem.service.UsersService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = {"http://localhost:8088", "http://127.0.0.1:8088"}, allowCredentials = "true")
public class UsersController {
    // 添加日志记录器
    private static final Logger logger = LoggerFactory.getLogger(UsersController.class);

    @Autowired
    private UsersService usersService;

    @Autowired
    private UsersMapper usersMapper;

    /**
     * 用户注册 - 服务层实现
     * @param user 用户信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<Void> register(@RequestBody Users user) {
        logger.info("收到用户注册请求(/register): {}", user.getAccount());
        return usersService.register(user);
    }

    /**
     * 添加用户（家长注册）- 直接实现
     * @param user 用户信息
     * @return 注册结果
     */
    @PostMapping("/add")
    public Map<String, Object> add(@RequestBody Users user) {
        logger.info("收到用户添加请求(/add)，账号: {}, 姓名: {}", user.getAccount(), user.getUname());
        logger.info("用户信息详情: {}", user);

        Map<String, Object> result = new HashMap<>();

        // 检查账号是否已存在
        Users existUser = usersMapper.findByAccount(user.getAccount());
        if (existUser != null) {
            logger.warn("用户注册失败: 账号已存在 {}", user.getAccount());
            result.put("code", 201);
            result.put("msg", "账号已存在，请更换账号");
            return result;
        }

        // 设置注册时间
        user.setRegtime(new Timestamp(new Date().getTime()));

        try {
            // 插入用户
            logger.info("准备插入用户数据: {}", user.getAccount());
            int rows = usersMapper.insert(user);
            if (rows > 0) {
                logger.info("用户注册成功: {}", user.getAccount());
                result.put("code", 200);
                result.put("msg", "注册成功，请等待管理员审核");
            } else {
                logger.error("用户注册失败: 数据库插入失败，影响行数为0");
                result.put("code", 500);
                result.put("msg", "注册失败，请稍后再试");
            }
        } catch (Exception e) {
            logger.error("用户注册异常: ", e);
            result.put("code", 500);
            result.put("msg", "注册失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 用户(家长)登录
     * @param user 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public Response login(@RequestBody Users user) {
        logger.info("收到用户登录请求: {}", user.getAccount());

        try {
            if (user == null) {
                logger.warn("用户登录失败: 用户信息为空");
                return Response.error(201, "用户信息不能为空");
            }
            if (user.getAccount() == null || user.getAccount().equals("")) {
                logger.warn("用户登录失败: 账号为空");
                return Response.error(202, "用户账号不能为空");
            }
            if (user.getPassword() == null || user.getPassword().equals("")) {
                logger.warn("用户登录失败: 密码为空, 账号: {}", user.getAccount());
                return Response.error(203, "用户密码不能为空");
            }

            // 根据账号查询用户
            Users queryUser = usersMapper.findByAccount(user.getAccount());
            if (queryUser == null) {
                // 账号不存在
                logger.warn("用户登录失败: 账号不存在 {}", user.getAccount());
                return Response.error(204, "用户账号不存在");
            } else {
                if (queryUser.getPassword().equals(user.getPassword())) {
                    // 密码正确
                    logger.info("用户登录成功: {}", user.getAccount());
                    return Response.success(queryUser);
                } else {
                    logger.warn("用户登录失败: 密码错误, 账号: {}", user.getAccount());
                    return Response.error(205, "用户密码错误");
                }
            }
        } catch (Exception e) {
            logger.error("用户登录异常: ", e);
            return Response.error(500, "系统错误: " + e.getMessage());
        }
    }

    /**
     * 用户(家长)列表查询
     * @param currentPage 当前页
     * @param pageSize 每页大小
     * @param user 查询条件
     * @return 用户列表
     */
    @PostMapping("/list")
    public Map<String, Object> list(@RequestParam(defaultValue = "1") int currentPage,
                             @RequestParam(defaultValue = "10") int pageSize,
                             @RequestBody(required = false) Users user) {
        logger.info("收到用户列表查询请求(/list), 页码:{}, 每页大小:{}", currentPage, pageSize);
        if (user != null) {
            logger.info("查询条件: 账号:{}, 姓名:{}, 手机:{}, 状态:{}",
                    user.getAccount(), user.getUname(), user.getPhone(), user.getUflag());

            // 处理前端传入的审核不通过条件查询
            if (user.getCondition() != null && user.getCondition().contains("审核不通过")) {
                logger.info("检测到审核不通过条件查询: {}", user.getCondition());
                // 条件已经在mapper中处理，这里只记录日志
            }
        }

        Map<String, Object> result = new HashMap<>();

        try {
            // 计算偏移量
            int offset = (currentPage - 1) * pageSize;

            // 查询数据
            List<Users> users = usersMapper.findByCondition(offset, pageSize, user);

            // 获取符合条件的总记录数
            int total = usersMapper.countByCondition(user);

            result.put("code", 200);
            result.put("msg", "获取成功");
            result.put("count", total);
            result.put("resdata", users);
        } catch (Exception e) {
            logger.error("查询用户列表异常:", e);
            result.put("code", 500);
            result.put("msg", "查询失败: " + e.getMessage());
            result.put("count", 0);
            result.put("resdata", new ArrayList<>());
        }

        return result;
    }

    /**
     * 获取单个用户
     */
    @RequestMapping(value = "/get", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> get(@RequestParam String id) {
        logger.info("收到获取单个用户请求(/get), id:{}", id);

        Map<String, Object> result = new HashMap<>();

        try {
            Users user = usersMapper.findByAccountId(id);

            if (user != null) {
                result.put("code", 200);
                result.put("msg", "获取成功");
                result.put("resdata", user);
            } else {
                result.put("code", 404);
                result.put("msg", "用户不存在");
            }
        } catch (Exception e) {
            logger.error("查询单个用户异常:", e);
            result.put("code", 500);
            result.put("msg", "查询失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 更新用户
     */
    @PostMapping("/update")
    public Map<String, Object> update(@RequestBody Users user, @RequestParam(required = false) String account) {
        // 如果URL中有account参数，优先使用它
        if (account != null && !account.isEmpty()) {
            logger.info("收到更新用户请求(/update)，URL参数account={}, 请求数据:{}", account, user);
            user.setAccount(account);
        } else {
            logger.info("收到更新用户请求(/update)，RequestBody中account={}, 请求数据:{}", user.getAccount(), user);
        }

        // 记录审核状态操作
        if (user.getUflag() != null && (user.getUflag().contains("审核通过") || user.getUflag().contains("审核不通过"))) {
            logger.info("审核操作 - 用户账号:{}，状态变更为:{}", user.getAccount(), user.getUflag());
        }

        Map<String, Object> result = new HashMap<>();

        if (user.getAccount() == null || user.getAccount().isEmpty()) {
            logger.error("更新用户失败: 账号为空");
            result.put("code", 400);
            result.put("msg", "更新失败: 账号不能为空");
            return result;
        }

        try {
            // 先查询用户是否存在
            Users existUser = usersMapper.findByAccountId(user.getAccount());
            if (existUser == null) {
                logger.error("更新用户失败: 用户不存在, 账号:{}", user.getAccount());
                result.put("code", 404);
                result.put("msg", "更新失败: 用户不存在");
                return result;
            }

            // 保持一些必要字段不变
            if (user.getUflag() == null || user.getUflag().isEmpty()) {
                user.setUflag(existUser.getUflag());
            }

            // 执行更新
            logger.info("执行更新用户: {}", user.getAccount());
            int rows = usersMapper.update(user);

            if (rows > 0) {
                logger.info("更新用户成功: {}", user.getAccount());

                // 获取更新后的数据
                Users updatedUser = usersMapper.findByAccountId(user.getAccount());

                // 确保返回格式与前端期望一致
                result.put("code", 200);
                result.put("msg", "操作成功！");
                result.put("resdata", updatedUser);
                // 模拟会话更新
                result.put("session", true);
            } else {
                logger.error("更新用户失败: 数据库操作影响行数为0, 账号:{}", user.getAccount());
                result.put("code", 500);
                result.put("msg", "更新失败: 数据库操作未生效");
            }
        } catch (Exception e) {
            logger.error("更新用户异常:", e);
            result.put("code", 500);
            result.put("msg", "更新失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 删除用户
     */
    @PostMapping("/del")
    public Map<String, Object> delete(@RequestParam String id) {
        logger.info("收到删除用户请求(/del), id:{}", id);

        Map<String, Object> result = new HashMap<>();

        try {
            int rows = usersMapper.delete(id);

            if (rows > 0) {
                result.put("code", 200);
                result.put("msg", "删除成功");
            } else {
                result.put("code", 500);
                result.put("msg", "删除失败");
            }
        } catch (Exception e) {
            logger.error("删除用户异常:", e);
            result.put("code", 500);
            result.put("msg", "删除失败: " + e.getMessage());
        }

        return result;
    }
}