package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Tutors;
import com.bkty.turtorsystem.response.Result;
import com.bkty.turtorsystem.response.Response;
import com.bkty.turtorsystem.service.TutorsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 家教控制器
 */
@RestController
@RequestMapping("/api/tutors")
@CrossOrigin(origins = {"http://localhost:8088", "http://127.0.0.1:8088"}, allowCredentials = "true")
public class TutorsController {
    // 添加日志记录器
    private static final Logger logger = LoggerFactory.getLogger(TutorsController.class);

    @Autowired
    private TutorsService tutorsService;

    /**
     * 家教注册
     * @param tutor 家教信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<Void> register(@RequestBody Tutors tutor) {
        logger.info("收到家教注册请求(/register): {}", tutor.getTaccount());
        return tutorsService.register(tutor);
    }

    /**
     * 添加家教（家教注册）- 兼容旧API
     * @param tutor 家教信息
     * @return 注册结果
     */
    @PostMapping("/add")
    public Map<String, Object> add(@RequestBody Tutors tutor) {
        logger.info("收到家教添加请求(/add)，账号: {}, 姓名: {}", tutor.getTaccount(), tutor.getTuname());
        Result<Void> result = tutorsService.register(tutor);

        // 转换Result为Map，保持API兼容
        Map<String, Object> response = new HashMap<>();
        if (result.getCode() == 0) { // 0表示成功
            response.put("code", 200);
            response.put("msg", result.getMsg());
            } else {
            response.put("code", 400); // 假设错误码为400
            response.put("msg", result.getMsg());
        }

        return response;
    }

    /**
     * 家教登录
     * @param tutor 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public Response login(@RequestBody Tutors tutor) {
        logger.info("收到家教登录请求: {}", tutor.getTaccount());

        try {
            if (tutor == null) {
                logger.warn("家教登录失败: 家教信息为空");
                return Response.error(201, "家教信息不能为空");
            }
            if (tutor.getTaccount() == null || tutor.getTaccount().equals("")) {
                logger.warn("家教登录失败: 账号为空");
                return Response.error(202, "家教账号不能为空");
            }
            if (tutor.getPassword() == null || tutor.getPassword().equals("")) {
                logger.warn("家教登录失败: 密码为空，账号: {}", tutor.getTaccount());
                return Response.error(203, "家教密码不能为空");
            }

            // 这部分暂时保留，后续可优化到Service层
            // 根据账号查询家教
            Map<String, Object> tutorResult = tutorsService.getTutorById(tutor.getTaccount());
            if (tutorResult.get("code").equals(200)) {
                Tutors queryTutor = (Tutors) tutorResult.get("resdata");
                if (queryTutor.getPassword().equals(tutor.getPassword())) {
                    // 密码正确
                    logger.info("家教登录成功: {}", tutor.getTaccount());
                    return Response.success(queryTutor);
                } else {
                    logger.warn("家教登录失败: 密码错误，账号: {}", tutor.getTaccount());
                    return Response.error(205, "家教密码错误");
                }
            } else {
                // 账号不存在
                logger.warn("家教登录失败: 账号不存在 {}", tutor.getTaccount());
                return Response.error(204, "家教账号不存在");
            }
        } catch (Exception e) {
            logger.error("家教登录异常: ", e);
            return Response.error(500, "系统错误: " + e.getMessage());
        }
    }

    /**
     * 家教列表查询
     * @param currentPage 当前页
     * @param pageSize 每页大小
     * @param tutor 查询条件
     * @param listType 列表类型
     * @return 家教列表
     */
    @PostMapping("/list")
    public Map<String, Object> list(@RequestParam(defaultValue = "1") int currentPage,
                             @RequestParam(defaultValue = "10") int pageSize,
                             @RequestBody(required = false) Tutors tutor,
                             @RequestParam(required = false) String listType) {
        logger.info("收到家教列表查询请求(/list), 页码:{}, 每页大小:{}, 列表类型:{}", currentPage, pageSize, listType);
        return tutorsService.listTutors(currentPage, pageSize, tutor, listType);
    }

    /**
     * 获取单个家教
     * @param id 家教账号
     * @return 家教信息
     */
    @RequestMapping(value = "/get", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> get(@RequestParam String id) {
        logger.info("收到获取单个家教请求(/get), id:{}", id);
        return tutorsService.getTutorById(id);
    }

    /**
     * 更新家教
     * @param tutor 家教信息
     * @param account URL参数账号（可选）
     * @return 更新结果
     */
    @PostMapping("/update")
    public Map<String, Object> update(@RequestBody Tutors tutor, @RequestParam(required = false) String account) {
        // 如果URL中有account参数，优先使用它
        if (account != null && !account.isEmpty()) {
            logger.info("收到更新家教请求(/update)，URL参数account={}, 请求数据(部分):{}", account, tutor.getTflag());
            tutor.setTaccount(account);
        } else {
            logger.info("收到更新家教请求(/update)，RequestBody中taccount={}", tutor.getTaccount());
        }

        // 记录审核状态操作
        if (tutor.getTflag() != null && (tutor.getTflag().contains("审核通过") || tutor.getTflag().contains("审核不通过"))) {
            logger.info("审核操作 - 家教账号:{}，状态变更为:{}", tutor.getTaccount(), tutor.getTflag());
        }

        return tutorsService.updateTutor(tutor);
    }

    /**
     * 删除家教
     * @param id 家教账号
     * @return 删除结果
     */
    @PostMapping("/del")
    public Map<String, Object> delete(@RequestParam String id) {
        logger.info("收到删除家教请求(/del), id:{}", id);
        return tutorsService.deleteTutor(id);
    }
}