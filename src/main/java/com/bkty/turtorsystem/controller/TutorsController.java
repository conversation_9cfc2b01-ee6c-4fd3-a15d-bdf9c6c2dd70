package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Tutors;
import com.bkty.turtorsystem.mapper.TutorsMapper;
import com.bkty.turtorsystem.response.Result;
import com.bkty.turtorsystem.response.Response;
import com.bkty.turtorsystem.service.TutorsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 家教控制器
 */
@RestController
@RequestMapping("/api/tutors")
@CrossOrigin(origins = {"http://localhost:8088", "http://127.0.0.1:8088"}, allowCredentials = "true")
public class TutorsController {
    // 添加日志记录器
    private static final Logger logger = LoggerFactory.getLogger(TutorsController.class);

    @Autowired
    private TutorsService tutorsService;
    
    @Autowired
    private TutorsMapper tutorsMapper;

    /**
     * 家教注册 - 服务层实现
     * @param tutor 家教信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<Void> register(@RequestBody Tutors tutor) {
        logger.info("收到家教注册请求(/register): {}", tutor.getTaccount());
        return tutorsService.register(tutor);
    }
    
    /**
     * 添加家教（家教注册）- 直接实现
     * @param tutor 家教信息
     * @return 注册结果
     */
    @PostMapping("/add")
    public Map<String, Object> add(@RequestBody Tutors tutor) {
        logger.info("收到家教添加请求(/add)，账号: {}, 姓名: {}", tutor.getTaccount(), tutor.getTuname());
        logger.info("家教信息详情: {}", tutor);
        
        Map<String, Object> result = new HashMap<>();
        
        // 检查账号是否已存在
        Tutors existTutor = tutorsMapper.findByAccount(tutor.getTaccount());
        if (existTutor != null) {
            logger.warn("家教注册失败: 账号已存在 {}", tutor.getTaccount());
            result.put("code", 201);
            result.put("msg", "账号已存在，请更换账号");
            return result;
        }
        
        // 设置注册时间
        tutor.setRegistrationdate(new Timestamp(new Date().getTime()));
        
        try {
            // 插入家教信息
            logger.info("准备插入家教数据: {}", tutor.getTaccount());
            int rows = tutorsMapper.insert(tutor);
            if (rows > 0) {
                logger.info("家教注册成功: {}", tutor.getTaccount());
                result.put("code", 200);
                result.put("msg", "注册成功，请等待管理员审核");
            } else {
                logger.error("家教注册失败: 数据库插入失败，影响行数为0");
                result.put("code", 500);
                result.put("msg", "注册失败，请稍后再试");
            }
        } catch (Exception e) {
            logger.error("家教注册异常: ", e);
            result.put("code", 500);
            result.put("msg", "注册失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 家教登录
     * @param tutor 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public Response login(@RequestBody Tutors tutor) {
        logger.info("收到家教登录请求: {}", tutor.getTaccount());
        
        try {
            if (tutor == null) {
                logger.warn("家教登录失败: 家教信息为空");
                return Response.error(201, "家教信息不能为空");
            }
            if (tutor.getTaccount() == null || tutor.getTaccount().equals("")) {
                logger.warn("家教登录失败: 账号为空");
                return Response.error(202, "家教账号不能为空");
            }
            if (tutor.getPassword() == null || tutor.getPassword().equals("")) {
                logger.warn("家教登录失败: 密码为空，账号: {}", tutor.getTaccount());
                return Response.error(203, "家教密码不能为空");
            }
            
            // 根据账号查询家教
            Tutors queryTutor = tutorsMapper.findByAccount(tutor.getTaccount());
            if (queryTutor == null) {
                // 账号不存在
                logger.warn("家教登录失败: 账号不存在 {}", tutor.getTaccount());
                return Response.error(204, "家教账号不存在");
            } else {
                if (queryTutor.getPassword().equals(tutor.getPassword())) {
                    // 密码正确
                    logger.info("家教登录成功: {}", tutor.getTaccount());
                    return Response.success(queryTutor);
                } else {
                    logger.warn("家教登录失败: 密码错误，账号: {}", tutor.getTaccount());
                    return Response.error(205, "家教密码错误");
                }
            }
        } catch (Exception e) {
            logger.error("家教登录异常: ", e);
            return Response.error(500, "系统错误: " + e.getMessage());
        }
    }

    /**
     * 家教列表查询
     * @param currentPage 当前页
     * @param pageSize 每页大小
     * @param tutor 查询条件
     * @return 家教列表
     */
    @PostMapping("/list")
    public Map<String, Object> list(@RequestParam(defaultValue = "1") int currentPage,
                             @RequestParam(defaultValue = "10") int pageSize,
                             @RequestBody(required = false) Tutors tutor) {
        logger.info("收到家教列表查询请求(/list), 页码:{}, 每页大小:{}", currentPage, pageSize);
        if (tutor != null) {
            logger.info("查询条件: 账号:{}, 姓名:{}, 手机:{}, 状态:{}, 类型:{}", 
                    tutor.getTaccount(), tutor.getTuname(), tutor.getPhone(), tutor.getTflag(), tutor.getCatid());
                
            // 处理前端传入的审核不通过条件查询
            if (tutor.getCondition() != null && tutor.getCondition().contains("审核不通过")) {
                logger.info("检测到审核不通过条件查询: {}", tutor.getCondition());
                // 条件已经在mapper中处理，这里只记录日志
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 计算偏移量
            int offset = (currentPage - 1) * pageSize;
            
            // 查询数据
            List<Tutors> tutors = tutorsMapper.findByCondition(offset, pageSize, tutor);
            
            // 获取符合条件的总记录数
            int total = tutorsMapper.countByCondition(tutor);
            
            result.put("code", 200);
            result.put("msg", "获取成功");
            result.put("count", total);
            result.put("resdata", tutors);
        } catch (Exception e) {
            logger.error("查询家教列表异常:", e);
            result.put("code", 500);
            result.put("msg", "查询失败: " + e.getMessage());
            result.put("count", 0);
            result.put("resdata", new ArrayList<>());
        }
        
        return result;
    }
    
    /**
     * 获取单个家教
     */
    @PostMapping("/get")
    public Map<String, Object> get(@RequestParam String id) {
        logger.info("收到获取单个家教请求(/get), id:{}", id);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Tutors tutor = tutorsMapper.findById(id);
            
            if (tutor != null) {
                result.put("code", 200);
                result.put("msg", "获取成功");
                result.put("resdata", tutor);
            } else {
                result.put("code", 404);
                result.put("msg", "家教不存在");
            }
        } catch (Exception e) {
            logger.error("查询单个家教异常:", e);
            result.put("code", 500);
            result.put("msg", "查询失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 更新家教
     */
    @PostMapping("/update")
    public Map<String, Object> update(@RequestBody Tutors tutor, @RequestParam(required = false) String account) {
        // 如果URL中有account参数，优先使用它
        if (account != null && !account.isEmpty()) {
            logger.info("收到更新家教请求(/update)，URL参数account={}, 请求数据:{}", account, tutor);
            tutor.setTaccount(account);
        } else {
            logger.info("收到更新家教请求(/update)，RequestBody中taccount={}, 请求数据:{}", tutor.getTaccount(), tutor);
        }
        
        // 记录审核状态操作
        if (tutor.getTflag() != null && (tutor.getTflag().contains("审核通过") || tutor.getTflag().contains("审核不通过"))) {
            logger.info("审核操作 - 家教账号:{}，状态变更为:{}", tutor.getTaccount(), tutor.getTflag());
        }
        
        Map<String, Object> result = new HashMap<>();
        
        if (tutor.getTaccount() == null || tutor.getTaccount().isEmpty()) {
            logger.error("更新家教失败: 账号为空");
            result.put("code", 400);
            result.put("msg", "更新失败: 账号不能为空");
            return result;
        }
        
        try {
            // 先查询家教是否存在
            Tutors existTutor = tutorsMapper.findById(tutor.getTaccount());
            if (existTutor == null) {
                logger.error("更新家教失败: 家教不存在, 账号:{}", tutor.getTaccount());
                result.put("code", 404);
                result.put("msg", "更新失败: 家教不存在");
                return result;
            }
            
            // 保持一些必要字段不变
            if (tutor.getTflag() == null || tutor.getTflag().isEmpty()) {
                tutor.setTflag(existTutor.getTflag());
            }
            
            // 执行更新
            logger.info("执行更新家教: {}", tutor.getTaccount());
            int rows = tutorsMapper.update(tutor);
            
            if (rows > 0) {
                logger.info("更新家教成功: {}", tutor.getTaccount());
                
                // 获取更新后的数据
                Tutors updatedTutor = tutorsMapper.findById(tutor.getTaccount());
                
                // 确保返回格式与前端期望一致
                result.put("code", 200);
                result.put("msg", "操作成功！");
                result.put("resdata", updatedTutor);
                // 模拟会话更新
                result.put("session", true);
            } else {
                logger.error("更新家教失败: 数据库操作影响行数为0, 账号:{}", tutor.getTaccount());
                result.put("code", 500);
                result.put("msg", "更新失败: 数据库操作未生效");
            }
        } catch (Exception e) {
            logger.error("更新家教异常:", e);
            result.put("code", 500);
            result.put("msg", "更新失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 删除家教
     */
    @PostMapping("/del")
    public Map<String, Object> delete(@RequestParam String id) {
        logger.info("收到删除家教请求(/del), id:{}", id);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            int rows = tutorsMapper.delete(id);
            
            if (rows > 0) {
                result.put("code", 200);
                result.put("msg", "删除成功");
            } else {
                result.put("code", 500);
                result.put("msg", "删除失败");
            }
        } catch (Exception e) {
            logger.error("删除家教异常:", e);
            result.put("code", 500);
            result.put("msg", "删除失败: " + e.getMessage());
        }
        
        return result;
    }
} 