package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Hometutorinfo;
import com.bkty.turtorsystem.service.HometutorinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 家教资讯控制器
 */
@RestController
@RequestMapping("/api/hometutorinfo")
@CrossOrigin(origins = {"http://localhost:8088", "http://127.0.0.1:8088"}, allowCredentials = "true")
public class HometutorinfoController {
    // 添加日志记录器
    private static final Logger logger = LoggerFactory.getLogger(HometutorinfoController.class);

    @Autowired
    private HometutorinfoService hometutorinfoService;

    /**
     * 获取家教资讯列表
     * @param currentPage 当前页
     * @param pageSize 每页大小
     * @param hometutorinfo 查询条件
     * @return 家教资讯列表
     */
    @PostMapping("/list")
    public Map<String, Object> list(@RequestParam(defaultValue = "1") int currentPage,
                             @RequestParam(defaultValue = "10") int pageSize,
                             @RequestBody(required = false) Hometutorinfo hometutorinfo) {
        logger.info("收到家教资讯列表查询请求(/list), 页码:{}, 每页大小:{}", currentPage, pageSize);
        if (hometutorinfo != null && hometutorinfo.getTitle() != null) {
            logger.info("查询条件: 标题:{}", hometutorinfo.getTitle());
        }

        return hometutorinfoService.listHometutorinfo(currentPage, pageSize, hometutorinfo);
    }

    /**
     * 获取单个家教资讯
     */
    @PostMapping("/get")
    public Map<String, Object> get(@RequestParam Long id) {
        logger.info("收到获取单个家教资讯请求(/get), id:{}", id);

        return hometutorinfoService.getHometutorinfoById(id);
    }

    /**
     * 添加家教资讯
     */
    @PostMapping("/add")
    public Map<String, Object> add(@RequestBody Hometutorinfo hometutorinfo) {
        logger.info("收到添加家教资讯请求(/add), 标题:{}", hometutorinfo.getTitle());

        return hometutorinfoService.addHometutorinfo(hometutorinfo);
    }

    /**
     * 更新家教资讯
     */
    @PostMapping("/update")
    public Map<String, Object> update(@RequestBody Hometutorinfo hometutorinfo) {
        logger.info("收到更新家教资讯请求(/update), id:{}", hometutorinfo.getId());

        return hometutorinfoService.updateHometutorinfo(hometutorinfo);
    }

    /**
     * 删除家教资讯
     */
    @PostMapping("/del")
    public Map<String, Object> delete(@RequestParam Long id) {
        logger.info("收到删除家教资讯请求(/del), id:{}", id);

        return hometutorinfoService.deleteHometutorinfo(id);
    }
}