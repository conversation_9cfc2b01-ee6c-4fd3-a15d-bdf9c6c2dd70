package com.bkty.turtorsystem.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 */
@RestController
@CrossOrigin(origins = {"http://localhost:8088", "http://127.0.0.1:8088"}, allowCredentials = "true")
public class FileController {
    // 添加日志记录器
    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    @Value("${file.upload.path}")
    private String uploadPath;

    /**
     * 文件上传处理 - 公共方法
     * @param file 上传的文件
     * @return 上传结果
     */
    private Map<String, Object> handleFileUpload(MultipartFile file, String apiPath) {
        logger.info("处理文件上传请求 [{}]，文件名: {}, 大小: {} bytes", 
                    apiPath, file.getOriginalFilename(), file.getSize());
                    
        Map<String, Object> resultMap = new HashMap<>();
        
        // 文件为空时返回失败信息
        if (file.isEmpty()) {
            logger.warn("上传文件为空");
            resultMap.put("code", 1);
            resultMap.put("msg", "文件为空");
            return resultMap;
        }

        try {
            // 获取文件名及后缀
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                logger.warn("无法获取原始文件名");
                resultMap.put("code", 1);
                resultMap.put("msg", "无法获取文件名");
                return resultMap;
            }
            
            String suffix = originalFilename.contains(".") ? 
                originalFilename.substring(originalFilename.lastIndexOf(".")) : "";
            
            // 生成新文件名（使用时间戳确保唯一性）
            String dateDir = new SimpleDateFormat("yyyyMMdd").format(new Date());
            String timestamp = new SimpleDateFormat("HHmmss").format(new Date());
            String newFileName = dateDir + timestamp + suffix;
            
            // 确保上传目录存在
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                logger.info("创建上传根目录: {}", uploadDir.getAbsolutePath());
                if (!uploadDir.mkdirs()) {
                    logger.error("创建上传根目录失败: {}", uploadDir.getAbsolutePath());
                    resultMap.put("code", 1);
                    resultMap.put("msg", "服务器存储目录创建失败");
                    return resultMap;
                }
            }
            
            // 完整文件路径
            String filePath = uploadPath + File.separator + newFileName;
            File dest = new File(filePath);
            
            logger.info("准备保存文件到: {}", filePath);
            
            // 保存文件
            file.transferTo(dest);
            
            logger.info("文件保存成功，文件名: {}", newFileName);
            
            // 返回成功结果
            resultMap.put("code", 0);
            resultMap.put("msg", "上传成功");
            
            // 创建resdata对象，以匹配前端期望的结构
            Map<String, Object> resdata = new HashMap<>();
            resdata.put("url", newFileName);
            resdata.put("filePath", newFileName);
            resultMap.put("resdata", resdata);
            
            // 同时保留url字段，为各种前端代码提供支持
            resultMap.put("url", newFileName);
            
            return resultMap;
        } catch (IOException e) {
            logger.error("文件上传失败: ", e);
            resultMap.put("code", 1);
            resultMap.put("msg", "上传失败: " + e.getMessage());
            return resultMap;
        }
    }

    /**
     * 文件上传处理 - 所有路径统一处理
     * @param file 上传的文件
     * @return 上传结果
     */
    @PostMapping({"/api/common/uploadFile", "/common/uploadFile", "/upload", "/api/upload"})
    public Map<String, Object> uploadFile(@RequestParam("file") MultipartFile file) {
        return handleFileUpload(file, "/api/common/uploadFile");
    }
} 