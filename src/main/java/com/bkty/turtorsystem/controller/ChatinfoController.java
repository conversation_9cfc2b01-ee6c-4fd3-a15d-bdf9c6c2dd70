package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Chatinfo;
import com.bkty.turtorsystem.service.ChatinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 聊天信息控制器
 */
@RestController
@RequestMapping("/chatinfo")
@CrossOrigin(origins = {"http://localhost:8088", "http://127.0.0.1:8088"}, allowCredentials = "true")
public class ChatinfoController {

    private static final Logger logger = LoggerFactory.getLogger(ChatinfoController.class);

    @Autowired
    private ChatinfoService chatinfoService;

    /**
     * 获取聊天记录列表
     * @param currentPage 当前页
     * @param pageSize 每页大小
     * @param params 查询参数
     * @return 聊天记录列表
     */
    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> list(@RequestParam(defaultValue = "1") int currentPage,
                                   @RequestParam(defaultValue = "10") int pageSize,
                                   @RequestParam(required = false) String condition,
                                   @RequestBody(required = false) Map<String, Object> params) {
        logger.info("收到获取聊天记录列表请求(/list), 页码:{}, 每页大小:{}", currentPage, pageSize);

        // 优先使用URL参数中的condition
        if (condition == null && params != null && params.containsKey("condition")) {
            condition = (String) params.get("condition");
        }

        if (condition != null) {
            logger.info("查询条件: {}", condition);
        }

        return chatinfoService.listChatinfo(condition, currentPage, pageSize);
    }

    /**
     * 添加聊天记录
     * @param chatinfo 聊天信息
     * @return 添加结果
     */
    @RequestMapping(value = "/add", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> add(@RequestBody Chatinfo chatinfo) {
        logger.info("收到添加聊天记录请求(/add), 发送人:{}, 接收人:{}", chatinfo.getLname(), chatinfo.getLname2());

        Map<String, Object> result = new HashMap<>();

        // 参数校验
        if (chatinfo.getLname() == null || chatinfo.getLname().isEmpty()) {
            result.put("code", 201);
            result.put("msg", "发送人不能为空");
            return result;
        }

        if (chatinfo.getLname2() == null || chatinfo.getLname2().isEmpty()) {
            result.put("code", 202);
            result.put("msg", "接收人不能为空");
            return result;
        }

        if (chatinfo.getContent() == null || chatinfo.getContent().isEmpty()) {
            result.put("code", 203);
            result.put("msg", "消息内容不能为空");
            return result;
        }

        boolean success = chatinfoService.addChatinfo(chatinfo);

        if (success) {
            result.put("code", 200);
            result.put("msg", "发送成功");
            result.put("resdata", chatinfo);
        } else {
            result.put("code", 500);
            result.put("msg", "发送失败");
        }

        return result;
    }

    /**
     * 获取单个聊天记录
     * @param id 聊天记录ID
     * @return 聊天记录
     */
    @RequestMapping(value = "/get", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> get(@RequestParam Long id) {
        logger.info("收到获取单个聊天记录请求(/get), id:{}", id);

        Map<String, Object> result = new HashMap<>();

        Chatinfo chatinfo = chatinfoService.getChatinfoById(id);

        if (chatinfo != null) {
            result.put("code", 200);
            result.put("msg", "获取成功");
            result.put("resdata", chatinfo);
        } else {
            result.put("code", 404);
            result.put("msg", "聊天记录不存在");
        }

        return result;
    }

    /**
     * 删除聊天记录
     * @param id 聊天记录ID
     * @return 删除结果
     */
    @PostMapping("/del")
    public Map<String, Object> delete(@RequestParam Long id) {
        logger.info("收到删除聊天记录请求(/del), id:{}", id);

        Map<String, Object> result = new HashMap<>();

        boolean success = chatinfoService.deleteChatinfo(id);

        if (success) {
            result.put("code", 200);
            result.put("msg", "删除成功");
        } else {
            result.put("code", 500);
            result.put("msg", "删除失败");
        }

        return result;
    }

    /**
     * 获取用户的聊天联系人列表
     * @param account 用户账号
     * @return 联系人列表
     */
    @RequestMapping(value = "/contacts", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> getContacts(@RequestParam String account) {
        logger.info("收到获取用户聊天联系人列表请求(/contacts), 账号:{}", account);

        Map<String, Object> result = new HashMap<>();

        List<String> contacts = chatinfoService.getContactsByAccount(account);

        result.put("code", 200);
        result.put("msg", "获取成功");
        result.put("resdata", contacts);

        return result;
    }
}
