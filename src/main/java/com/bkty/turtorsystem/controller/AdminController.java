package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Admin;
import com.bkty.turtorsystem.response.Response;
import com.bkty.turtorsystem.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/admin")
@CrossOrigin(origins = {"http://localhost:8088", "http://127.0.0.1:8088"}, allowCredentials = "true")
public class AdminController {

    @Autowired
    private AdminService adminService;

    @PostMapping("/login")
    public Response login(@RequestBody Admin admin) {
        //登录业务逻辑
        try{//调用service层的方法
            if(admin == null){
                return Response.error(201,"管理员信息不能为空");
            }
            if(admin.getAname() == null || admin.getAname().equals("")){
                return Response.error(202,"管理员名称不能为空");
            }
            if(admin.getLoginpassword() == null || admin.getLoginpassword().equals("")){
                return Response.error(203,"管理员密码不能为空");
            }
            //调用service层的方法
            Admin queryAdmin = adminService.queryAdmin(admin);
            if(queryAdmin == null){//账号不存在
                return Response.error(204,"管理员账号不存在");
            }else {
                if(queryAdmin.getLoginpassword().equals(admin.getLoginpassword())){//密码正
                    return Response.success(queryAdmin);
                }else{
                    return Response.error(205,"管理员密码错误");
                }
            }
        }catch(Exception e){
            e.printStackTrace();
            return Response.error(500,"系统错误");
        }
    }

}
