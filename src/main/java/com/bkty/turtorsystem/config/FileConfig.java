package com.bkty.turtorsystem.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 文件上传配置类
 * 注意：此类已弃用，所有功能已移至WebMvcConfig和TurtorSystemApplication中
 * 保留此类是为了向后兼容性
 */
@Configuration  
public class FileConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(FileConfig.class);
    
    @Value("${file.upload.path}")
    private String uploadPath;
    
    @Value("${file.upload.url}")
    private String uploadUrl;
    
    // Bean initializeUploadDirectory 已移至 TurtorSystemApplication 类中
} 