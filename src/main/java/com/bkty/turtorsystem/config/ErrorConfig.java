package com.bkty.turtorsystem.config;

import org.springframework.boot.web.server.ErrorPage;
import org.springframework.boot.web.server.ErrorPageRegistrar;
import org.springframework.boot.web.server.ErrorPageRegistry;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 错误页面配置
 */
@Configuration
public class ErrorConfig implements ErrorPageRegistrar {
    
    private static final Logger logger = LoggerFactory.getLogger(ErrorConfig.class);

    @Override
    public void registerErrorPages(ErrorPageRegistry registry) {
        logger.info("注册错误页面配置");
        
        // 将404错误重定向到首页
        ErrorPage error404Page = new ErrorPage(HttpStatus.NOT_FOUND, "/dist/index.html");
        
        // 注册错误页面
        registry.addErrorPages(error404Page);
        
        logger.info("错误页面配置完成");
    }
} 