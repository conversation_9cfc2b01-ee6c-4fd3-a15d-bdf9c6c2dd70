package com.bkty.turtorsystem.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    
    private static final Logger logger = LoggerFactory.getLogger(WebMvcConfig.class);
    
    @Value("${file.upload.path}")
    private String uploadPath;

	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {
        logger.info("配置WebMvcConfig静态资源映射, 上传目录路径: {}", uploadPath);
        
        // 确保上传目录存在
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            boolean created = uploadDir.mkdirs();
            logger.info("上传目录创建结果: {}, 路径: {}", created, uploadPath);
        }
        
        // 静态资源映射配置，优先级从高到低
        
        // 1. 直接映射上传目录的内容到根路径，这样前端硬编码的URL可以正常工作
        registry.addResourceHandler("/**")
                .addResourceLocations("file:" + uploadPath + "/", "classpath:/static/", "classpath:/static/dist/");
        
        logger.info("资源映射配置完成，上传目录 {} 可通过根路径访问", uploadPath);
	}

	@Bean
	public WebMvcConfigurer corsConfigurer() {
		return new WebMvcConfigurer() {
			@Override
			public void addCorsMappings(CorsRegistry registry) {
				registry.addMapping("/**")
						.allowedOrigins("http://localhost:8088", "http://127.0.0.1:8088") // 明确指定允许的源
						.allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS") // 所有方法
						.allowCredentials(true)
						.maxAge(3600)
						.allowedHeaders("*");
			}
		};
	}
}
