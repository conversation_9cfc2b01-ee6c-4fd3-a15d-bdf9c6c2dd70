package com.bkty.turtorsystem.mapper;

import com.bkty.turtorsystem.entity.Users;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 用户数据访问接口
 */
@Mapper
public interface UsersMapper {

    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM users WHERE uid = #{id}")
    Users findById(@Param("id") Long id);
    
    /**
     * 根据账号查询用户
     */
    @Select("SELECT * FROM users WHERE account = #{id}")
    Users findByAccountId(@Param("id") String id);
    
    /**
     * 根据账号查询用户
     */
    @Select("SELECT * FROM users WHERE account = #{account}")
    Users findByAccount(@Param("account") String account);

    /**
     * 查询所有用户（分页）
     */
    @Select("SELECT * FROM users LIMIT #{offset}, #{limit}")
    List<Users> findAll(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 获取总数
     */
    @Select("SELECT COUNT(*) FROM users")
    int count();
    
    /**
     * 插入用户
     */
    @Insert("INSERT INTO users(account, password, uname, gender, age, phone, email, address, avatar, uflag, regtime) " +
            "VALUES(#{account}, #{password}, #{uname}, #{gender}, #{age}, #{phone}, #{email}, #{address}, #{avatar}, #{uflag}, #{regtime})")
    @Options(useGeneratedKeys = true, keyProperty = "uid")
    int insert(Users user);
    
    /**
     * 根据条件查询用户（分页）
     */
    @Select("<script>" +
            "SELECT * FROM users" +
            "<where>" +
            "  <if test='user != null'>" +
            "    <if test='user.account != null and user.account != \"\"'>" +
            "      AND account LIKE CONCAT('%', #{user.account}, '%')" +
            "    </if>" +
            "    <if test='user.uname != null and user.uname != \"\"'>" +
            "      AND uname LIKE CONCAT('%', #{user.uname}, '%')" +
            "    </if>" +
            "    <if test='user.phone != null and user.phone != \"\"'>" +
            "      AND phone LIKE CONCAT('%', #{user.phone}, '%')" +
            "    </if>" +
            "    <if test='user.uflag != null and user.uflag != \"\"'>" +
            "      <choose>" +
            "        <when test='user.uflag == \"审核通过\"'>" +
            "          AND uflag = '审核通过'" +
            "        </when>" +
            "        <when test='user.uflag == \"待审核\"'>" +
            "          AND uflag = '待审核'" +
            "        </when>" +
            "        <when test='user.uflag == \"审核不通过\"'>" +
            "          AND uflag LIKE '审核不通过%' AND uflag != '审核通过'" +
            "        </when>" +
            "        <otherwise>" +
            "          AND uflag = #{user.uflag}" +
            "        </otherwise>" +
            "      </choose>" +
            "    </if>" +
            "    <if test='user.condition != null and user.condition != \"\"'>" +
            "      ${user.condition}" +
            "    </if>" +
            "  </if>" +
            "</where>" +
            " LIMIT #{offset}, #{limit}" +
            "</script>")
    List<Users> findByCondition(@Param("offset") int offset, @Param("limit") int limit, @Param("user") Users user);
    
    /**
     * 根据条件统计用户数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM users" +
            "<where>" +
            "  <if test='user != null'>" +
            "    <if test='user.account != null and user.account != \"\"'>" +
            "      AND account LIKE CONCAT('%', #{user.account}, '%')" +
            "    </if>" +
            "    <if test='user.uname != null and user.uname != \"\"'>" +
            "      AND uname LIKE CONCAT('%', #{user.uname}, '%')" +
            "    </if>" +
            "    <if test='user.phone != null and user.phone != \"\"'>" +
            "      AND phone LIKE CONCAT('%', #{user.phone}, '%')" +
            "    </if>" +
            "    <if test='user.uflag != null and user.uflag != \"\"'>" +
            "      <choose>" +
            "        <when test='user.uflag == \"审核通过\"'>" +
            "          AND uflag = '审核通过'" +
            "        </when>" +
            "        <when test='user.uflag == \"待审核\"'>" +
            "          AND uflag = '待审核'" +
            "        </when>" +
            "        <when test='user.uflag == \"审核不通过\"'>" +
            "          AND uflag LIKE '审核不通过%' AND uflag != '审核通过'" +
            "        </when>" +
            "        <otherwise>" +
            "          AND uflag = #{user.uflag}" +
            "        </otherwise>" +
            "      </choose>" +
            "    </if>" +
            "    <if test='user.condition != null and user.condition != \"\"'>" +
            "      ${user.condition}" +
            "    </if>" +
            "  </if>" +
            "</where>" +
            "</script>")
    int countByCondition(@Param("user") Users user);
    
    /**
     * 更新用户
     */
    @Update("<script>" +
            "UPDATE users" +
            "<set>" +
            "  <if test='password != null and password != \"\"'>password = #{password},</if>" +
            "  <if test='uname != null and uname != \"\"'>uname = #{uname},</if>" +
            "  <if test='gender != null'>gender = #{gender},</if>" +
            "  <if test='age > 0'>age = #{age},</if>" +
            "  <if test='phone != null and phone != \"\"'>phone = #{phone},</if>" +
            "  <if test='email != null and email != \"\"'>email = #{email},</if>" +
            "  <if test='address != null and address != \"\"'>address = #{address},</if>" +
            "  <if test='avatar != null and avatar != \"\"'>avatar = #{avatar},</if>" +
            "  <if test='uflag != null and uflag != \"\"'>uflag = #{uflag},</if>" +
            "</set>" +
            "WHERE account = #{account}" +
            "</script>")
    int update(Users user);
    
    /**
     * 删除用户
     */
    @Delete("DELETE FROM users WHERE account = #{id}")
    int delete(@Param("id") String id);
} 