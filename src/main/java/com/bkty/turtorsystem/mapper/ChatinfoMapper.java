package com.bkty.turtorsystem.mapper;

import com.bkty.turtorsystem.entity.Chatinfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 聊天信息数据访问接口
 */
@Mapper
public interface ChatinfoMapper {

    /**
     * 根据条件查询聊天记录
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @param condition 查询条件
     * @return 聊天记录列表
     */
    @Select({
            "<script>",
            "SELECT * FROM chatinfo",
            "<where>",
            "  <if test='condition != null'>",
            "    ${condition}",
            "  </if>",
            "</where>",
            "ORDER BY sendtime DESC",
            "LIMIT #{offset}, #{pageSize}",
            "</script>"
    })
    List<Chatinfo> findByCondition(@Param("offset") int offset, @Param("pageSize") int pageSize, 
                                  @Param("condition") String condition);

    /**
     * 根据条件统计聊天记录数量
     * @param condition 查询条件
     * @return 聊天记录数量
     */
    @Select({
            "<script>",
            "SELECT COUNT(*) FROM chatinfo",
            "<where>",
            "  <if test='condition != null'>",
            "    ${condition}",
            "  </if>",
            "</where>",
            "</script>"
    })
    int countByCondition(@Param("condition") String condition);

    /**
     * 根据ID查询聊天记录
     * @param cid 聊天记录ID
     * @return 聊天记录
     */
    @Select("SELECT * FROM chatinfo WHERE cid = #{cid}")
    Chatinfo findById(@Param("cid") Long cid);

    /**
     * 插入聊天记录
     * @param chatinfo 聊天记录
     * @return 影响的行数
     */
    @Insert({
            "INSERT INTO chatinfo(lname, lname2, content, sendtime) ",
            "VALUES(#{lname}, #{lname2}, #{content}, #{sendtime})"
    })
    @Options(useGeneratedKeys = true, keyProperty = "cid")
    int insert(Chatinfo chatinfo);

    /**
     * 删除聊天记录
     * @param cid 聊天记录ID
     * @return 影响的行数
     */
    @Delete("DELETE FROM chatinfo WHERE cid = #{cid}")
    int delete(@Param("cid") Long cid);
    
    /**
     * 获取用户的聊天联系人列表
     * @param account 用户账号
     * @return 聊天联系人列表
     */
    @Select("SELECT DISTINCT lname FROM chatinfo WHERE lname2 = #{account} " +
            "UNION " +
            "SELECT DISTINCT lname2 FROM chatinfo WHERE lname = #{account}")
    List<String> findContactsByAccount(@Param("account") String account);
}
