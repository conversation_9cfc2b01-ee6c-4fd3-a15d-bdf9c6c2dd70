package com.bkty.turtorsystem.mapper;

import com.bkty.turtorsystem.entity.Hometutorinfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 家教资讯Mapper接口
 */
@Mapper
public interface HometutorinfoMapper {

    /**
     * 根据条件查询家教资讯列表
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @param hometutorinfo 查询条件
     * @return 家教资讯列表
     */
    @Select({
            "<script>",
            "SELECT * FROM hometutorinfo",
            "<where>",
            "  <if test='hometutorinfo != null and hometutorinfo.title != null and hometutorinfo.title != \"\"'>",
            "    AND title LIKE CONCAT('%', #{hometutorinfo.title}, '%')",
            "  </if>",
            "</where>",
            "ORDER BY publishtime DESC",
            "LIMIT #{offset}, #{pageSize}",
            "</script>"
    })
    List<Hometutorinfo> findByCondition(@Param("offset") int offset, @Param("pageSize") int pageSize, 
                                        @Param("hometutorinfo") Hometutorinfo hometutorinfo);

    /**
     * 统计符合条件的家教资讯总数
     * @param hometutorinfo 查询条件
     * @return 总记录数
     */
    @Select({
            "<script>",
            "SELECT COUNT(*) FROM hometutorinfo",
            "<where>",
            "  <if test='hometutorinfo != null and hometutorinfo.title != null and hometutorinfo.title != \"\"'>",
            "    AND title LIKE CONCAT('%', #{hometutorinfo.title}, '%')",
            "  </if>",
            "</where>",
            "</script>"
    })
    int countByCondition(@Param("hometutorinfo") Hometutorinfo hometutorinfo);

    /**
     * 根据ID查询家教资讯
     * @param id 家教资讯ID
     * @return 家教资讯
     */
    @Select("SELECT * FROM hometutorinfo WHERE id = #{id}")
    Hometutorinfo findById(@Param("id") Long id);

    /**
     * 插入家教资讯
     * @param hometutorinfo 家教资讯
     * @return 影响的行数
     */
    @Insert({
            "INSERT INTO hometutorinfo(title, pimage, content, publishtime) ",
            "VALUES(#{title}, #{pimage}, #{content}, #{publishtime})"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Hometutorinfo hometutorinfo);

    /**
     * 更新家教资讯
     * @param hometutorinfo 家教资讯
     * @return 影响的行数
     */
    @Update({
            "<script>",
            "UPDATE hometutorinfo",
            "<set>",
            "  <if test='title != null'>title = #{title},</if>",
            "  <if test='pimage != null'>pimage = #{pimage},</if>",
            "  <if test='content != null'>content = #{content},</if>",
            "</set>",
            "WHERE id = #{id}",
            "</script>"
    })
    int update(Hometutorinfo hometutorinfo);

    /**
     * 删除家教资讯
     * @param id 家教资讯ID
     * @return 影响的行数
     */
    @Delete("DELETE FROM hometutorinfo WHERE id = #{id}")
    int delete(@Param("id") Long id);
} 