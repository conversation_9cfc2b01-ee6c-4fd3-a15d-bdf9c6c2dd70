package com.bkty.turtorsystem.mapper;

import com.bkty.turtorsystem.entity.Subjects;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface SubjectsMapper {

    /**
     * 根据ID查询科目
     */
    @Select("SELECT * FROM subjects WHERE subid = #{id}")
    Subjects findById(@Param("id") Long id);

    /**
     * 查询所有科目（分页）
     */
    @Select("SELECT * FROM subjects LIMIT #{offset}, #{limit}")
    List<Subjects> findAll(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 获取总数
     */
    @Select("SELECT COUNT(*) FROM subjects")
    int count();
    
    /**
     * 插入科目
     */
    @Insert("INSERT INTO subjects (subname) VALUES (#{subname})")
    @Options(useGeneratedKeys = true, keyProperty = "subid")
    int insert(Subjects subject);
    
    /**
     * 更新科目
     */
    @Update("UPDATE subjects SET subname = #{subname} WHERE subid = #{subid}")
    int update(Subjects subject);
    
    /**
     * 删除科目
     */
    @Delete("DELETE FROM subjects WHERE subid = #{id}")
    int delete(@Param("id") Long id);
} 