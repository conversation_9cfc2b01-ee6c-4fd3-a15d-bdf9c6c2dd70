package com.bkty.turtorsystem.mapper;

import com.bkty.turtorsystem.entity.Categorys;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CategorysMapper {

    /**
     * 根据ID查询家教类型
     */
    @Select("SELECT * FROM categorys WHERE catid = #{id}")
    Categorys findById(@Param("id") Long id);

    /**
     * 查询所有家教类型（分页）
     */
    @Select("SELECT * FROM categorys LIMIT #{offset}, #{limit}")
    List<Categorys> findAll(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 获取总数
     */
    @Select("SELECT COUNT(*) FROM categorys")
    int count();
    
    /**
     * 插入家教类型
     */
    @Insert("INSERT INTO categorys (catname) VALUES (#{catname})")
    @Options(useGeneratedKeys = true, keyProperty = "catid")
    int insert(Categorys category);
    
    /**
     * 更新家教类型
     */
    @Update("UPDATE categorys SET catname = #{catname} WHERE catid = #{catid}")
    int update(Categorys category);
    
    /**
     * 删除家教类型
     */
    @Delete("DELETE FROM categorys WHERE catid = #{id}")
    int delete(@Param("id") Long id);
} 