package com.bkty.turtorsystem.mapper;

import com.bkty.turtorsystem.entity.Tutors;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 家教数据访问接口
 */
@Mapper
public interface TutorsMapper {

    /**
     * 根据ID(账号)查询家教
     */
    @Select("SELECT t.*, c.catname FROM tutors t LEFT JOIN categorys c ON t.catid = c.catid WHERE t.taccount = #{id}")
    Tutors findById(@Param("id") String id);
    
    /**
     * 根据账号查询家教
     */
    @Select("SELECT t.*, c.catname FROM tutors t LEFT JOIN categorys c ON t.catid = c.catid WHERE t.taccount = #{account}")
    Tutors findByAccount(@Param("account") String account);

    /**
     * 查询所有家教（分页）
     */
    @Select("SELECT t.*, c.catname FROM tutors t LEFT JOIN categorys c ON t.catid = c.catid LIMIT #{offset}, #{limit}")
    List<Tutors> findAll(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 获取总数
     */
    @Select("SELECT COUNT(*) FROM tutors")
    int count();
    
    /**
     * 插入家教
     */
    @Insert("INSERT INTO tutors(taccount, password, tuname, gender, age, phone, email, education, teachingexperience, " +
            "photo, catid, suids, subval, price, introduction, registrationdate, tflag, tflag2) " +
            "VALUES(#{taccount}, #{password}, #{tuname}, #{gender}, #{age}, #{phone}, #{email}, #{education}, " +
            "#{teachingexperience}, #{photo}, #{catid}, #{suids}, #{subval}, #{price}, #{introduction}, " +
            "#{registrationdate}, #{tflag}, #{tflag2})")
    int insert(Tutors tutor);
    
    /**
     * 根据条件查询家教（分页）
     */
    @Select("<script>" +
            "SELECT t.*, c.catname FROM tutors t LEFT JOIN categorys c ON t.catid = c.catid" +
            "<where>" +
            "  <if test='tutor != null'>" +
            "    <if test='tutor.taccount != null and tutor.taccount != \"\"'>" +
            "      AND t.taccount LIKE CONCAT('%', #{tutor.taccount}, '%')" +
            "    </if>" +
            "    <if test='tutor.tuname != null and tutor.tuname != \"\"'>" +
            "      AND t.tuname LIKE CONCAT('%', #{tutor.tuname}, '%')" +
            "    </if>" +
            "    <if test='tutor.phone != null and tutor.phone != \"\"'>" +
            "      AND t.phone LIKE CONCAT('%', #{tutor.phone}, '%')" +
            "    </if>" +
            "    <if test='tutor.tflag != null and tutor.tflag != \"\"'>" +
            "      <choose>" +
            "        <when test='tutor.tflag == \"审核通过\"'>" +
            "          AND t.tflag = '审核通过'" +
            "        </when>" +
            "        <when test='tutor.tflag == \"待审核\"'>" +
            "          AND t.tflag = '待审核'" +
            "        </when>" +
            "        <when test='tutor.tflag == \"审核不通过\"'>" +
            "          AND t.tflag LIKE '审核不通过%' AND t.tflag != '审核通过'" +
            "        </when>" +
            "        <otherwise>" +
            "          AND t.tflag = #{tutor.tflag}" +
            "        </otherwise>" +
            "      </choose>" +
            "    </if>" +
            "    <if test='tutor.catid != null and tutor.catid != \"\" and tutor.catid != 0'>" +
            "      AND t.catid = #{tutor.catid}" +
            "    </if>" +
            "    <if test='tutor.condition != null and tutor.condition != \"\"'>" +
            "      ${tutor.condition}" +
            "    </if>" +
            "  </if>" +
            "</where>" +
            " LIMIT #{offset}, #{limit}" +
            "</script>")
    List<Tutors> findByCondition(@Param("offset") int offset, @Param("limit") int limit, @Param("tutor") Tutors tutor);
    
    /**
     * 根据条件统计家教数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM tutors t" +
            "<where>" +
            "  <if test='tutor != null'>" +
            "    <if test='tutor.taccount != null and tutor.taccount != \"\"'>" +
            "      AND t.taccount LIKE CONCAT('%', #{tutor.taccount}, '%')" +
            "    </if>" +
            "    <if test='tutor.tuname != null and tutor.tuname != \"\"'>" +
            "      AND t.tuname LIKE CONCAT('%', #{tutor.tuname}, '%')" +
            "    </if>" +
            "    <if test='tutor.phone != null and tutor.phone != \"\"'>" +
            "      AND t.phone LIKE CONCAT('%', #{tutor.phone}, '%')" +
            "    </if>" +
            "    <if test='tutor.tflag != null and tutor.tflag != \"\"'>" +
            "      <choose>" +
            "        <when test='tutor.tflag == \"审核通过\"'>" +
            "          AND t.tflag = '审核通过'" +
            "        </when>" +
            "        <when test='tutor.tflag == \"待审核\"'>" +
            "          AND t.tflag = '待审核'" +
            "        </when>" +
            "        <when test='tutor.tflag == \"审核不通过\"'>" +
            "          AND t.tflag LIKE '审核不通过%' AND t.tflag != '审核通过'" +
            "        </when>" +
            "        <otherwise>" +
            "          AND t.tflag = #{tutor.tflag}" +
            "        </otherwise>" +
            "      </choose>" +
            "    </if>" +
            "    <if test='tutor.catid != null and tutor.catid != \"\" and tutor.catid != 0'>" +
            "      AND t.catid = #{tutor.catid}" +
            "    </if>" +
            "    <if test='tutor.condition != null and tutor.condition != \"\"'>" +
            "      ${tutor.condition}" +
            "    </if>" +
            "  </if>" +
            "</where>" +
            "</script>")
    int countByCondition(@Param("tutor") Tutors tutor);
    
    /**
     * 更新家教
     */
    @Update("<script>" +
            "UPDATE tutors" +
            "<set>" +
            "  <if test='password != null and password != \"\"'>password = #{password},</if>" +
            "  <if test='tuname != null and tuname != \"\"'>tuname = #{tuname},</if>" +
            "  <if test='gender != null'>gender = #{gender},</if>" +
            "  <if test='age > 0'>age = #{age},</if>" +
            "  <if test='phone != null and phone != \"\"'>phone = #{phone},</if>" +
            "  <if test='email != null and email != \"\"'>email = #{email},</if>" +
            "  <if test='education != null and education != \"\"'>education = #{education},</if>" +
            "  <if test='teachingexperience != null and teachingexperience != \"\"'>teachingexperience = #{teachingexperience},</if>" +
            "  <if test='photo != null and photo != \"\"'>photo = #{photo},</if>" +
            "  <if test='catid != null'>catid = #{catid},</if>" +
            "  <if test='suids != null and suids != \"\"'>suids = #{suids},</if>" +
            "  <if test='subval != null and subval != \"\"'>subval = #{subval},</if>" +
            "  <if test='price != null and price != \"\"'>price = #{price},</if>" +
            "  <if test='introduction != null and introduction != \"\"'>introduction = #{introduction},</if>" +
            "  <if test='tflag != null and tflag != \"\"'>tflag = #{tflag},</if>" +
            "  <if test='tflag2 != null and tflag2 != \"\"'>tflag2 = #{tflag2},</if>" +
            "</set>" +
            "WHERE taccount = #{taccount}" +
            "</script>")
    int update(Tutors tutor);
    
    /**
     * 删除家教
     */
    @Delete("DELETE FROM tutors WHERE taccount = #{id}")
    int delete(@Param("id") String id);
} 