package com.bkty.turtorsystem.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {

		// 配置外部文件映射
		registry.addResourceHandler("/**")
				.addResourceLocations("classpath:/static/")
		        .addResourceLocations("classpath:/upload/");

        // 添加文件上传目录映射
        registry.addResourceHandler("/uploads/**")
                .addResourceLocations("file:C:/upload/");
	}

    /**
     * 添加跨域配置
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .maxAge(3600);
    }
}
