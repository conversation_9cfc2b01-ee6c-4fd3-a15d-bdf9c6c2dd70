package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Feedback;
import com.bkty.turtorsystem.response.Result;
import com.bkty.turtorsystem.service.IFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Date;

@RestController
@RequestMapping("/api/feedback")
public class FeedbackController {

    @Autowired
    private IFeedbackService feedbackService;

    /**
     * 前台用户提交留言反馈
     * @param feedback 留言信息
     * @return Result<Void>
     */
    @PostMapping("/add")
    public Result<Void> addFeedback(@RequestBody Feedback feedback) {
        // 参数校验
        if (feedback == null) {
            return Result.error("提交信息不能为空");
        }
        if (feedback.getAccount() == null || feedback.getAccount().isEmpty()) {
            return Result.error("用户账号不能为空");
        }
        if (feedback.getUflag() == null || feedback.getUflag().isEmpty()) {
            return Result.error("用户身份不能为空");
        }
        if (feedback.getTitle() == null || feedback.getTitle().isEmpty()) {
            return Result.error("反馈主题不能为空");
        }
        if (feedback.getContent() == null || feedback.getContent().isEmpty()) {
            return Result.error("反馈内容不能为空");
        }

        // 设置默认值
        feedback.setAddtime(new Timestamp(new Date().getTime())); // 设置当前时间
        feedback.setAdminreply(""); // 管理员回复默认为空

        try {
            int result = feedbackService.addFeedback(feedback);
            if (result > 0) {
                return Result.success("留言反馈成功！");
            }
            return Result.error("留言反馈失败！");
        } catch (Exception e) {
            // Log the exception e
            return Result.error("系统错误，请稍后再试！");
        }
    }

    // 后台管理相关接口稍后添加
} 