package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Feedback;
import com.bkty.turtorsystem.response.Result;
import com.bkty.turtorsystem.service.IFeedbackService;
import com.bkty.turtorsystem.vo.PageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Map;

@RestController
@RequestMapping("/api/feedback")
public class FeedbackController {

    @Autowired
    private IFeedbackService feedbackService;

    /**
     * 前台用户提交留言反馈
     * @param feedback 留言信息
     * @return Result<Void>
     */
    @PostMapping("/add")
    public Result<Void> addFeedback(@RequestBody Feedback feedback) {
        // 参数校验
        if (feedback == null) {
            return Result.error("提交信息不能为空");
        }
        if (feedback.getAccount() == null || feedback.getAccount().isEmpty()) {
            return Result.error("用户账号不能为空");
        }
        if (feedback.getUflag() == null || feedback.getUflag().isEmpty()) {
            return Result.error("用户身份不能为空");
        }
        if (feedback.getTitle() == null || feedback.getTitle().isEmpty()) {
            return Result.error("反馈主题不能为空");
        }
        if (feedback.getContent() == null || feedback.getContent().isEmpty()) {
            return Result.error("反馈内容不能为空");
        }

        // 设置默认值
        feedback.setAddtime(new Timestamp(new Date().getTime())); // 设置当前时间
        feedback.setAdminreply(""); // 管理员回复默认为空

        try {
            int result = feedbackService.addFeedback(feedback);
            if (result > 0) {
                return Result.success("留言反馈成功！");
            }
            return Result.error("留言反馈失败！");
        } catch (Exception e) {
            // Log the exception e
            return Result.error("系统错误，请稍后再试！");
        }
    }

    /**
     * 后台管理 - 查询留言反馈列表（分页）
     * @param params 包含分页参数 (currentPage, pageSize) 和可选的查询条件 (feedback)
     *               feedback 对象中可包含 account, title, uflag 等进行筛选
     * @return Result<PageVO<Feedback>>
     */
    @PostMapping("/list")
    public Result<PageVO<Feedback>> listFeedback(@RequestBody Map<String, Object> params) {
        Integer currentPage = params.get("currentPage") != null ? Integer.parseInt(params.get("currentPage").toString()) : 1;
        Integer pageSize = params.get("pageSize") != null ? Integer.parseInt(params.get("pageSize").toString()) : 10;
        
        Feedback queryFeedback = null;
        if (params.containsKey("feedback")) {
            // TODO: Consider using a proper way to map params.get("feedback") to Feedback object
            // For now, assuming it's already a Feedback object or can be easily converted.
            // This might require a custom deserializer or a more structured request body.
            // For simplicity, if 'feedback' is a map, we'd need to manually create Feedback instance.
            // For now, let's assume it's passed in a way that Spring can convert it, or it's null.
            // A safer approach would be to define a specific DTO for search criteria.
            if (params.get("feedback") instanceof Feedback) {
                 queryFeedback = (Feedback) params.get("feedback");
            } else if (params.get("feedback") instanceof Map) {
                // Basic conversion if feedback is passed as a map
                queryFeedback = new Feedback();
                Map<String, String> feedbackMap = (Map<String, String>) params.get("feedback");
                if (feedbackMap.containsKey("account")) queryFeedback.setAccount(feedbackMap.get("account"));
                if (feedbackMap.containsKey("title")) queryFeedback.setTitle(feedbackMap.get("title"));
                if (feedbackMap.containsKey("uflag")) queryFeedback.setUflag(feedbackMap.get("uflag"));
            }
        }

        try {
            PageVO<Feedback> pageVO = feedbackService.listFeedback(queryFeedback, currentPage, pageSize);
            return Result.success(pageVO);
        } catch (Exception e) {
            // Log the exception e
            return Result.error("查询留言列表失败，请稍后再试！");
        }
    }

    /**
     * 后台管理 - 回复留言
     * @param feedback 包含id和adminreply的Feedback对象
     * @return Result<Void>
     */
    @PostMapping("/reply")
    public Result<Void> replyFeedback(@RequestBody Feedback feedback) {
        if (feedback == null || feedback.getId() <= 0) {
            return Result.error("参数错误，请提供有效的留言ID");
        }
        if (feedback.getAdminreply() == null || feedback.getAdminreply().isEmpty()) {
            return Result.error("回复内容不能为空");
        }

        try {
            int result = feedbackService.replyFeedback(feedback.getId(), feedback.getAdminreply());
            if (result > 0) {
                return Result.success("回复成功！");
            }
            return Result.error("回复失败或留言不存在！");
        } catch (Exception e) {
            // Log the exception e
            return Result.error("系统错误，回复失败！");
        }
    }
    
    // 后台管理相关接口稍后添加
} 