package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Tutors;
import com.bkty.turtorsystem.mapper.TutorsMapper;
import com.bkty.turtorsystem.response.Result;
import com.bkty.turtorsystem.response.Response;
import com.bkty.turtorsystem.service.TutorsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 家教控制器
 */
@RestController
@RequestMapping("/api/tutors")
@CrossOrigin
public class TutorsController {
    // 添加日志记录器
    private static final Logger logger = LoggerFactory.getLogger(TutorsController.class);

    @Autowired
    private TutorsService tutorsService;
    
    @Autowired
    private TutorsMapper tutorsMapper;

    /**
     * 家教注册
     * @param tutor 家教信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<Void> register(@RequestBody Tutors tutor) {
        logger.info("收到家教注册请求: {}", tutor.getTaccount());
        return tutorsService.register(tutor);
    }
    
    /**
     * 添加家教（家教注册）
     * @param tutor 家教信息
     * @return 注册结果
     */
    @PostMapping("/add")
    public Map<String, Object> add(@RequestBody Tutors tutor) {
        logger.info("收到家教添加请求，账号: {}, 姓名: {}", tutor.getTaccount(), tutor.getTuname());
        
        Map<String, Object> result = new HashMap<>();
        
        // 检查账号是否已存在
        Tutors existTutor = tutorsMapper.findByAccount(tutor.getTaccount());
        if (existTutor != null) {
            logger.warn("家教注册失败: 账号已存在 {}", tutor.getTaccount());
            result.put("code", 201);
            result.put("msg", "账号已存在，请更换账号");
            return result;
        }
        
        // 设置注册时间
        tutor.setRegistrationdate(new Timestamp(new Date().getTime()));
        
        try {
            // 插入家教信息
            logger.info("准备插入家教数据: {}", tutor.getTaccount());
            int rows = tutorsMapper.insert(tutor);
            if (rows > 0) {
                logger.info("家教注册成功: {}", tutor.getTaccount());
                result.put("code", 200);
                result.put("msg", "注册成功，请等待管理员审核");
            } else {
                logger.error("家教注册失败: 数据库插入失败，影响行数为0");
                result.put("code", 500);
                result.put("msg", "注册失败，请稍后再试");
            }
        } catch (Exception e) {
            logger.error("家教注册异常: ", e);
            result.put("code", 500);
            result.put("msg", "注册失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 家教登录
     * @param tutor 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public Response login(@RequestBody Tutors tutor) {
        logger.info("收到家教登录请求: {}", tutor.getTaccount());
        
        try {
            if (tutor == null) {
                logger.warn("家教登录失败: 家教信息为空");
                return Response.error(201, "家教信息不能为空");
            }
            if (tutor.getTaccount() == null || tutor.getTaccount().equals("")) {
                logger.warn("家教登录失败: 账号为空");
                return Response.error(202, "家教账号不能为空");
            }
            if (tutor.getPassword() == null || tutor.getPassword().equals("")) {
                logger.warn("家教登录失败: 密码为空，账号: {}", tutor.getTaccount());
                return Response.error(203, "家教密码不能为空");
            }
            
            // 根据账号查询家教
            Tutors queryTutor = tutorsMapper.findByAccount(tutor.getTaccount());
            if (queryTutor == null) {
                // 账号不存在
                logger.warn("家教登录失败: 账号不存在 {}", tutor.getTaccount());
                return Response.error(204, "家教账号不存在");
            } else {
                if (queryTutor.getPassword().equals(tutor.getPassword())) {
                    // 密码正确
                    logger.info("家教登录成功: {}", tutor.getTaccount());
                    return Response.success(queryTutor);
                } else {
                    logger.warn("家教登录失败: 密码错误，账号: {}", tutor.getTaccount());
                    return Response.error(205, "家教密码错误");
                }
            }
        } catch (Exception e) {
            logger.error("家教登录异常: ", e);
            return Response.error(500, "系统错误: " + e.getMessage());
        }
    }
} 