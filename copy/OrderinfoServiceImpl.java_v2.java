package com.bkty.turtorsystem.service.impl;

import com.bkty.turtorsystem.entity.Orderinfo;
import com.bkty.turtorsystem.mapper.OrderinfoMapper;
import com.bkty.turtorsystem.service.OrderinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;

/**
 * 订单信息服务实现类
 */
@Service
public class OrderinfoServiceImpl implements OrderinfoService {

    private static final Logger logger = LoggerFactory.getLogger(OrderinfoServiceImpl.class);

    @Autowired
    private OrderinfoMapper orderinfoMapper;

    /**
     * 创建订单
     * @param orderinfo 订单信息对象
     * @return 是否创建成功
     */
    @Override
    @Transactional
    public boolean createOrder(Orderinfo orderinfo) {
        try {
            // 生成订单ID (使用UUID)
            if (orderinfo.getOid() == null || orderinfo.getOid().isEmpty()) {
                orderinfo.setOid(UUID.randomUUID().toString().replace("-", ""));
            }
            
            // 设置提交时间
            if (orderinfo.getSubmittime() == null) {
                orderinfo.setSubmittime(new Timestamp(System.currentTimeMillis()));
            }
            
            // 设置默认状态
            if (orderinfo.getStatus() == null || orderinfo.getStatus().isEmpty()) {
                orderinfo.setStatus("待接单");
            }
            
            // 计算总金额 - 安全处理包装类型
            Double amount = orderinfo.getAmount();
            Double price = orderinfo.getPrice();
            Long hours = orderinfo.getHours();
            
            if ((amount == null || amount == 0) && price != null && hours != null && price > 0 && hours > 0) {
                orderinfo.setAmount(price * hours);
            }
            
            // 插入订单数据
            int result = orderinfoMapper.insert(orderinfo);
            return result > 0;
        } catch (Exception e) {
            logger.error("创建订单失败", e);
            return false;
        }
    }

    /**
     * 分页查询订单列表
     * @param orderinfo 查询条件
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 包含订单列表和总数量的Map
     */
    @Override
    public Map<String, Object> listOrders(Orderinfo orderinfo, Integer currentPage, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 默认值设置
            currentPage = (currentPage == null || currentPage < 1) ? 1 : currentPage;
            pageSize = (pageSize == null || pageSize < 1) ? 10 : pageSize;
            
            // 计算偏移量
            int offset = (currentPage - 1) * pageSize;
            
            // 查询数据
            List<Orderinfo> orders = orderinfoMapper.findByCondition(orderinfo, offset, pageSize);
            long total = orderinfoMapper.countByCondition(orderinfo);
            
            // 构建返回结果
            result.put("resdata", orders);
            result.put("count", total);
            result.put("code", 0);
            result.put("msg", "查询成功");
            
            // 确保即使没有数据，也返回空数组而非null
            if (orders == null) {
                result.put("resdata", Collections.emptyList());
            }
            
            return result;
        } catch (Exception e) {
            logger.error("查询订单列表失败", e);
            result.put("code", 500);
            result.put("msg", "查询订单列表失败: " + e.getMessage());
            result.put("resdata", Collections.emptyList());
            result.put("count", 0);
            return result;
        }
    }

    /**
     * 更新订单状态
     * @param oid 订单ID
     * @param status 新状态
     * @return 是否更新成功
     */
    @Override
    @Transactional
    public boolean updateOrderStatus(String oid, String status) {
        try {
            // 调用Mapper的updateStatus方法更新订单状态
            int result = orderinfoMapper.updateStatus(oid, status);
            return result > 0;
        } catch (Exception e) {
            logger.error("更新订单状态失败", e);
            return false;
        }
    }

    /**
     * 根据订单ID查询订单
     * @param oid 订单ID
     * @return 订单信息
     */
    @Override
    public Orderinfo getOrderById(String oid) {
        try {
            // 构建查询条件
            Orderinfo query = new Orderinfo();
            query.setOid(oid);
            
            // 查询数据
            List<Orderinfo> orders = orderinfoMapper.findByCondition(query, null, null);
            return orders.isEmpty() ? null : orders.get(0);
        } catch (Exception e) {
            logger.error("查询订单详情失败", e);
            return null;
        }
    }
} 