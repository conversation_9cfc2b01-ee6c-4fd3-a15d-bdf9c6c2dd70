package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Subjects;
import com.bkty.turtorsystem.mapper.SubjectsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/subjects")
public class SubjectsController {

    @Autowired
    private SubjectsMapper subjectsMapper;

    /**
     * 获取科目列表
     * @return 科目列表
     */
    @PostMapping("/list")
    public Map<String, Object> list(@RequestParam(defaultValue = "1") int currentPage,
                                    @RequestParam(defaultValue = "1000") int pageSize) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取数据总数
        int total = subjectsMapper.count();
        
        // 计算偏移量
        int offset = (currentPage - 1) * pageSize;
        
        // 查询数据
        List<Subjects> subjects = subjectsMapper.findAll(offset, pageSize);
        
        result.put("code", 200);
        result.put("msg", "获取成功");
        result.put("count", total);
        result.put("resdata", subjects);
        
        return result;
    }
    
    /**
     * 获取单个科目
     */
    @PostMapping("/get")
    public Map<String, Object> get(@RequestParam Long id) {
        Map<String, Object> result = new HashMap<>();
        
        Subjects subject = subjectsMapper.findById(id);
        
        result.put("code", 200);
        result.put("msg", "获取成功");
        result.put("resdata", subject);
        
        return result;
    }
} 