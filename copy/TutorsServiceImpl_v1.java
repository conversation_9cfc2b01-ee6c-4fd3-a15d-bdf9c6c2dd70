package com.bkty.turtorsystem.service.impl;

import com.bkty.turtorsystem.entity.Tutors;
import com.bkty.turtorsystem.mapper.TutorsMapper;
import com.bkty.turtorsystem.response.Result;
import com.bkty.turtorsystem.service.TutorsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 家教服务实现类
 */
@Service
public class TutorsServiceImpl implements TutorsService {

    @Autowired
    private TutorsMapper tutorsMapper;

    /**
     * 家教注册
     * @param tutor 家教对象
     * @return 注册结果
     */
    @Override
    public Result<Void> register(Tu<PERSON> tutor) {
        // 检查账号是否已存在
        Tutors existTutor = tutorsMapper.findByAccount(tutor.getTaccount());
        if (existTutor != null) {
            return Result.error("账号已存在，请更换账号");
        }

        // 设置注册时间
        tutor.setRegistrationdate(new Timestamp(new Date().getTime()));
        
        // 设置家教标识（默认为待审核状态）
        tutor.setTflag("0");  // 0-待审核，1-已审核
        tutor.setTflag2("0"); // 其他标识，默认为0
        
        // 保存家教信息
        int rows = tutorsMapper.insert(tutor);
        if (rows > 0) {
            return Result.success("注册成功，请等待管理员审核");
        } else {
            return Result.error("注册失败，请稍后再试");
        }
    }
} 