package com.bkty.turtorsystem.service.impl;

import com.bkty.turtorsystem.entity.Feedback;
import com.bkty.turtorsystem.mapper.FeedbackMapper;
import com.bkty.turtorsystem.service.IFeedbackService;
import com.bkty.turtorsystem.vo.PageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 留言反馈服务实现类
 */
@Service
public class FeedbackServiceImpl implements IFeedbackService {

    @Autowired
    private FeedbackMapper feedbackMapper;

    @Override
    @Transactional // 添加事务管理
    public int addFeedback(Feedback feedback) {
        // 可以在这里添加更复杂的业务逻辑，例如敏感词过滤等
        return feedbackMapper.insertFeedback(feedback);
    }

    @Override
    public PageVO<Feedback> listFeedback(Feedback feedback, Integer currentPage, Integer pageSize) {
        // 计算分页起始索引
        int offset = (currentPage - 1) * pageSize;

        // 查询当前页数据
        List<Feedback> feedbackList = feedbackMapper.selectFeedbackByCondition(feedback, offset, pageSize);
        
        // 查询总记录数
        Long total = feedbackMapper.countFeedbackByCondition(feedback);

        return new PageVO<>(feedbackList, total);
    }

    // 后台管理相关方法稍后添加
} 