server:
  servlet:
    context-path: /TutoringServicePlatform
  port: 8088
#配置数据源连接信息
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************
    username: root
    password: Wangpen1511*
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

# 文件上传路径配置
file:
  upload:
    path: C:/upload/
    url: /uploads/

#mybatis配置信息
mybatis:
  mapper-locations: classpath:mapper/*.xml  #指定mapper文件的位置
  type-aliases-package: com.bkty.turtorsystem.entity #指定实体类的位置



