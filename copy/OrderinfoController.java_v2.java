package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Orderinfo;
import com.bkty.turtorsystem.service.OrderinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单信息控制器
 */
@RestController
@RequestMapping("/api/orderinfo")
public class OrderinfoController {

    private static final Logger logger = LoggerFactory.getLogger(OrderinfoController.class);

    @Autowired
    private OrderinfoService orderinfoService;

    /**
     * 创建订单
     * @param orderinfo 订单信息对象
     * @return 创建结果
     */
    @PostMapping("/add")
    public Map<String, Object> addOrder(@RequestBody Orderinfo orderinfo) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到创建订单请求: {}", orderinfo);
            
            // 参数校验
            if (orderinfo == null) {
                result.put("code", 201);
                result.put("msg", "订单信息不能为空");
                return result;
            }
            
            if (orderinfo.getAccount() == null || orderinfo.getAccount().isEmpty()) {
                result.put("code", 202);
                result.put("msg", "家长账号不能为空");
                return result;
            }
            
            if (orderinfo.getTaccount() == null || orderinfo.getTaccount().isEmpty()) {
                result.put("code", 203);
                result.put("msg", "家教账号不能为空");
                return result;
            }
            
            if (orderinfo.getPrice() == null || orderinfo.getPrice() <= 0) {
                result.put("code", 204);
                result.put("msg", "收费标准必须大于0");
                return result;
            }
            
            if (orderinfo.getHours() == null || orderinfo.getHours() <= 0) {
                result.put("code", 205);
                result.put("msg", "预约课时必须大于0");
                return result;
            }
            
            // 创建订单
            boolean success = orderinfoService.createOrder(orderinfo);
            
            if (success) {
                result.put("code", 0);
                result.put("msg", "创建订单成功");
                result.put("oid", orderinfo.getOid()); // 返回订单ID
            } else {
                result.put("code", 500);
                result.put("msg", "创建订单失败");
            }
        } catch (Exception e) {
            logger.error("创建订单异常", e);
            result.put("code", 500);
            result.put("msg", "系统错误: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 查询订单列表
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param oid 订单编号（可选）
     * @param account 家长账号（可选）
     * @param taccount 家教账号（可选）
     * @param status 订单状态（可选）
     * @return 订单列表和分页信息
     */
    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> listOrders(
            @RequestParam(value = "currentPage", required = false) Integer currentPage,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "oid", required = false) String oid,
            @RequestParam(value = "account", required = false) String account,
            @RequestParam(value = "taccount", required = false) String taccount,
            @RequestParam(value = "status", required = false) String status) {
        
        logger.info("接收到查询订单列表请求: currentPage={}, pageSize={}, oid={}, account={}, taccount={}, status={}", 
                currentPage, pageSize, oid, account, taccount, status);
        
        // 创建查询条件对象
        Orderinfo orderinfo = new Orderinfo();
        if (oid != null && !oid.isEmpty()) {
            orderinfo.setOid(oid);
        }
        if (account != null && !account.isEmpty()) {
            orderinfo.setAccount(account);
        }
        if (taccount != null && !taccount.isEmpty()) {
            orderinfo.setTaccount(taccount);
        }
        if (status != null && !status.isEmpty()) {
            orderinfo.setStatus(status);
        }
        
        // 调用服务层查询订单列表
        return orderinfoService.listOrders(orderinfo, currentPage, pageSize);
    }

    /**
     * 获取订单详情
     * @param oid 订单ID
     * @return 订单详情
     */
    @RequestMapping(value = "/get", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> getOrder(@RequestParam("id") String oid) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到查询订单详情请求: oid={}", oid);
            
            // 参数校验
            if (oid == null || oid.isEmpty()) {
                result.put("code", 201);
                result.put("msg", "订单ID不能为空");
                return result;
            }
            
            // 查询订单
            Orderinfo orderinfo = orderinfoService.getOrderById(oid);
            
            if (orderinfo != null) {
                result.put("code", 0);
                result.put("msg", "查询成功");
                result.put("data", orderinfo);
            } else {
                result.put("code", 204);
                result.put("msg", "订单不存在");
            }
        } catch (Exception e) {
            logger.error("查询订单详情异常", e);
            result.put("code", 500);
            result.put("msg", "系统错误: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 更新订单状态
     * @param oid 订单ID
     * @param status 新状态
     * @return 更新结果
     */
    @RequestMapping(value = "/updateStatus", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> updateOrderStatus(
            @RequestParam("id") String oid,
            @RequestParam("status") String status) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("接收到更新订单状态请求: oid={}, status={}", oid, status);
            
            // 参数校验
            if (oid == null || oid.isEmpty()) {
                result.put("code", 201);
                result.put("msg", "订单ID不能为空");
                return result;
            }
            
            if (status == null || status.isEmpty()) {
                result.put("code", 202);
                result.put("msg", "订单状态不能为空");
                return result;
            }
            
            // 更新订单状态
            boolean success = orderinfoService.updateOrderStatus(oid, status);
            
            if (success) {
                result.put("code", 0);
                result.put("msg", "更新订单状态成功");
            } else {
                result.put("code", 500);
                result.put("msg", "更新订单状态失败");
            }
        } catch (Exception e) {
            logger.error("更新订单状态异常", e);
            result.put("code", 500);
            result.put("msg", "系统错误: " + e.getMessage());
        }
        
        return result;
    }
} 