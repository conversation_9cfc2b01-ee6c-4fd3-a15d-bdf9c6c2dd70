package com.bkty.turtorsystem.mapper;

import com.bkty.turtorsystem.entity.Feedback;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface FeedbackMapper {

    /**
     * 插入一条留言反馈
     * @param feedback 留言信息
     * @return 影响行数
     */
    @Insert("INSERT INTO feedback (account, uflag, avatar, title, content, addtime, adminreply) " +
            "VALUES (#{account}, #{uflag}, #{avatar}, #{title}, #{content}, #{addtime}, #{adminreply})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertFeedback(Feedback feedback);

    /**
     * 根据条件查询留言反馈列表 (分页)
     * @param feedback 查询条件 (可包含 account, title, uflag)
     * @param offset 分页起始索引
     * @param pageSize 每页大小
     * @return List<Feedback>
     */
    @SelectProvider(type = FeedbackSqlBuilder.class, method = "buildSelectFeedbackByCondition")
    List<Feedback> selectFeedbackByCondition(@Param("feedback") Feedback feedback, 
                                             @Param("offset") Integer offset, 
                                             @Param("pageSize") Integer pageSize);

    /**
     * 根据条件统计留言反馈数量
     * @param feedback 查询条件
     * @return Long 总记录数
     */
    @SelectProvider(type = FeedbackSqlBuilder.class, method = "buildCountFeedbackByCondition")
    Long countFeedbackByCondition(@Param("feedback") Feedback feedback);

    // 后台管理相关方法稍后添加
}

// 动态SQL构造器类
class FeedbackSqlBuilder {
    public String buildSelectFeedbackByCondition(@Param("feedback") Feedback feedback, 
                                                 @Param("offset") Integer offset, 
                                                 @Param("pageSize") Integer pageSize) {
        StringBuilder sql = new StringBuilder("SELECT * FROM feedback WHERE 1=1 ");
        if (feedback != null) {
            if (feedback.getAccount() != null && !feedback.getAccount().isEmpty()) {
                sql.append("AND account LIKE CONCAT('%', #{feedback.account}, '%') ");
            }
            if (feedback.getTitle() != null && !feedback.getTitle().isEmpty()) {
                sql.append("AND title LIKE CONCAT('%', #{feedback.title}, '%') ");
            }
            if (feedback.getUflag() != null && !feedback.getUflag().isEmpty()) {
                sql.append("AND uflag = #{feedback.uflag} ");
            }
            // 可以根据需要添加更多筛选条件，例如按时间范围等
        }
        sql.append("ORDER BY addtime DESC LIMIT #{offset}, #{pageSize}");
        return sql.toString();
    }

    public String buildCountFeedbackByCondition(@Param("feedback") Feedback feedback) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM feedback WHERE 1=1 ");
        if (feedback != null) {
            if (feedback.getAccount() != null && !feedback.getAccount().isEmpty()) {
                sql.append("AND account LIKE CONCAT('%', #{feedback.account}, '%') ");
            }
            if (feedback.getTitle() != null && !feedback.getTitle().isEmpty()) {
                sql.append("AND title LIKE CONCAT('%', #{feedback.title}, '%') ");
            }
            if (feedback.getUflag() != null && !feedback.getUflag().isEmpty()) {
                sql.append("AND uflag = #{feedback.uflag} ");
            }
        }
        return sql.toString();
    }
} 