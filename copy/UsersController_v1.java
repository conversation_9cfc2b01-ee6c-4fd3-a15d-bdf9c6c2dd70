package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Users;
import com.bkty.turtorsystem.mapper.UsersMapper;
import com.bkty.turtorsystem.response.Result;
import com.bkty.turtorsystem.response.Response;
import com.bkty.turtorsystem.service.UsersService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/users")
@CrossOrigin
public class UsersController {
    // 添加日志记录器
    private static final Logger logger = LoggerFactory.getLogger(UsersController.class);

    @Autowired
    private UsersService usersService;
    
    @Autowired
    private UsersMapper usersMapper;

    /**
     * 用户注册
     * @param user 用户信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<Void> register(@RequestBody Users user) {
        logger.info("收到用户注册请求: {}", user.getAccount());
        return usersService.register(user);
    }
    
    /**
     * 添加用户（家长注册）
     * @param user 用户信息
     * @return 注册结果
     */
    @PostMapping("/add")
    public Map<String, Object> add(@RequestBody Users user) {
        logger.info("收到用户添加请求，账号: {}, 姓名: {}", user.getAccount(), user.getUname());
        
        Map<String, Object> result = new HashMap<>();
        
        // 检查账号是否已存在
        Users existUser = usersMapper.findByAccount(user.getAccount());
        if (existUser != null) {
            logger.warn("用户注册失败: 账号已存在 {}", user.getAccount());
            result.put("code", 201);
            result.put("msg", "账号已存在，请更换账号");
            return result;
        }
        
        // 设置注册时间
        user.setRegtime(new Timestamp(new Date().getTime()));
        
        try {
            // 插入用户
            logger.info("准备插入用户数据: {}", user.getAccount());
            int rows = usersMapper.insert(user);
            if (rows > 0) {
                logger.info("用户注册成功: {}", user.getAccount());
                result.put("code", 200);
                result.put("msg", "注册成功，请等待管理员审核");
            } else {
                logger.error("用户注册失败: 数据库插入失败，影响行数为0");
                result.put("code", 500);
                result.put("msg", "注册失败，请稍后再试");
            }
        } catch (Exception e) {
            logger.error("用户注册异常: ", e);
            result.put("code", 500);
            result.put("msg", "注册失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 用户(家长)登录
     * @param user 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public Response login(@RequestBody Users user) {
        logger.info("收到用户登录请求: {}", user.getAccount());
        
        try {
            if (user == null) {
                logger.warn("用户登录失败: 用户信息为空");
                return Response.error(201, "用户信息不能为空");
            }
            if (user.getAccount() == null || user.getAccount().equals("")) {
                logger.warn("用户登录失败: 账号为空");
                return Response.error(202, "用户账号不能为空");
            }
            if (user.getPassword() == null || user.getPassword().equals("")) {
                logger.warn("用户登录失败: 密码为空, 账号: {}", user.getAccount());
                return Response.error(203, "用户密码不能为空");
            }
            
            // 根据账号查询用户
            Users queryUser = usersMapper.findByAccount(user.getAccount());
            if (queryUser == null) {
                // 账号不存在
                logger.warn("用户登录失败: 账号不存在 {}", user.getAccount());
                return Response.error(204, "用户账号不存在");
            } else {
                if (queryUser.getPassword().equals(user.getPassword())) {
                    // 密码正确
                    logger.info("用户登录成功: {}", user.getAccount());
                    return Response.success(queryUser);
                } else {
                    logger.warn("用户登录失败: 密码错误, 账号: {}", user.getAccount());
                    return Response.error(205, "用户密码错误");
                }
            }
        } catch (Exception e) {
            logger.error("用户登录异常: ", e);
            return Response.error(500, "系统错误: " + e.getMessage());
        }
    }
}