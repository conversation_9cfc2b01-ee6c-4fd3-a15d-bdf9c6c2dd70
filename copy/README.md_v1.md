# 家教服务平台系统 (Tutoring Service Platform)

## 项目概述
本系统是一个家教服务平台，为家长和家教老师提供线上匹配、交流和管理服务。系统包括前台用户端和后台管理端两部分。

## 系统架构
- 前端：Vue.js
- 后端：Spring Boot + MyBatis
- 数据库：MySQL

## 功能模块
1. 用户管理
   - 家长注册与登录
   - 家教注册与登录
   - 管理员登录

2. 家长功能
   - 浏览家教信息
   - 预约家教服务
   - 评价反馈
   - 订单管理
   - 个人信息管理

3. 家教功能
   - 发布家教信息
   - 接受预约
   - 查看评价
   - 订单管理
   - 个人信息管理

4. 管理员功能
   - 用户审核
   - 用户管理
   - 订单管理
   - 系统设置

## 数据库结构
### 表结构
1. users表（家长用户表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | account | varchar | 主键，账号 |
   | password | varchar | 密码 |
   | uname | varchar | 用户姓名 |
   | gender | varchar | 孩子性别 |
   | age | int | 孩子年龄 |
   | phone | varchar | 手机号码 |
   | email | varchar | 邮箱 |
   | address | varchar | 地址 |
   | avatar | varchar | 头像路径 |
   | regtime | datetime | 注册时间 |
   | uflag | varchar | 用户状态（审核通过/审核不通过） |

2. tutors表（家教表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | taccount | varchar | 主键，账号 |
   | password | varchar | 密码 |
   | tuname | varchar | 姓名 |
   | gender | varchar | 性别 |
   | age | int | 年龄 |
   | phone | varchar | 手机号码 |
   | email | varchar | 邮箱 |
   | education | varchar | 学历 |
   | teachingexperience | varchar | 教学经验 |
   | photo | varchar | 照片路径 |
   | catid | int | 类别ID |
   | suids | varchar | 科目ID集合 |
   | subval | varchar | 科目值 |
   | price | double | 价格 |
   | introduction | text | 介绍 |
   | registrationdate | datetime | 注册时间 |
   | tflag | varchar | 审核状态 |
   | tflag2 | varchar | 额外状态标识 |

3. admin表（管理员表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | aid | int | 主键ID |
   | aname | varchar | 管理员名称 |
   | loginpassword | varchar | 登录密码 |
   | arole | varchar | 角色 |

### 字段映射说明
- 注意：Users实体类中的`registrationdate`字段对应数据库中的`regtime`字段
- 注意：Tutors实体类中的字段名与数据库表字段一致

### 完整数据库脚本
```sql
-- ----------------------------
-- Chat2DB export data , export time: 2025-02-26 13:08:41
-- ----------------------------
SET FOREIGN_KEY_CHECKS=0;
-- ----------------------------
-- Table structure for table admini5u
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin` (
  `aid` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员id',
  `aname` varchar(50) DEFAULT NULL COMMENT '账号',
  `loginpassword` varchar(50) DEFAULT NULL COMMENT '登录密码',
  `arole` varchar(50) DEFAULT NULL COMMENT '身份',
  PRIMARY KEY (`aid`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of admin
-- ----------------------------
INSERT INTO `admin` (`aid`,`aname`,`loginpassword`,`arole`)  VALUES ('1','admin','1','超级管理员');
INSERT INTO `admin` (`aid`,`aname`,`loginpassword`,`arole`)  VALUES ('2','aaaaaa','123456','管理员');
-- ----------------------------
-- Table structure for table categorys
-- ----------------------------
DROP TABLE IF EXISTS `categorys`;
CREATE TABLE `categorys` (
  `catid` int(11) NOT NULL AUTO_INCREMENT COMMENT '类型id',
  `catname` varchar(100) DEFAULT NULL COMMENT '类型名称',
  PRIMARY KEY (`catid`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of categorys
-- ----------------------------
INSERT INTO `categorys` (`catid`,`catname`)  VALUES ('1','全职');
INSERT INTO `categorys` (`catid`,`catname`)  VALUES ('2','兼职');
-- ----------------------------
-- Table structure for table chatinfo
-- ----------------------------
DROP TABLE IF EXISTS `chatinfo`;
CREATE TABLE `chatinfo` (
  `cid` int(11) NOT NULL AUTO_INCREMENT COMMENT '聊天id',
  `lname` varchar(50) DEFAULT NULL COMMENT '发送人',
  `lname2` varchar(50) DEFAULT NULL COMMENT '接收人',
  `content` text COMMENT '聊天内容',
  `sendtime` datetime DEFAULT NULL COMMENT '发送时间',
  PRIMARY KEY (`cid`)
) ENGINE=MyISAM AUTO_INCREMENT=15 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of chatinfo
-- ----------------------------
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('4','user001','tutor004','你好啊请问在不在','2024-11-30 20:57:45');
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('5','user001','tutor004','想和你聊下天','2024-11-30 21:03:26');
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('6','tutor004','user001','你好啊在的','2024-11-30 21:07:29');
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('7','user001','tutor004','想和你聊天想和你聊天想和你聊天想和你聊天','2024-11-30 21:12:26');
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('8','tutor004','user001','请问有什么事','2024-11-30 21:15:03');
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('9','tutor004','user001','你好还在不你好还在不你好还在不','2024-11-30 21:23:05');
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('10','hehehe','tutor004','你好啊请问在不在','2024-11-30 21:42:29');
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('11','tutor004','hehehe','在的还没休息呢','2024-11-30 21:42:45');
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('12','tutor004','hehehe','请问有什么事','2024-11-30 21:42:49');
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('13','hehehe','tutor004','想预约了','2024-11-30 21:42:59');
INSERT INTO `chatinfo` (`cid`,`lname`,`lname2`,`content`,`sendtime`)  VALUES ('14','tutor004','hehehe','可以的','2024-11-30 21:43:06');
-- ----------------------------
-- Table structure for table feedback
-- ----------------------------
DROP TABLE IF EXISTS `feedback`;
CREATE TABLE `feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '留言id',
  `account` varchar(50) DEFAULT NULL COMMENT '账号',
  `uflag` varchar(50) DEFAULT NULL COMMENT '身份',
  `avatar` varchar(100) DEFAULT NULL COMMENT '头像',
  `title` varchar(50) DEFAULT NULL COMMENT '反馈主题',
  `content` text COMMENT '反馈内容',
  `addtime` datetime DEFAULT NULL COMMENT '反馈时间',
  `adminreply` text COMMENT '管理员回复',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of feedback
-- ----------------------------
INSERT INTO `feedback` (`id`,`account`,`uflag`,`avatar`,`title`,`content`,`addtime`,`adminreply`)  VALUES ('4','user001','家长','upload/**************.jpg','测试一个留言','测试一个留言测试一个留言测试一个留言测试一个留言测试一个留言测试一个留言测试一个留言测试一个留言测试一个留言测试一个留言测试一个留言测试一个留言','2024-11-30 20:43:12','欢迎留言');
INSERT INTO `feedback` (`id`,`account`,`uflag`,`avatar`,`title`,`content`,`addtime`,`adminreply`)  VALUES ('7','tutor004','家教','upload/**************.jpg','再测试一个留言反馈','再测试一个留言反馈再测试一个留言反馈再测试一个留言反馈再测试一个留言反馈','2024-11-30 21:48:42','欢迎留言反馈');
-- ----------------------------
-- Table structure for table hometutorinfo
-- ----------------------------
DROP TABLE IF EXISTS `hometutorinfo`;
CREATE TABLE `hometutorinfo` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '资讯ID',
  `title` varchar(100) DEFAULT NULL COMMENT '资讯标题',
  `pimage` varchar(100) DEFAULT NULL COMMENT '资讯图片',
  `content` text COMMENT '资讯内容',
  `publishtime` datetime DEFAULT NULL COMMENT '发布时间',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of hometutorinfo
-- ----------------------------
INSERT INTO `hometutorinfo` (`id`,`title`,`pimage`,`content`,`publishtime`)  VALUES ('1','如何选择合适的家教','upload/20241130191939.jpg','<p>选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的</p><p>经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。选择家教需要考虑孩子的性格、学习需求以及家教的经验。</p>','2024-11-30 18:37:33');
INSERT INTO `hometutorinfo` (`id`,`title`,`pimage`,`content`,`publishtime`)  VALUES ('2','家教的教学方法对学习的重要性','upload/20241130191956.jpg','<p>教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。</p><p>教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教</p><p>需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。教学方法是影响学习效果的重要因素，家教需要因材施教。</p>','2024-11-30 18:37:33');
INSERT INTO `hometutorinfo` (`id`,`title`,`pimage`,`content`,`publishtime`)  VALUES ('3','暑期家教的好处','upload/20241130192016.jpg','<p>暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预</p><p>习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。暑期家教可以帮助孩子提前预习课程，提升学习效率。</p>','2024-11-30 18:37:33');
-- ----------------------------
-- Table structure for table orderinfo
-- ----------------------------
DROP TABLE IF EXISTS `orderinfo`;
CREATE TABLE `orderinfo` (
  `oid` varchar(50) NOT NULL COMMENT '订单编号',
  `account` varchar(50) DEFAULT NULL COMMENT '家长账号',
  `taccount` varchar(50) DEFAULT NULL COMMENT '家教账号',
  `price` double DEFAULT '0' COMMENT '收费标准/时',
  `hours` int(11) DEFAULT NULL COMMENT '预约课时',
  `amount` double DEFAULT '0' COMMENT '总金额',
  `remarks` text COMMENT '备注说明',
  `submittime` datetime DEFAULT NULL COMMENT '提交时间',
  `status` varchar(20) DEFAULT NULL COMMENT '接单状态',
  PRIMARY KEY (`oid`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of orderinfo
-- ----------------------------
INSERT INTO `orderinfo` (`oid`,`account`,`taccount`,`price`,`hours`,`amount`,`remarks`,`submittime`,`status`)  VALUES ('***************','user001','tutor002','180.0','2','360.0','先本周六上两个课时','2024-11-30 20:48:12','已接单');
INSERT INTO `orderinfo` (`oid`,`account`,`taccount`,`price`,`hours`,`amount`,`remarks`,`submittime`,`status`)  VALUES ('***************','hehehe','tutor004','150.0','2','300.0','这里填写具体的备注','2024-11-30 21:43:21','已接单');
-- ----------------------------
-- Table structure for table subjects
-- ----------------------------
DROP TABLE IF EXISTS `subjects`;
CREATE TABLE `subjects` (
  `subid` int(11) NOT NULL AUTO_INCREMENT COMMENT '科目id',
  `subname` varchar(100) DEFAULT NULL COMMENT '科目名称',
  PRIMARY KEY (`subid`)
) ENGINE=MyISAM AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of subjects
-- ----------------------------
INSERT INTO `subjects` (`subid`,`subname`)  VALUES ('1','数学');
INSERT INTO `subjects` (`subid`,`subname`)  VALUES ('2','英语');
INSERT INTO `subjects` (`subid`,`subname`)  VALUES ('3','语文');
INSERT INTO `subjects` (`subid`,`subname`)  VALUES ('4','物理');
INSERT INTO `subjects` (`subid`,`subname`)  VALUES ('5','化学');
INSERT INTO `subjects` (`subid`,`subname`)  VALUES ('6','生物');
INSERT INTO `subjects` (`subid`,`subname`)  VALUES ('7','历史');
INSERT INTO `subjects` (`subid`,`subname`)  VALUES ('8','地理');
-- ----------------------------
-- Table structure for table tutors
-- ----------------------------
DROP TABLE IF EXISTS `tutors`;
CREATE TABLE `tutors` (
  `taccount` varchar(50) NOT NULL COMMENT '账号',
  `password` varchar(50) DEFAULT NULL COMMENT '登录密码',
  `tuname` varchar(50) DEFAULT NULL COMMENT '姓名',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `email` varchar(50) DEFAULT NULL COMMENT '电子邮箱',
  `education` varchar(20) DEFAULT NULL COMMENT '学历',
  `teachingexperience` varchar(100) DEFAULT NULL COMMENT '教学经验',
  `photo` varchar(100) DEFAULT NULL COMMENT '照片',
  `catid` int(11) DEFAULT NULL COMMENT '家教类型',
  `suids` varchar(100) DEFAULT NULL COMMENT '家教科目id',
  `subval` varchar(200) DEFAULT NULL COMMENT '家教科目',
  `price` double DEFAULT '0' COMMENT '收费标准/时',
  `introduction` text COMMENT '个人介绍',
  `registrationdate` datetime DEFAULT NULL COMMENT '注册时间',
  `tflag` varchar(100) DEFAULT NULL COMMENT '审核状态',
  `tflag2` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`taccount`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of tutors
-- ----------------------------
INSERT INTO `tutors` (`taccount`,`password`,`tuname`,`gender`,`age`,`phone`,`email`,`education`,`teachingexperience`,`photo`,`catid`,`suids`,`subval`,`price`,`introduction`,`registrationdate`,`tflag`,`tflag2`)  VALUES ('tutor001','123456','李明','男','35','***********','<EMAIL>','本科','5年教学经验','upload/**************.jpg','1','1,2,4','数学,英语,物理','200.0','<p>擅长数学和英语教学，有丰富的教学经验。22</p>','2024-11-30 18:37:32','审核通过','线下');
INSERT INTO `tutors` (`taccount`,`password`,`tuname`,`gender`,`age`,`phone`,`email`,`education`,`teachingexperience`,`photo`,`catid`,`suids`,`subval`,`price`,`introduction`,`registrationdate`,`tflag`,`tflag2`)  VALUES ('tutor002','123456','王芳','女','23','***********','<EMAIL>','研究生','3年教学经验','upload/**************.jpg','2','3,5','语文,化学','180.0','<p>专业语文家教，熟悉各类考试技巧。</p>','2024-11-30 18:37:32','审核通过','线下');
INSERT INTO `tutors` (`taccount`,`password`,`tuname`,`gender`,`age`,`phone`,`email`,`education`,`teachingexperience`,`photo`,`catid`,`suids`,`subval`,`price`,`introduction`,`registrationdate`,`tflag`,`tflag2`)  VALUES ('tutor003','123456','张强','男','30','***********','<EMAIL>','硕士','10年教学经验','upload/**************.jpg','1','4,5,1','物理,化学,数学','220.0','<p>重点中学教师，擅长理科教学。</p>','2024-11-30 18:37:32','审核通过','线上');
INSERT INTO `tutors` (`taccount`,`password`,`tuname`,`gender`,`age`,`phone`,`email`,`education`,`teachingexperience`,`photo`,`catid`,`suids`,`subval`,`price`,`introduction`,`registrationdate`,`tflag`,`tflag2`)  VALUES ('tutor004','123456','李丽','男','32','***********','<EMAIL>','本科','6年教学经验','upload/**************.jpg','2','6,7,8,4,3,5','生物,历史,地理,物理,语文,化学','150.0','<p>专注于中学生生物辅导。</p>','2024-11-30 18:37:32','审核通过','线上');
INSERT INTO `tutors` (`taccount`,`password`,`tuname`,`gender`,`age`,`phone`,`email`,`education`,`teachingexperience`,`photo`,`catid`,`suids`,`subval`,`price`,`introduction`,`registrationdate`,`tflag`,`tflag2`)  VALUES ('1','1','2','男','22','***********','<EMAIL>','本科','22','upload/**************.jpg','1','1,4,7','数学,物理,历史','160.0',NULL,'2024-11-30 19:52:51','审核不过：信息虚假',NULL);
-- ----------------------------
-- Table structure for table users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `account` varchar(50) NOT NULL COMMENT '账号',
  `password` varchar(50) DEFAULT NULL COMMENT '密码',
  `uname` varchar(50) DEFAULT NULL COMMENT '姓名',
  `gender` varchar(10) DEFAULT NULL COMMENT '孩子性别',
  `age` int(11) DEFAULT NULL COMMENT '孩子年龄',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '联系地址',
  `avatar` varchar(100) DEFAULT NULL COMMENT '个人头像',
  `regtime` datetime DEFAULT NULL COMMENT '注册时间',
  `uflag` varchar(100) DEFAULT NULL COMMENT '审核状态',
  PRIMARY KEY (`account`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` (`account`,`password`,`uname`,`gender`,`age`,`phone`,`email`,`address`,`avatar`,`regtime`,`uflag`)  VALUES ('user001','123456','赵刚','男','12','***********','<EMAIL>','北京市海淀区','upload/**************.jpg','2024-11-30 18:37:32','审核通过');
INSERT INTO `users` (`account`,`password`,`uname`,`gender`,`age`,`phone`,`email`,`address`,`avatar`,`regtime`,`uflag`)  VALUES ('user002','123456','陈梅','女','14','***********','<EMAIL>','上海市浦东新区','upload/**************.jpg','2024-11-30 18:37:32','审核通过');
INSERT INTO `users` (`account`,`password`,`uname`,`gender`,`age`,`phone`,`email`,`address`,`avatar`,`regtime`,`uflag`)  VALUES ('hehehe','111111','谢广坤','男','12','***********','<EMAIL>','大学路77号','upload/**************.jpg','2024-11-30 21:41:36','审核通过');
SET FOREIGN_KEY_CHECKS=1;
```

## 开发环境
- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+
- Node.js 10+

## 运行说明
1. 确保MySQL数据库已启动并创建名为`Turtorsystem`的数据库
2. 导入上述数据库脚本，建立表结构和初始数据
3. 修改`application.yml`中的数据库连接信息
4. 运行`TurtorSystemApplication.java`启动后端服务
5. 访问`http://localhost:8088/TutoringServicePlatform/dist/index.html`

## API文档
### 用户相关API
1. 家长注册
   - 请求URL: `/TutoringServicePlatform/api/users/register`
   - 方法: POST
   - 参数: 用户信息对象
   - 返回: 注册结果

2. 家教注册
   - 请求URL: `/TutoringServicePlatform/api/tutors/register`
   - 方法: POST
   - 参数: 家教信息对象
   - 返回: 注册结果

3. 文件上传
   - 请求URL: `/TutoringServicePlatform/upload`
   - 方法: POST
   - 参数: MultipartFile文件
   - 返回: 上传结果和文件访问URL

4. 家长登录
   - 请求URL: `/TutoringServicePlatform/api/users/login`
   - 方法: POST
   - 参数: {account: 账号, password: 密码}
   - 返回: 登录结果和家长信息
   
5. 家教登录
   - 请求URL: `/TutoringServicePlatform/api/tutors/login`
   - 方法: POST
   - 参数: {taccount: 账号, password: 密码}
   - 返回: 登录结果和家教信息

6. 管理员登录
   - 请求URL: `/TutoringServicePlatform/api/admin/login`
   - 方法: POST
   - 参数: {aname: 账号, loginpassword: 密码}
   - 返回: 登录结果和管理员信息

## 技术实现细节
### 登录功能实现
1. 家长登录
   - 通过Controller层的`/login`接口接收家长的账号和密码
   - 进行参数校验：检查账号和密码是否为空
   - 通过Mapper层的`findByAccount`方法查询家长信息
   - 验证密码是否正确
   - 返回统一格式的Response对象，包含状态码、消息和家长信息
   
2. 家教登录
   - 通过Controller层的`/login`接口接收家教的账号和密码
   - 进行参数校验：检查账号和密码是否为空
   - 通过Mapper层的`findByAccount`方法查询家教信息
   - 验证密码是否正确
   - 返回统一格式的Response对象，包含状态码、消息和家教信息

3. 错误处理
   - 使用try-catch捕获可能的异常
   - 针对不同的错误情况返回对应的错误码和错误信息
   - 201: 用户/家教信息不能为空
   - 202: 账号不能为空
   - 203: 密码不能为空
   - 204: 账号不存在
   - 205: 密码错误
   - 500: 系统错误

## 开发状态跟踪
| 模块/功能 | 状态 | 负责人 | 完成日期 | 备注 |
|---|---|---|---|---|
| 项目初始化 | 已完成 | AI/用户 | - | - |
| 家长注册模块 | 已完成 | AI/用户 | - | - |
| 家教注册模块 | 已完成 | AI/用户 | - | - |
| 管理员登录功能 | 已完成 | AI/用户 | - | - |
| 家长登录功能 | 已完成 | AI | 当前日期 | 模仿管理员登录实现 |
| 家教登录功能 | 已完成 | AI | 当前日期 | 模仿管理员登录实现 |
| 管理员审核功能 | 未开始 | - | - | - |
| 前端页面优化 | 未开始 | - | - | - |

## 代码检查与问题记录
### 登录功能安全性问题
1. 当前密码是明文存储和比较的，存在安全隐患
2. 可以考虑使用BCrypt等加密方式存储密码
3. 建议添加登录状态保持功能，如JWT令牌或Session管理

### 已修复问题
1. 用户注册和家教注册接口的URL映射错误
   - 问题描述: 控制器中的注册方法与前端访问的URL路径不匹配，导致404和400错误
   - 原因: 控制器中的方法名与URL映射不一致，前端请求`/register`，但后端实际实现在`/add`接口
   - 解决方案: 交换了UsersController和TutorsController中的URL映射注解，使`/register`和`/add`正确映射到对应的处理方法
   - 修复日期: 2025-05-13

2. 文件上传功能问题修复
   - 问题描述: 家教注册页面不显示照片上传功能，上传API请求返回404错误
   - 原因: 前端上传文件的API路径与后端不匹配，请求了错误的URL路径
   - 解决方案: 
     - 修改前端上传文件的URL从`/common/uploadFile`改为`/upload`
     - 在FileController中添加了新的`/upload`端点，支持文件上传
     - 确保了上传接口返回的数据格式符合前端期望
   - 修复日期: 2025-05-13

3. 家长和家教注册时服务器错误问题修复
   - 问题描述: 家长和家教注册页面均显示"服务器错误"，无法完成注册流程
   - 原因分析:
     - 前端HTTP请求中基础URL配置使用localhost而不是127.0.0.1，可能导致某些环境下的跨域问题
     - 前端文件上传功能的API路径与后端接口不匹配，前端使用的是`/common/uploadFile`，而后端实际路径是`/upload`
     - 日志记录不够详细，难以准确定位问题
   - 解决方案:
     - 修改前端HTTP基础URL从`localhost`改为`127.0.0.1`，保证IP地址一致性
     - 统一修改前端文件上传的API路径为`/upload`
     - 在后端控制器中添加详细的日志记录，便于问题追踪
     - 优化FileController，将`/api/common/uploadFile`路径的处理复用`/upload`的逻辑，减少代码重复
   - 修复日期: 2025-05-13

## 项目反思与改进
1. 安全性考虑
   - 密码应该进行加密存储，可以使用Spring Security或其他加密工具
   - 接口应增加权限控制，防止未授权访问

2. 代码优化
   - 可以使用统一异常处理机制，提高代码健壮性
   - 增加日志记录，便于问题排查

3. 功能扩展
   - 可增加短信/邮件验证功能，提高账号安全性
   - 可增加用户信息修改功能
   - 可增加家教搜索、筛选功能 