package com.bkty.turtorsystem.mapper;

import com.bkty.turtorsystem.entity.Orderinfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface OrderinfoMapper {

    /**
     * 插入订单信息
     * @param orderinfo 订单信息对象
     * @return 影响行数
     */
    @Insert("INSERT INTO orderinfo (oid, account, taccount, price, hours, amount, remarks, submittime, status) " +
            "VALUES (#{oid}, #{account}, #{taccount}, #{price}, #{hours}, #{amount}, #{remarks}, #{submittime}, #{status})")
    int insert(Orderinfo orderinfo);

    /**
     * 根据条件查询订单列表 (分页)
     * @param orderinfo 查询条件
     * @param offset 偏移量
     * @param pageSize 页大小
     * @return 订单列表
     */
    @Select("<script>" +
            "SELECT * FROM orderinfo " +
            "WHERE 1=1 " +
            "<if test='orderinfo.oid != null and orderinfo.oid != \"\"'> AND oid = #{orderinfo.oid} </if> " +
            "<if test='orderinfo.account != null and orderinfo.account != \"\"'> AND account = #{orderinfo.account} </if> " +
            "<if test='orderinfo.taccount != null and orderinfo.taccount != \"\"'> AND taccount = #{orderinfo.taccount} </if> " +
            "<if test='orderinfo.status != null and orderinfo.status != \"\"'> AND status = #{orderinfo.status} </if> " +
            "ORDER BY submittime DESC " +
            "<if test='offset != null and pageSize != null'> LIMIT #{pageSize} OFFSET #{offset} </if>" +
            "</script>")
    List<Orderinfo> findByCondition(@Param("orderinfo") Orderinfo orderinfo, @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    /**
     * 根据条件统计订单数量
     * @param orderinfo 查询条件
     * @return 订单数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM orderinfo " +
            "WHERE 1=1 " +
            "<if test='oid != null and oid != \"\"'> AND oid = #{oid} </if> " +
            "<if test='account != null and account != \"\"'> AND account = #{account} </if> " +
            "<if test='taccount != null and taccount != \"\"'> AND taccount = #{taccount} </if> " +
            "<if test='status != null and status != \"\"'> AND status = #{status} </if> " +
            "</script>")
    long countByCondition(Orderinfo orderinfo);

    /**
     * 更新订单状态
     * @param oid 订单ID
     * @param status 新状态
     * @return 影响行数
     */
    @Update("UPDATE orderinfo SET status = #{status} WHERE oid = #{oid}")
    int updateStatus(@Param("oid") String oid, @Param("status") String status);
} 