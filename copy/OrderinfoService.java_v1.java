package com.bkty.turtorsystem.service;

import com.bkty.turtorsystem.entity.Orderinfo;
import java.util.Map;

/**
 * 订单信息服务接口
 */
public interface OrderinfoService {
    
    /**
     * 创建订单
     * @param orderinfo 订单信息对象
     * @return 是否创建成功
     */
    boolean createOrder(Orderinfo orderinfo);
    
    /**
     * 分页查询订单列表
     * @param orderinfo 查询条件
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 包含订单列表和总数量的Map
     */
    Map<String, Object> listOrders(Orderinfo orderinfo, Integer currentPage, Integer pageSize);
    
    /**
     * 更新订单状态
     * @param oid 订单ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateOrderStatus(String oid, String status);
    
    /**
     * 根据订单ID查询订单
     * @param oid 订单ID
     * @return 订单信息
     */
    Orderinfo getOrderById(String oid);
} 