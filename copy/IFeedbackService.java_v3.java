package com.bkty.turtorsystem.service;

import com.bkty.turtorsystem.entity.Feedback;
import com.bkty.turtorsystem.vo.PageVO;

/**
 * 留言反馈服务接口
 */
public interface IFeedbackService {
    /**
     * 添加留言反馈
     * @param feedback 留言信息
     * @return 影响行数
     */
    int addFeedback(Feedback feedback);

    /**
     * 分页查询留言反馈列表
     * @param feedback 查询条件 (可包含 account, title, uflag 等)
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return PageVO<Feedback> 包含列表和总记录数
     */
    PageVO<Feedback> listFeedback(Feedback feedback, Integer currentPage, Integer pageSize);

    // 后台管理相关方法稍后添加
} 