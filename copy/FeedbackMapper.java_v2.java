package com.bkty.turtorsystem.mapper;

import com.bkty.turtorsystem.entity.Feedback;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface FeedbackMapper {

    /**
     * 插入一条留言反馈
     * @param feedback 留言信息
     * @return 影响行数
     */
    @Insert("INSERT INTO feedback (account, uflag, avatar, title, content, addtime, adminreply) " +
            "VALUES (#{account}, #{uflag}, #{avatar}, #{title}, #{content}, #{addtime}, #{adminreply})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertFeedback(Feedback feedback);

    // 后台管理相关方法稍后添加
} 