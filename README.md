# 家教服务平台系统 (Tutoring Service Platform)

## 项目概述
本系统是一个家教服务平台，为家长和家教老师提供线上匹配、交流和管理服务。系统包括前台用户端和后台管理端两部分。

## 系统架构
- 前端：Vue.js
- 后端：Spring Boot + MyBatis
- 数据库：MySQL 8.0
- 构建工具：Maven
- Java版本：JDK 20

## 功能模块
1. 用户管理
   - 家长注册与登录
   - 家教注册与登录
   - 管理员登录

2. 家长功能
   - 浏览家教信息
   - 预约家教服务
   - 评价反馈
   - 订单管理
   - 个人信息管理

3. 家教功能
   - 发布家教信息
   - 接受预约
   - 查看评价
   - 订单管理
   - 个人信息管理

4. 管理员功能
   - 用户审核
   - 用户管理
   - 订单管理
   - 系统设置

## 数据库结构
### 表结构
1. users表（家长用户表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | account | varchar(50) | 主键，账号 |
   | password | varchar(50) | 密码 |
   | uname | varchar(50) | 用户姓名 |
   | gender | varchar(10) | 孩子性别 |
   | age | int | 孩子年龄 |
   | phone | varchar(20) | 手机号码 |
   | email | varchar(100) | 邮箱 |
   | address | varchar(255) | 联系地址 |
   | avatar | varchar(100) | 头像路径 |
   | regtime | datetime | 注册时间 |
   | uflag | varchar(100) | 用户状态（审核通过/审核不通过） |

2. tutors表（家教表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | taccount | varchar(50) | 主键，账号 |
   | password | varchar(50) | 密码 |
   | tuname | varchar(50) | 姓名 |
   | gender | varchar(10) | 性别 |
   | age | int | 年龄 |
   | phone | varchar(20) | 手机号码 |
   | email | varchar(50) | 邮箱 |
   | education | varchar(20) | 学历 |
   | teachingexperience | varchar(100) | 教学经验 |
   | photo | varchar(100) | 照片路径 |
   | catid | int | 类别ID |
   | suids | varchar(100) | 科目ID集合 |
   | subval | varchar(200) | 科目值 |
   | price | double | 价格（每小时） |
   | introduction | text | 个人介绍 |
   | registrationdate | datetime | 注册时间 |
   | tflag | varchar(100) | 审核状态 |
   | tflag2 | varchar(50) | 授课方式（线上/线下） |

3. admin表（管理员表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | aid | int | 主键，管理员ID，自增 |
   | aname | varchar(50) | 管理员账号 |
   | loginpassword | varchar(50) | 登录密码 |
   | arole | varchar(50) | 角色（如超级管理员、管理员） |

4. categorys表（家教类型表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | catid | int | 主键，类型ID，自增 |
   | catname | varchar(100) | 类型名称（如全职、兼职） |

5. subjects表（科目表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | subid | int | 主键，科目ID，自增 |
   | subname | varchar(100) | 科目名称（如数学、英语、物理等） |

6. chatinfo表（聊天消息表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | cid | int | 主键，聊天ID，自增 |
   | lname | varchar(50) | 发送人账号 |
   | lname2 | varchar(50) | 接收人账号 |
   | content | text | 聊天内容 |
   | sendtime | datetime | 发送时间 |

7. orderinfo表（订单信息表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | oid | varchar(50) | 主键，订单编号 |
   | account | varchar(50) | 家长账号 |
   | taccount | varchar(50) | 家教账号 |
   | price | double | 收费标准（每小时） |
   | hours | int | 预约课时 |
   | amount | double | 总金额 |
   | remarks | text | 备注说明 |
   | submittime | datetime | 提交时间 |
   | status | varchar(20) | 接单状态（如已接单） |

8. feedback表（反馈信息表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | id | int | 主键，留言ID，自增 |
   | account | varchar(50) | 用户账号 |
   | uflag | varchar(50) | 用户身份（如家长、家教） |
   | avatar | varchar(100) | 头像路径 |
   | title | varchar(50) | 反馈主题 |
   | content | text | 反馈内容 |
   | addtime | datetime | 反馈时间 |
   | adminreply | text | 管理员回复 |

9. hometutorinfo表（家教资讯表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | id | int | 主键，资讯ID，自增 |
   | title | varchar(100) | 资讯标题 |
   | pimage | varchar(100) | 资讯图片路径 |
   | content | text | 资讯内容 |
   | publishtime | datetime | 发布时间 |

### 字段映射说明
- 注意：Users实体类中的`registrationdate`字段对应数据库中的`regtime`字段
- 注意：tutors表中的`tflag2`字段表示授课方式（线上/线下）

## 环境配置
1. 数据库配置：MySQL 8.0
2. 服务器端口：8088
3. 项目上下文路径：/TutoringServicePlatform
4. 文件上传配置：
   - 最大文件大小：10MB
   - 上传路径：需在application.yml中配置

## 部署说明
1. 克隆项目到本地
2. 配置数据库连接信息（修改application.yml）
3. 创建对应的数据库和表结构
4. 配置文件上传路径
5. 使用Maven构建：`mvn clean package`
6. 运行生成的jar包：`java -jar TurtorSystem-0.0.1-SNAPSHOT.jar`
7. 访问系统：http://localhost:8088/TutoringServicePlatform/dist/index.html

## API文档
### 用户相关API
1. 家长注册
   - 请求URL: `/TutoringServicePlatform/api/users/register`
   - 方法: POST
   - 参数: 用户信息对象
   - 返回: 注册结果（Result<Void>类型）

   或者:
   - 请求URL: `/TutoringServicePlatform/api/users/add`
   - 方法: POST
   - 参数: 用户信息对象
   - 返回: 包含code、msg的Map格式响应

2. 家教注册
   - 请求URL: `/TutoringServicePlatform/api/tutors/register`
   - 方法: POST
   - 参数: 家教信息对象
   - 返回: 注册结果（Result<Void>类型）

   或者:
   - 请求URL: `/TutoringServicePlatform/api/tutors/add`
   - 方法: POST
   - 参数: 家教信息对象
   - 返回: 包含code、msg的Map格式响应

3. 文件上传
   - 请求URL: `/TutoringServicePlatform/upload`
   - 方法: POST
   - 参数: MultipartFile文件
   - 返回: 上传结果和文件访问URL

4. 家长登录
   - 请求URL: `/TutoringServicePlatform/api/users/login`
   - 方法: POST
   - 参数: {account: 账号, password: 密码}
   - 返回: 登录结果和家长信息

5. 家教登录
   - 请求URL: `/TutoringServicePlatform/api/tutors/login`
   - 方法: POST
   - 参数: {taccount: 账号, password: 密码}
   - 返回: 登录结果和家教信息

6. 管理员登录
   - 请求URL: `/TutoringServicePlatform/api/admin/login`
   - 方法: POST
   - 参数: {aname: 账号, loginpassword: 密码}
   - 返回: 登录结果和管理员信息

### 家教相关API
1. 家教列表查询 (通用接口)
   - 请求URL: `/TutoringServicePlatform/api/tutors/list`
   - 方法: POST
   - 参数:
     - `currentPage`: 当前页码 (可选, 默认 1)
     - `pageSize`: 每页大小 (可选, 默认 10)
     - `tutor`: 查询条件对象 (RequestBody, 可选, 支持taccount, tuname, phone, tflag, catid, gender, tflag2筛选)
     - `listType`: 列表类型 (RequestParam, 可选, String类型):
       - 不传或为空: 普通家教列表查询。
       - `"latest"`: 查询最新家教列表 (按注册时间降序排序)。
       - `"hot"`: 查询热门家教列表 (有过"已接单"记录的家教，按注册时间降序排序)。
   - 返回: 家教列表和分页信息

2. 获取单个家教信息
   - 请求URL: `/TutoringServicePlatform/api/tutors/get`
   - 方法: POST
   - 参数: id: 家教账号
   - 返回: 家教详细信息

3. 更新家教信息
   - 请求URL: `/TutoringServicePlatform/api/tutors/update`
   - 方法: POST
   - 参数:
     - tutor: 家教信息对象
     - account: 家教账号(可选)
   - 返回: 更新结果

4. 删除家教
   - 请求URL: `/TutoringServicePlatform/api/tutors/del`
   - 方法: POST
   - 参数: id: 家教账号
   - 返回: 删除结果

### 管理员相关API
1. 查询所有家长用户
   - 请求URL: `/TutoringServicePlatform/api/users/list`
   - 方法: POST
   - 参数:
     - currentPage: 当前页码
     - pageSize: 每页大小
     - user: 查询条件对象（可选，其中uflag字段可用于按审核状态筛选）
   - 返回: 家长用户列表和分页信息

2. 审核家长用户
   - 请求URL: `/TutoringServicePlatform/api/users/update`
   - 方法: POST
   - 参数:
     - 用户对象，其中包含account和uflag字段（例如"审核通过"、"审核不通过"）
     - account: 家长账号(可选，URL参数)
   - 返回: 审核结果

### 类别和科目API
1. 添加家教类型
   - 请求URL: `/TutoringServicePlatform/api/categorys/add`
   - 方法: POST
   - 参数: {catname: 类型名称}
   - 返回: 添加结果

2. 查询所有家教类型
   - 请求URL: `/TutoringServicePlatform/api/categorys/list`
   - 方法: GET/POST
   - 参数: 无
   - 返回: 家教类型列表

3. 添加科目
   - 请求URL: `/TutoringServicePlatform/api/subjects/add`
   - 方法: POST
   - 参数: {subname: 科目名称}
   - 返回: 添加结果

4. 查询所有科目
   - 请求URL: `/TutoringServicePlatform/api/subjects/list`
   - 方法: GET/POST
   - 参数: 无
   - 返回: 科目列表

### 订单相关API
1. 创建订单
   - 请求URL: `/TutoringServicePlatform/api/orderinfo/add`
   - 方法: POST
   - 参数: 订单信息对象
   - 返回: 创建结果

2. 查询订单列表
   - 请求URL: `/TutoringServicePlatform/api/orderinfo/list`
   - 方法: POST
   - 参数:
     - currentPage: 当前页码
     - pageSize: 每页大小
     - order: 查询条件对象 (支持按账号(account)、家教账号(taccount)、订单状态(status)等进行筛选)
   - 返回: 订单列表和分页信息

3. 获取订单详情
   - 请求URL: `/TutoringServicePlatform/api/orderinfo/get`
   - 方法: POST
   - 参数: id: 订单ID
   - 返回: 订单详细信息

4. 更新订单状态
   - 请求URL: `/TutoringServicePlatform/api/orderinfo/updateStatus`
   - 方法: POST
   - 参数:
     - id: 订单ID
     - status: 新状态（如"待接单"、"已接单"、"已完成"等）
   - 返回: 更新结果

### 聊天信息API
1. **功能描述**
   - 实现了聊天信息模块的基本功能，包括发送消息、获取聊天记录列表、获取单个聊天记录和删除聊天记录。
   - 支持按发送人、接收人等条件进行查询。
   - 所有接口均实现了参数校验，确保数据的完整性和有效性。
   - 采用统一的响应格式，使前端能够一致地处理不同接口返回的数据。

2. **实现方案**
   - **三层架构**：遵循Controller -> Service -> Mapper的标准三层架构。
     - `ChatinfoController`：负责接收前端请求、参数校验和响应返回。
     - `ChatinfoService` (`ChatinfoServiceImpl`)：封装业务逻辑，处理聊天记录的添加、查询和删除等操作。
     - `ChatinfoMapper`：通过MyBatis注解方式直接定义SQL，实现数据库操作。
   - **API路径设计**：
     - 控制器使用 `@RequestMapping("/chatinfo")` 注解，而不是 `/api/chatinfo`
     - 前端 base URL 已包含 `/api` 路径，因此后端控制器不再重复包含
   - **分页实现**：
     - 采用自定义分页逻辑，通过offset和limit参数控制分页
     - 统一使用Map对象封装分页结果
   - **参数校验**：
     - 在Controller层对所有请求参数进行严格校验
     - 返回明确的错误码和错误信息，便于前端处理异常情况

3. **主要功能点**
   - **发送聊天消息**：
     - 接口：`/api/chatinfo/add`
     - 方法：POST
     - 参数：Chatinfo对象（包含lname, lname2, content等字段）
     - 实现：设置当前时间作为sendtime，调用Service层addChatinfo方法保存数据
   - **获取聊天记录列表**：
     - 接口：`/api/chatinfo/list`
     - 方法：POST
     - 参数：condition（查询条件），currentPage（当前页码），pageSize（每页记录数）
     - 实现：调用Service层listChatinfo方法查询数据
   - **获取聊天联系人列表**：
     - 接口：`/api/chatinfo/contacts`
     - 方法：POST
     - 参数：account（用户账号）
     - 实现：调用Service层getContactsByAccount方法获取联系人列表

### 留言反馈API
1. **功能描述**
   - 实现了留言反馈模块的完整功能，包括用户提交留言、管理员查看留言列表、查看留言详情和回复留言。
   - 支持按账号、标题和用户身份等条件进行查询。
   - 所有接口均实现了参数校验，确保数据的完整性和有效性。
   - 采用统一的响应格式，使前端能够一致地处理不同接口返回的数据。

2. **实现方案**
   - **三层架构**：遵循Controller -> Service -> Mapper的标准三层架构。
     - `FeedbackController`：负责接收前端请求、参数校验和响应返回。
     - `IFeedbackService` (`FeedbackServiceImpl`)：封装业务逻辑，处理留言的添加、查询和回复等操作。
     - `FeedbackMapper`：通过MyBatis注解方式直接定义SQL，实现数据库操作。
   - **分页实现**：
     - 采用PageHelper进行分页处理，简化分页查询代码
     - 统一使用PageInfo对象封装分页结果
   - **参数校验**：
     - 在Controller层对所有请求参数进行严格校验
     - 返回明确的错误码和错误信息，便于前端处理异常情况

3. **主要功能点**
   - **用户提交留言**：
     - 接口：`/api/feedback/add`
     - 方法：POST
     - 参数：Feedback对象（包含account, uflag, title, content等字段）
     - 实现：设置当前时间作为addtime，将adminreply设为空字符串，调用Service层addFeedback方法保存数据
   - **管理员查看留言列表**：
     - 接口：`/api/feedback/list`
     - 方法：POST
     - 参数：Feedback对象（作为查询条件），currentPage（当前页码），pageSize（每页记录数）
     - 实现：使用PageHelper进行分页，调用Service层queryFeedbackList方法查询数据
   - **管理员查看留言详情**：
     - 接口：`/api/feedback/get`
     - 方法：GET/POST
     - 参数：id（留言ID）
     - 实现：调用Service层getFeedbackById方法获取单个留言详情
   - **管理员回复留言**：
     - 接口：`/api/feedback/reply`
     - 方法：POST
     - 参数：Feedback对象（必须包含id和adminreply）
     - 实现：调用Service层updateFeedbackReply方法更新留言的回复内容

4. **核心特性**
   - **事务管理**：在Service层方法上添加`@Transactional`注解，确保数据一致性。
   - **统一响应格式**：所有接口都使用统一的Response对象封装返回结果，包含状态码、消息和数据。
   - **错误处理**：全面的异常捕获和处理，确保系统在各种异常情况下能够返回友好的错误信息。
   - **SQL优化**：在查询中使用动态SQL，根据实际查询条件构建最优SQL语句，提高查询效率。

5. **接口文档**
   - **添加留言**
     ```
     URL: /api/feedback/add
     Method: POST
     Request Body: {
       "account": "user123",
       "uflag": "家长",
       "title": "关于课程安排的建议",
       "content": "希望能够增加周末的课程时间"
     }
     Response: {
       "code": 200,
       "msg": "操作成功",
       "resdata": "提交反馈成功"
     }
     ```
   - **查询留言列表**
     ```
     URL: /api/feedback/list
     Method: POST
     Request Body: {
       "currentPage": 1,
       "pageSize": 10,
       "feedback": {
         "uflag": "家长",
         "title": "建议"
       }
     }
     Response: {
       "code": 200,
       "msg": "操作成功",
       "resdata": [
         {
           "id": 1,
           "account": "user123",
           "uflag": "家长",
           "title": "关于课程安排的建议",
           "content": "希望能够增加周末的课程时间",
           "addtime": "2023-11-20 10:30:00",
           "adminreply": "已收到您的建议，我们会认真考虑"
         }
       ],
       "count": 1,
       "totalPage": 1
     }
     ```
   - **获取留言详情**
     ```
     URL: /api/feedback/get?id=1
     Method: GET
     Response: {
       "code": 200,
       "msg": "操作成功",
       "resdata": {
         "id": 1,
         "account": "user123",
         "uflag": "家长",
         "title": "关于课程安排的建议",
         "content": "希望能够增加周末的课程时间",
         "addtime": "2023-11-20 10:30:00",
         "adminreply": "已收到您的建议，我们会认真考虑"
       }
     }
     ```
   - **回复留言**
     ```
     URL: /api/feedback/reply
     Method: POST
     Request Body: {
       "id": 1,
       "adminreply": "已收到您的建议，我们会认真考虑"
     }
     Response: {
       "code": 200,
       "msg": "操作成功",
       "resdata": "回复成功"
     }
     ```

## 技术实现细节
### 登录功能实现
1. 家长登录
   - 通过`UsersController`的`/login`接口接收家长的账号和密码
   - 进行参数校验：检查账号和密码是否为空
   - **直接通过`UsersMapper`的`findByAccount`方法查询家长信息**
   - 验证密码是否正确
   - 返回统一格式的`Response`对象，包含状态码、消息和家长信息

2. 家教登录
   - 通过`TutorsController`的`/login`接口接收家教的账号和密码
   - 进行参数校验：检查账号和密码是否为空
   - **直接通过`TutorsMapper`的`findByAccount`方法查询家教信息**
   - 验证密码是否正确
   - 返回统一格式的`Response`对象，包含状态码、消息和家教信息

3. 管理员登录
   - 通过`AdminController`的`/login`接口接收管理员的账号和密码
   - 进行参数校验：检查账号和密码是否为空
   - **通过`AdminService`的`queryAdmin`方法查询管理员信息**
   - 验证密码是否正确
   - 返回统一格式的`Response`对象，包含状态码、消息和管理员信息

4. 错误处理
   - 使用try-catch捕获可能的异常
   - 针对不同的错误情况返回对应的错误码和错误信息
   - 201: 用户/家教/管理员信息不能为空
   - 202: 账号不能为空
   - 203: 密码不能为空
   - 204: 账号不存在
   - 205: 密码错误
   - 500: 系统错误

### 文件上传功能实现
1. 多路径支持策略
   - 通过统一的`handleFileUpload`私有方法处理所有上传请求
   - 支持多个API路径：`/upload`、`/api/common/uploadFile`、`/common/uploadFile`和`/api/upload`
   - 确保前端不同页面使用的不同上传路径都能正确处理

2. 文件存储流程
   - 生成基于时间戳的唯一文件名，确保文件不会被覆盖
   - 统一URL格式，处理Windows和Linux路径差异
   - 返回标准化的响应格式，同时兼容多种前端预期结构

3. 错误处理
   - 详细日志记录上传过程每个步骤
   - 全面的异常捕获和友好的错误提示

### 管理员审核家长功能实现
1. 数据访问层
   - 在`UsersMapper`接口中添加了`findByCondition`、`countByCondition`和`update`等方法
   - 这些方法用于按条件查询用户、获取符合条件的用户数量和更新用户状态
   - 使用MyBatis注解方式实现SQL语句，简化开发

2. 控制器层
   - 在`UsersController`中通过`/list`接口实现了按条件查询家长用户的功能，支持分页
   - 通过`/update`接口实现了审核家长用户（更新用户状态）的功能
   - **这些接口直接调用`UsersMapper`相关方法，而非通过Service层**
   - 使用标准的返回格式，包含状态码、消息、数据和分页信息
   - 添加了详细的日志记录和异常处理

### 家教管理模块实现
1. 功能描述
   - 实现了家教管理模块的完整功能，包括家教列表查询、详情查看、审核和管理。
   - 支持按账号、姓名、手机号码、审核状态、家教类型、性别、授课方式（线上/线下）进行筛选。
   - 提供了最新家教和热门家教（有过"已接单"记录）的查询接口。
   - 系统提供三个不同的管理页面，分别展示"审核通过"、"待审核"和"审核不通过"状态的家教。
   - 管理员可以对家教进行审核、查看详情和删除操作。

2. 实现方案
   - **分层架构**：遵循Controller -> Service -> Mapper的三层架构。
     - `TutorsController`：负责接收前端请求、参数校验和响应返回。
     - `TutorsService` (`TutorsServiceImpl`)：封装核心业务逻辑，如构建查询条件、调用Mapper方法、处理不同类型的家教列表查询（普通、最新、热门）。
     - `TutorsMapper`：负责数据访问，通过MyBatis注解实现动态SQL，支持多条件查询、分页和排序。
   - **后端实现**：
     - `TutorsMapper`接口：
       - `findByCondition`方法：支持多条件（包括性别、授课方式）查询和排序（通过`queryType="latest"`或`tutor.condition`属性）。
       - `countByCondition`方法：配套的计数方法。
       - `findHotTutorsByCondition`方法：通过JOIN `orderinfo`表（`status='已接单'`）查询热门家教。
       - `countHotTutorsByCondition`方法：配套的热门家教计数方法。
     - `TutorsServiceImpl`：
       - `listTutors`方法：根据传入的`queryType`参数（`null`, `"latest"`, `"hot"`）调用相应的Mapper方法，实现不同列表的查询逻辑。
       - 实现了家教注册、更新、删除、获取详情等业务逻辑。
     - `TutorsController`：
       - `/list`接口：调用`tutorsService.listTutors`获取普通家教列表。
       - `/latest`接口：调用`tutorsService.listTutors`并传递`queryType="latest"`获取最新家教列表。
       - `/hot`接口：调用`tutorsService.listTutors`并传递`queryType="hot"`获取热门家教列表。
       - 其他接口（如`/get`, `/update`, `/del`）均调用对应的Service层方法。
   - **数据交互优化**：
     - 确保Response对象正确设置count属性，使前端能获取正确的分页信息。
     - 优化图片URL处理，确保家教照片正确显示。
     - 添加详细的日志记录，便于排查问题。

3. 功能特点
   - 支持多条件搜索：可通过账号、姓名、手机号码、审核状态、家教类型、性别、授课方式（线上/线下）筛选家教。
   - 支持最新家教和热门家教查询。
   - 分页显示：支持大数据量下的分页浏览
   - 灵活的接口设计：同时支持GET和POST请求，提高前端兼容性
   - 完善的图片处理：支持不同格式的图片路径，并提供图片加载失败回退处理

### 订单管理模块实现
1. 功能描述
   - 实现了订单管理模块的完整功能，包括创建订单、查询订单列表、获取订单详情和更新订单状态。
   - 支持按家长账号、家教账号和订单状态等条件进行查询。
   - 所有接口均具备完整的参数校验，确保数据的有效性和完整性。
   - 采用统一的错误码和响应格式，提高前端开发体验。

2. 实现方案
   - **三层架构**：严格遵循 Controller -> Service -> Mapper 的三层架构模式。
     - `OrderInfoController`：负责接收前端请求、参数校验和响应返回。
     - `OrderInfoService` (`OrderInfoServiceImpl`)：封装核心业务逻辑，如订单创建、状态更新和查询等。
     - `OrderInfoMapper`：负责数据访问，通过 MyBatis 注解和 SQL 构建器实现动态查询。
   - **动态SQL构建**：采用 `OrderInfoSqlBuilder` 类结合 MyBatis 的 `@SelectProvider` 注解，实现灵活的多条件查询功能。
   - **业务逻辑处理**：
     - 创建订单时会自动处理订单号生成（UUID格式）、提交时间、默认状态设置和总金额计算等。
     - 支持完善的事务管理，使用 `@Transactional` 注解确保数据一致性。
     - 统一的异常处理，确保系统稳定性和可靠性。

3. 功能特点
   - **自动化订单号生成**：使用 UUID 技术生成全局唯一的订单编号。
   - **智能金额计算**：基于家教的每小时收费标准和预约课时，自动计算订单总金额。
   - **状态管理机制**：支持订单状态流转（如从"待接单"到"已接单"），便于跟踪订单进度。
   - **灵活的查询功能**：支持多条件组合查询，包括按家长账号、家教账号、订单状态等进行筛选。
   - **错误码标准化**：采用统一的错误码体系，使前端能够清晰理解请求处理结果。
   - **分页机制**：优化数据加载性能，支持大量订单数据的高效浏览。

4. 接口增强
   - 在原有的 `/api/orderinfo/add` 和 `/api/orderinfo/list` 接口基础上，新增了：
     - `/api/orderinfo/get`：查询单个订单的详细信息。
     - `/api/orderinfo/updateStatus`：更新订单的状态（如接单、完成等）。
   - 所有接口均采用标准的请求参数和响应格式，确保API使用的一致性体验。

## 开发状态跟踪
| 模块/功能        | 状态       | 负责人 | 计划完成日期 | 实际完成日期 | 关联PR/Commit | 备注与问题追踪                               |
|-----------------|------------|-------|------------|------------|--------------|------------------------------------------|
| 用户登录功能     | 已完成      | AI    | -          | 2023-10-27  | -            | 直接在Controller中调用Mapper实现              |
| 家教登录功能     | 已完成      | AI    | -          | 2023-10-27  | -            | 直接在Controller中调用Mapper实现              |
| 管理员登录功能   | 已完成      | AI    | -          | 2023-10-27  | -            | 通过Service层实现                           |
| 用户注册功能     | 已完成      | AI    | -          | 2023-10-27  | -            | 存在两个API：/register和/add                |
| 家教注册功能     | 已完成      | AI    | -          | 2023-10-27  | -            | 存在两个API：/register和/add                |
| 文件上传功能     | 已完成      | AI    | -          | 2023-10-27  | -            | 支持多路径上传                              |
| 家长审核功能     | 已完成      | AI    | -          | 2023-10-27  | -            | API与文档不一致，直接调用Mapper实现            |
| 家教管理功能     | 已完成      | AI    | -          | 2023-10-27  | -            | 业务逻辑迁移到Service层，支持更多筛选和查询类型 |
| 订单管理功能     | 已完成      | AI    | -          | 2023-10-29  | -            | 标准三层架构实现，支持订单创建、查询和状态更新 |
| 家教查询功能     | 已优化/已增强 | AI    | -          | 2023-10-28  | -            | 支持性别、授课方式筛选；支持最新、热门家教查询；业务逻辑迁移到Service层 |
| 留言反馈功能     | 已完成      | AI    | -          | 2023-11-20  | -            | 实现用户提交、管理员查看列表、管理员回复及查看详情功能       |
| 在线咨询功能     | 已修复      | AI    | -          | 2023-11-25  | -            | 修复API路径问题，使聊天消息能够成功发送                    |
| 订单提交功能     | 已优化      | AI    | -          | 2023-11-25  | -            | 优化订单提交成功提示，添加绿色成功消息                     |
| 家教订单管理     | 已修复      | AI    | -          | 2023-11-26  | -            | 修复家教无法更新订单状态的问题，添加跨域支持和详细日志       |
| 聊天功能增强     | 已优化      | AI    | -          | 2023-11-26  | -            | 增强聊天功能的错误处理和日志记录，添加用户登录状态检查       |

## 代码检查与问题记录
### 2023-11-26 家教无法更新订单状态和聊天功能问题修复

本次修复了家教无法更新订单状态和聊天功能的问题：

1. **家教无法更新订单状态**
   * **问题描述**：家教用户无法更新订单状态（接单或拒绝接单）。
   * **原因分析**：
     1. `OrderinfoController` 缺少 `@CrossOrigin` 注解，导致跨域请求被拒绝。
     2. 前端缺少详细的错误处理和日志记录，难以排查问题。
   * **解决方案**：
     1. 为 `OrderinfoController` 添加 `@CrossOrigin` 注解，允许跨域请求。
     2. 在前端 `Orderinfo_Manage2.vue` 中添加详细的日志记录和错误处理。
   * **代码变更**：
     ```java
     // 修改前
     @RestController
     @RequestMapping("/orderinfo")
     public class OrderinfoController {

     // 修改后
     @RestController
     @RequestMapping("/orderinfo")
     @CrossOrigin(origins = {"http://localhost:8088", "http://127.0.0.1:8088"}, allowCredentials = "true")
     public class OrderinfoController {
     ```
     ```javascript
     // 前端添加详细日志记录和错误处理
     console.log('接单请求参数:', para);
     console.log('接单请求URL:', url);

     request.post(url, para).then((res) => {
       this.listLoading = false;
       console.log('接单响应:', res);

       // 其他代码...
     }).catch(error => {
       this.listLoading = false;
       console.error('接单异常:', error);
       this.$message({
         message: '接单失败，请稍后重试',
         type: 'error',
         offset: 320,
       });
     });
     ```

2. **聊天功能问题**
   * **问题描述**：聊天功能不工作，家长无法发送消息，家教无法查看聊天页面。
   * **原因分析**：
     1. 前端缺少详细的错误处理和日志记录，难以排查问题。
     2. 聊天功能依赖于 sessionStorage 中的 'lname' 和 'sf' 值，但没有检查这些值是否存在。
   * **解决方案**：
     1. 在 `ChatDialog.vue` 中添加详细的日志记录和错误处理。
     2. 添加对 sessionStorage 中 'lname' 和 'sf' 值的检查，并在值不存在时提示用户登录。
   * **代码变更**：
     ```javascript
     // 初始化聊天时检查用户登录状态
     initChat() {
       this.currentUser = sessionStorage.getItem('lname');
       const sf = sessionStorage.getItem('sf');

       console.log('初始化聊天, 当前用户:', this.currentUser, '身份:', sf, '聊天对象:', this.toUser);

       if (!this.currentUser || !sf) {
         console.error('初始化聊天失败: 用户未登录或会话已过期');
         this.$message.error('请先登录');
         return;
       }

       // 其他代码...
     }

     // 发送消息时添加详细日志记录和错误处理
     sendMessage() {
       // 检查当前用户和目标用户是否存在
       if (!this.currentUser) {
         this.$message.error('当前用户未登录或会话已过期');
         console.error('发送消息失败: 当前用户未登录或会话已过期');
         return;
       }

       // 其他代码...

       console.log('发送消息参数:', para);
       console.log('发送消息URL:', base + '/chatinfo/add');

       request.post(base + '/chatinfo/add', para).then((res) => {
         // 处理响应...
       }).catch(error => {
         // 处理错误...
       });
     }
     ```

### 2023-11-25 在线咨询和订单提交功能修复

本次修复了在线咨询和订单提交功能中的两个问题：

1. **在线咨询功能无法发送聊天消息**
   * **问题描述**：用户在使用在线咨询功能时，无法成功发送聊天消息。
   * **原因分析**：后端 `ChatinfoController` 的 API 路径配置为 `/api/chatinfo`，而前端 `http.js` 中的 base 路径已经包含了 `/api`，导致实际请求路径变成了 `/api/api/chatinfo/add`，与后端不匹配。
   * **解决方案**：修改后端 `ChatinfoController` 的 `@RequestMapping` 注解，将路径从 `/api/chatinfo` 改为 `/chatinfo`，使其与前端请求路径匹配。
   * **代码变更**：
     ```java
     // 修改前
     @RequestMapping("/api/chatinfo")
     // 修改后
     @RequestMapping("/chatinfo")
     ```

2. **订单提交成功后显示红色而非绿色提示**
   * **问题描述**：用户成功提交订单后，系统显示红色的提示信息，而不是预期的绿色成功提示。
   * **原因分析**：在 `OrderSubmit.vue` 中，订单提交成功后没有使用 Element UI 的 `$message.success` 方法显示绿色成功提示。
   * **解决方案**：在订单提交成功后，添加 `this.$message.success('订单提交成功')` 代码，显示绿色的成功提示。
   * **代码变更**：
     ```javascript
     // 修改前
     if (res.code === 0 || res.code === 200) {
       // 订单提交成功
       this.submittedOrderId = res.resdata ? res.resdata.oid : '';
       this.successDialogVisible = true;
     } else {
       this.$message.error(res.msg || '订单提交失败');
     }

     // 修改后
     if (res.code === 0 || res.code === 200) {
       // 订单提交成功
       this.submittedOrderId = res.resdata ? res.resdata.oid : '';
       this.successDialogVisible = true;
       // 添加绿色成功提示
       this.$message.success('订单提交成功');
     } else {
       this.$message.error(res.msg || '订单提交失败');
     }
     ```

### 2023-10-28 代码检查与更新摘要

本次主要针对家教查询功能进行了增强和优化，并修复了一些问题：

1.  **架构分层优化 (家教模块)**
    *   **行动**：将`TutorsController`中原先直接调用`TutorsMapper`的逻辑（如列表查询、获取详情、更新、删除）迁移到了`TutorsServiceImpl`中。
    *   **理由**：遵循了更规范的三层架构，提高了代码的可维护性和模块化程度。

2.  **家教列表查询功能增强**
    *   **性别和授课方式筛选**：修复了家教列表筛选中性别和授课方式无效的BUG，在`TutorsMapper`的`findByCondition`和`countByCondition`中添加了对`gender`和`tflag2`字段的筛选逻辑。
    *   **最新家教查询**：
        *   新增接口 `/api/tutors/latest`。
        *   在`TutorsServiceImpl`的`listTutors`方法中，通过`queryType="latest"`参数，在`TutorsMapper.findByCondition`中实现了按`registrationdate`降序排序。
    *   **热门家教查询**：
        *   新增接口 `/api/tutors/hot`。
        *   热门家教定义为：在`orderinfo`表中有`status='已接单'`记录的家教。
        *   在`TutorsMapper`中新增了`findHotTutorsByCondition`和`countHotTutorsByCondition`方法，通过`JOIN orderinfo`实现查询，并按`registrationdate`降序排序。
        *   `TutorsServiceImpl`的`listTutors`方法通过`queryType="hot"`参数调用上述新Mapper方法。

3. **后台家教管理页面家教类型显示问题**
    *   **分析**：检查发现`TutorsMapper`中的查询（如`findByCondition`）已经通过`LEFT JOIN categorys c ON t.catid = c.catid`查询了`c.catname`。理论上后端返回的数据中应包含家教类型名称。
    *   **建议**：若前端仍未显示家教类型名称，请优先检查：
        1.  **数据问题**：确认`tutors`表中的`catid`在`categorys`表中确实存在对应的记录，且`catname`字段有值。
        2.  **前端代码**：确认前端页面是否正确绑定和显示了返回对象中的`catname`字段。

4.  **API文档更新**
    *   已在`README.md`的API文档部分添加了`/api/tutors/latest`和`/api/tutors/hot`接口的说明。
    *   更新了`/api/tutors/list`接口的参数说明，明确支持了新的筛选条件。

### 2023-11-05 订单管理模块数据类型修复

本次修复了订单管理模块中的数据类型问题，解决了前端页面无法正常显示订单数据的问题：

1. **原始类型转换为包装类型**
   * **问题描述**：`Orderinfo`实体类中的`price`、`hours`和`amount`字段使用原始数据类型（`double`、`long`），在与`null`值比较时会导致编译错误。
   * **修复方案**：
     * 将`Orderinfo.java`中的原始数据类型改为对应的包装类型：`double` → `Double`，`long` → `Long`
     * 在`OrderinfoServiceImpl.java`中安全处理可能为null的包装类型字段，特别是在计算总金额时
     * 修改响应数据结构，确保返回给前端的数据结构符合预期，包括使用`resdata`字段名而非`list`

2. **前端错误处理**
   * **问题描述**：前端报错"Cannot read properties of undefined (reading 'length')"，这通常发生在尝试读取undefined对象的属性。
   * **修复方案**：
     * 在`OrderinfoServiceImpl.java`中确保即使在查询结果为空或发生异常时，也返回空数组而不是null
     * 统一使用`resdata`字段名返回数据，与前端预期保持一致

3. **提高代码健壮性**
   * **行动**：添加了更多的null检查和默认值处理，确保系统在各种边缘情况下都能稳定运行。
   * **收益**：降低了系统出错的可能性，提高了用户体验。

这些修改解决了订单管理页面无法加载数据的问题，同时也提高了系统整体的稳定性和可靠性。

### 2023-11-10 订单管理模块功能优化

本次对订单管理模块进行了进一步的功能优化和简化：

1. **删除订单编辑功能**
   * **变更说明**：移除了系统中订单编辑功能，包括前端和后端的相关代码。
   * **变更详情**：
     * 从前端删除了`OrderinfoEdit.vue`文件
     * 从前端路由配置中移除了OrderinfoEdit相关的路由
     * 从后端控制器中移除了相关的编辑接口
     * 从服务层和数据访问层中移除了相关的订单更新方法
   * **变更原因**：业务需求分析表明，订单一旦创建后只需要支持状态更新和查看详情，不需要支持修改订单的核心信息（如价格、课时等），以维护交易的完整性和准确性。

2. **订单管理功能优化**
   * **操作按钮优化**：在订单列表中只保留"详情"和"删除"按钮，移除了"编辑"按钮，使界面更加简洁明了。
   * **订单详情展示完善**：完善了订单详情页面的信息展示，确保所有订单字段都能正确显示。
   * **订单状态流转逻辑优化**：通过`updateStatus`接口，支持订单从"待接单"到"已接单"再到"已完成"的状态流转，而无需修改其他订单信息。

3. **代码质量提升**
   * **数据类型安全处理**：在`Orderinfo`实体类中使用包装类型（`Double`、`Long`）替代原始类型，提高了与null值处理的安全性。
   * **错误处理完善**：在控制器和服务层方法中增强了错误处理机制，确保异常情况下返回友好的错误信息。
   * **日志完善**：添加了更详细的日志记录，便于跟踪和排查问题。

这些优化使得订单管理模块的功能更加聚焦和稳定，同时简化了用户操作流程，提高了系统的可用性和可维护性。

### 2023-11-20 留言反馈功能完整实现

本次完成了留言反馈功能的全部开发工作，主要包括以下内容：

1. **架构优化与标准化**
   * **改进点**：采用标准三层架构（Controller -> Service -> Mapper），遵循业界最佳实践。
   * **变更详情**：
     * 使用PageHelper替代自定义PageVO类实现分页，简化了代码并提高了可维护性
     * 统一使用Response类封装API响应，确保前端能够以一致的方式处理响应数据
     * 在Service层方法上添加@Transactional注解，增强数据完整性保障

2. **功能完善**
   * **新增功能**：添加了获取单个留言详情的接口`/api/feedback/get`，支持查看留言详情和管理员回复
   * **功能增强**：
     * 优化了留言列表查询的SQL语句，修复了查询条件中的语法错误
     * 增强了参数校验逻辑，确保所有接口都能正确处理异常情况
     * 将日期类型从java.sql.Timestamp改为String，便于前后端数据交换

3. **代码质量提升**
   * **代码优化**：
     * 移除了冗余的代码和注释，提高了代码的可读性
     * 统一了命名风格，使代码更加一致和专业
     * 简化了Controller层的异常处理，使其更加清晰和高效
   * **错误处理增强**：
     * 添加了详细的异常捕获和日志记录
     * 统一使用错误码和错误消息，使前端能够更好地理解和处理错误

4. **文档更新**
   * 在README.md中添加了完整的留言反馈功能文档，包括：
     * 功能描述和实现方案
     * 核心功能点和特性
     * 详细的API接口文档，包含请求/响应示例

这些工作使留言反馈模块成为一个功能完整、架构合理、代码质量高的组件，能够满足用户提交反馈和管理员处理反馈的所有需求。

## 开发环境
- JDK 20
- Maven 3.6+
- MySQL 8.0+
- Spring Boot 3.1.5

## 未来计划
- 增加在线支付功能
- 添加评分系统
- 优化搜索算法
- 开发移动端应用