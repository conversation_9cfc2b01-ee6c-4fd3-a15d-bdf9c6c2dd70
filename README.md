# 家教服务平台系统 (Tutoring Service Platform)

## 项目概述
本系统是一个家教服务平台，为家长和家教老师提供线上匹配、交流和管理服务。系统包括前台用户端和后台管理端两部分。

## 系统架构
- 前端：Vue.js
- 后端：Spring Boot + MyBatis
- 数据库：MySQL 8.0
- 构建工具：Maven
- Java版本：JDK 20

## 功能模块
1. 用户管理
   - 家长注册与登录
   - 家教注册与登录
   - 管理员登录

2. 家长功能
   - 浏览家教信息
   - 预约家教服务
   - 评价反馈
   - 订单管理
   - 个人信息管理

3. 家教功能
   - 发布家教信息
   - 接受预约
   - 查看评价
   - 订单管理
   - 个人信息管理

4. 管理员功能
   - 用户审核
   - 用户管理
   - 订单管理
   - 系统设置

## 数据库结构
### 表结构
1. users表（家长用户表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | account | varchar | 主键，账号 |
   | password | varchar | 密码 |
   | uname | varchar | 用户姓名 |
   | gender | varchar | 孩子性别 |
   | age | int | 孩子年龄 |
   | phone | varchar | 手机号码 |
   | email | varchar | 邮箱 |
   | address | varchar | 地址 |
   | avatar | varchar | 头像路径 |
   | regtime | datetime | 注册时间 |
   | uflag | varchar | 用户状态（审核通过/审核不通过） |

2. tutors表（家教表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | taccount | varchar | 主键，账号 |
   | password | varchar | 密码 |
   | tuname | varchar | 姓名 |
   | gender | varchar | 性别 |
   | age | int | 年龄 |
   | phone | varchar | 手机号码 |
   | email | varchar | 邮箱 |
   | education | varchar | 学历 |
   | teachingexperience | varchar | 教学经验 |
   | photo | varchar | 照片路径 |
   | catid | int | 类别ID |
   | suids | varchar | 科目ID集合 |
   | subval | varchar | 科目值 |
   | price | double | 价格 |
   | introduction | text | 介绍 |
   | registrationdate | datetime | 注册时间 |
   | tflag | varchar | 审核状态 |
   | tflag2 | varchar | 额外状态标识 |

3. admin表（管理员表）
   | 字段名 | 类型 | 说明 |
   |-------|------|-----|
   | aid | int | 主键ID |
   | aname | varchar | 管理员名称 |
   | loginpassword | varchar | 登录密码 |
   | arole | varchar | 角色 |

4. 其他表
   - categorys（家教类型表）
   - subjects（科目表）
   - chatinfo（聊天消息表）
   - orderinfo（订单信息表）
   - feedback（反馈信息表）
   - hometutorinfo（家教资讯表）

### 字段映射说明
- 注意：Users实体类中的`registrationdate`字段对应数据库中的`regtime`字段
- 注意：Tutors实体类中的字段名与数据库表字段一致

## 环境配置
1. 数据库配置：MySQL 8.0
2. 服务器端口：8088
3. 项目上下文路径：/TutoringServicePlatform
4. 文件上传配置：
   - 最大文件大小：10MB
   - 上传路径：需在application.yml中配置

## 部署说明
1. 克隆项目到本地
2. 配置数据库连接信息（修改application.yml）
3. 创建对应的数据库和表结构
4. 配置文件上传路径
5. 使用Maven构建：`mvn clean package`
6. 运行生成的jar包：`java -jar TurtorSystem-0.0.1-SNAPSHOT.jar`
7. 访问系统：http://localhost:8088/TutoringServicePlatform/dist/index.html

## API文档
### 用户相关API
1. 家长注册
   - 请求URL: `/TutoringServicePlatform/api/users/register`
   - 方法: POST
   - 参数: 用户信息对象
   - 返回: 注册结果

2. 家教注册
   - 请求URL: `/TutoringServicePlatform/api/tutors/register`
   - 方法: POST
   - 参数: 家教信息对象
   - 返回: 注册结果

3. 文件上传
   - 请求URL: `/TutoringServicePlatform/upload`
   - 方法: POST
   - 参数: MultipartFile文件
   - 返回: 上传结果和文件访问URL

4. 家长登录
   - 请求URL: `/TutoringServicePlatform/api/users/login`
   - 方法: POST
   - 参数: {account: 账号, password: 密码}
   - 返回: 登录结果和家长信息
   
5. 家教登录
   - 请求URL: `/TutoringServicePlatform/api/tutors/login`
   - 方法: POST
   - 参数: {taccount: 账号, password: 密码}
   - 返回: 登录结果和家教信息

6. 管理员登录
   - 请求URL: `/TutoringServicePlatform/api/admin/login`
   - 方法: POST
   - 参数: {aname: 账号, loginpassword: 密码}
   - 返回: 登录结果和管理员信息

### 家教相关API
1. 家教列表查询
   - 请求URL: `/TutoringServicePlatform/api/tutors/list`
   - 方法: POST
   - 参数: 
     - currentPage: 当前页码
     - pageSize: 每页大小
     - tutor: 查询条件对象
   - 返回: 家教列表和分页信息

2. 获取单个家教信息
   - 请求URL: `/TutoringServicePlatform/api/tutors/get`
   - 方法: POST
   - 参数: id: 家教账号
   - 返回: 家教详细信息

3. 更新家教信息
   - 请求URL: `/TutoringServicePlatform/api/tutors/update`
   - 方法: POST
   - 参数: 
     - tutor: 家教信息对象
     - account: 家教账号(可选)
   - 返回: 更新结果

4. 删除家教
   - 请求URL: `/TutoringServicePlatform/api/tutors/del`
   - 方法: POST
   - 参数: id: 家教账号
   - 返回: 删除结果

### 管理员相关API
1. 查询所有家长用户
   - 请求URL: `/TutoringServicePlatform/api/admin/users/all`
   - 方法: GET
   - 参数: 无
   - 返回: 所有家长用户列表

2. 根据审核状态查询家长用户
   - 请求URL: `/TutoringServicePlatform/api/admin/users/status/{uflag}`
   - 方法: GET
   - 参数: uflag (路径参数，表示审核状态，如"审核通过"、"审核不通过"、"0"表示待审核)
   - 返回: 符合条件的家长用户列表

3. 审核家长用户
   - 请求URL: `/TutoringServicePlatform/api/admin/users/audit`
   - 方法: POST
   - 参数: {account: 家长账号, uflag: 审核状态}
   - 返回: 审核结果

### 类别和科目API
1. 添加家教类型
   - 请求URL: `/TutoringServicePlatform/api/categorys/add`
   - 方法: POST
   - 参数: {catname: 类型名称}
   - 返回: 添加结果

2. 查询所有家教类型
   - 请求URL: `/TutoringServicePlatform/api/categorys/list`
   - 方法: GET/POST
   - 参数: 无
   - 返回: 家教类型列表

3. 添加科目
   - 请求URL: `/TutoringServicePlatform/api/subjects/add`
   - 方法: POST
   - 参数: {subname: 科目名称}
   - 返回: 添加结果

4. 查询所有科目
   - 请求URL: `/TutoringServicePlatform/api/subjects/list`
   - 方法: GET/POST
   - 参数: 无
   - 返回: 科目列表

### 订单相关API
1. 创建订单
   - 请求URL: `/TutoringServicePlatform/api/orderinfo/add`
   - 方法: POST
   - 参数: 订单信息对象
   - 返回: 创建结果

2. 查询订单列表
   - 请求URL: `/TutoringServicePlatform/api/orderinfo/list`
   - 方法: POST
   - 参数: 
     - currentPage: 当前页码
     - pageSize: 每页大小
     - order: 查询条件对象
   - 返回: 订单列表和分页信息

## 技术实现细节
### 登录功能实现
1. 家长登录
   - 通过Controller层的`/login`接口接收家长的账号和密码
   - 进行参数校验：检查账号和密码是否为空
   - 通过Mapper层的`findByAccount`方法查询家长信息
   - 验证密码是否正确
   - 返回统一格式的Response对象，包含状态码、消息和家长信息
   
2. 家教登录
   - 通过Controller层的`/login`接口接收家教的账号和密码
   - 进行参数校验：检查账号和密码是否为空
   - 通过Mapper层的`findByAccount`方法查询家教信息
   - 验证密码是否正确
   - 返回统一格式的Response对象，包含状态码、消息和家教信息

3. 错误处理
   - 使用try-catch捕获可能的异常
   - 针对不同的错误情况返回对应的错误码和错误信息
   - 201: 用户/家教信息不能为空
   - 202: 账号不能为空
   - 203: 密码不能为空
   - 204: 账号不存在
   - 205: 密码错误
   - 500: 系统错误

### 文件上传功能实现
1. 多路径支持策略
   - 通过统一的`handleFileUpload`私有方法处理所有上传请求
   - 支持多个API路径：`/upload`、`/api/common/uploadFile`、`/common/uploadFile`和`/api/upload`
   - 确保前端不同页面使用的不同上传路径都能正确处理
   
2. 文件存储流程
   - 生成基于时间戳的唯一文件名，确保文件不会被覆盖
   - 统一URL格式，处理Windows和Linux路径差异
   - 返回标准化的响应格式，同时兼容多种前端预期结构
   
3. 错误处理
   - 详细日志记录上传过程每个步骤
   - 全面的异常捕获和友好的错误提示

### 管理员审核家长功能实现
1. 数据访问层
   - 在`UsersMapper`接口中添加了查询所有用户、根据状态查询用户和更新用户状态的方法
   - 使用MyBatis注解方式实现SQL语句，简化开发

2. 业务逻辑层
   - 在`UsersService`接口中添加了查询所有用户、根据状态查询用户和审核用户的方法
   - 在`UsersServiceImpl`实现类中实现了这些方法
   - 审核用户时，先检查用户是否存在，然后更新用户状态

3. 控制器层
   - 在`AdminController`中添加了查询所有家长用户、根据状态查询家长用户和审核家长用户的接口
   - 使用标准的RESTful API设计风格
   - 添加了详细的日志记录和异常处理

### 家教管理模块实现
1. 功能描述
   - 实现了家教管理模块的完整功能，包括家教列表查询、详情查看、审核和管理
   - 系统提供三个不同的管理页面，分别展示"审核通过"、"待审核"和"审核不通过"状态的家教
   - 管理员可以对家教进行审核、查看详情和删除操作

2. 实现方案
   - 后端实现
     - 创建`TutorsMapper`接口，添加`findByCondition`等方法用于按条件查询家教信息
     - 实现`TutorsService`和`TutorsServiceImpl`，处理家教管理的业务逻辑
     - 开发`TutorsController`，提供多种API接口支持家教信息的增删改查和审核
     - 使用详细的日志记录，便于排查问题

   - 数据交互优化
     - 确保Response对象正确设置count属性，使前端能获取正确的分页信息
     - 优化图片URL处理，确保家教照片正确显示
     - 添加详细的日志记录，便于排查问题

3. 功能特点
   - 支持多条件搜索：可通过账号、姓名、手机号码和家教类型筛选家教
   - 分页显示：支持大数据量下的分页浏览
   - 灵活的接口设计：同时支持GET和POST请求，提高前端兼容性
   - 完善的图片处理：支持不同格式的图片路径，并提供图片加载失败回退处理

## 开发环境
- JDK 20
- Maven 3.6+
- MySQL 8.0+
- Spring Boot 3.1.5

## 未来计划
- 增加在线支付功能
- 添加评分系统
- 优化搜索算法
- 开发移动端应用 